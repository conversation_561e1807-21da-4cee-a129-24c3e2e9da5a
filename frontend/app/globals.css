.main {
  display: flex;
  min-height: 100vh;
  padding: 2rem;
  gap: 2rem;
}

.visualization {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
}

.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.textarea {
  width: 100%;
  height: 16rem;
  padding: 1rem;
  border: 1px solid #ccc;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  font-family: monospace;
  resize: vertical;
}

.button {
  background-color: #3b82f6;
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  align-self: flex-start;
}

.button.active {
  background-color: #0056b3;
}

.button:hover {
  background-color: #2563eb;
}

h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #333;
}

.graph-container {
  flex: 1;
  min-height: 0;
  position: relative;
  margin-bottom: 2rem;
  max-height: calc(100vh - 200px); /* Limit height to viewport minus some space for headers */
}

.force-graph-container {
  width: 100% !important;
  height: 100% !important;
}

.force-graph-container canvas {
  width: 100% !important;
  height: 100% !important;
} 
