/* Minimal React Toastify styles */
.Toastify__toast-container {
  position: fixed;
  z-index: 9999;
  bottom: 1em;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  max-width: 500px;
  box-sizing: border-box;
  color: #fff;
  padding: 4px;
  font-family: sans-serif;
}

.Toastify__toast {
  position: relative;
  min-height: 64px;
  box-sizing: border-box;
  margin-bottom: 1rem;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1), 0 2px 15px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  max-height: 800px;
  overflow: hidden;
  font-family: sans-serif;
  cursor: pointer;
  direction: ltr;
}

.Toastify__toast--error {
  background-color: #E74C3C;
}

.Toastify__toast--success {
  background-color: #07bc0c;
}

.Toastify__toast-body {
  margin: auto 0;
  padding: 6px;
  display: flex;
  align-items: center;
}

.Toastify__close-button {
  color: #fff;
  background: transparent;
  outline: none;
  border: none;
  padding: 0;
  cursor: pointer;
  opacity: 0.7;
  transition: 0.3s ease;
  align-self: flex-start;
  font-size: 16px;
  font-weight: bold;
}

.Toastify__close-button:hover, .Toastify__close-button:focus {
  opacity: 1;
}