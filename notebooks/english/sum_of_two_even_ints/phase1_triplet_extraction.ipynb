{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Phase 1: Extracting Triplets from Informal Proofs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add src to the Python Path in the Notebook"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Read Informal Proves\n", "Read the LaTeX file. This file contains the informal proofs of the theorems in the book."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/latex": ["## Question:\n", "\n", "Prove that the sum of two even integers is always even.\n", "\n", "## Answer:\n", "\n", "Let $a$ and $b$ be two even integers. By definition, an even integer can be written as:\n", "\\begin{equation}\n", "    a = 2m, \\quad b = 2n, \\quad \\text{where } m, n \\text{ are integers}.\n", "\\end{equation}\n", "\n", "The sum of $a$ and $b$ is:\n", "\\begin{equation}\n", "    a + b = 2m + 2n.\n", "\\end{equation}\n", "\n", "Factoring out $2$:\n", "\\begin{equation}\n", "    a + b = 2(m + n).\n", "\\end{equation}\n", "\n", "Since $m + n$ is an integer, we conclude that $a + b$ is even."], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "\n", "# Load LaTeX proof\n", "proof_latex = read_proof(\"../../data/proofs/english/multiple_solutions/sum_of_two_even_ints/proof1.tex\")\n", "\n", "# Find the start and end positions\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "\n", "# Extract the content between \\begin{document} and \\end{document}\n", "informal_proof = proof_latex[start:end].strip()\n", "\n", "# Replace any \\section{...} with ## ...\n", "informal_proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\subsection\\{([^}]+)\\}\", r\"### \\1\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\title\\{([^}]+)\\}\", r\"# \\1\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\maketitle\", \"\", informal_proof)\n", "\n", "# Replace \\begin{itemize} and \\end{itemize} with Markdown-style lists\n", "informal_proof = re.sub(r\"\\\\begin{itemize}\", \"\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\end{itemize}\", \"\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\item\\s+\\*\\*([^:]+):\\*\\*\", r\"- **\\1:**\", informal_proof)\n", "\n", "# Display the LaTeX content\n", "display(Latex(informal_proof))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Extract Triplet proofs from Informal Proofs"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["Triplet(entities=[Entity(id='1', name='a', label='even integer', type='variable'), Entity(id='2', name='b', label='even integer', type='variable'), Entity(id='3', name='m', label='integer', type='variable'), Entity(id='4', name='n', label='integer', type='variable'), Entity(id='5', name='a + b', label='sum of two even integers', type='expression'), Entity(id='6', name='2(m + n)', label='factored sum', type='expression'), Entity(id='7', name='even integer', label='conclusion', type='definition')], relations=[Relation(source='1', target='3', type='defines', name='definition of even integer'), Relation(source='2', target='4', type='defines', name='definition of even integer'), Relation(source='1', target='5', type='contributes to', name='sum of integers'), Relation(source='2', target='5', type='contributes to', name='sum of integers'), Relation(source='5', target='6', type='factored into', name='factoring out 2'), Relation(source='6', target='7', type='concludes', name='sum is even')])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.phase1.extract_triplets import extract_triplets\n", "\n", "# Extract triplets\n", "triplet = extract_triplets(informal_proof)\n", "triplet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Store Triplets into Neo4J DB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Initialize Neo4JUtils\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"fanavaran\", \"fanavaran\"))\n", "\n", "# Clean the database (delete all nodes and relationships)\n", "neo4j.clean_database()\n", "\n", "# Add nodes and relationships with step tracking\n", "for entity in triplet.entities:\n", "    neo4j.create_node(entity)  # Uses the current step (default is 0)\n", "for relation in triplet.relations:\n", "    neo4j.create_relation(relation)  # Uses the current step (default is 0)\n", "\n", "# Increment the step counter for the next set of changes\n", "neo4j.increment_step()\n", "\n", "# Clean the database (delete nodes and relationships with step > 1)\n", "neo4j.clean_database(step=1)\n", "\n", "# Add or modify nodes and relationships in the next step\n", "# Example:\n", "# neo4j.create_node(new_entity)  # This will use the updated step counter (1)\n", "# neo4j.create_relation(new_relation)  # This will use the updated step counter (1)\n", "\n", "# Close the connection\n", "neo4j.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}