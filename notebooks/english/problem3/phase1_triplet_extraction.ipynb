{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Phase 1: Extracting Triplets from Informal Proofs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add src to the Python Path in the Notebook"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Read Informal Proves\n", "Read the LaTeX file. This file contains the informal proofs of the theorems in the book."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/latex": ["## Question:\n", "\n", "THEOREM 3.10: A square system $A X=B$ of linear equations has a unique solution if and only if the matrix $A$ is invertible. In such a case, $A^{-1} B$ is the unique solution of the system.\n", "\n", "## Answer:\n", "\n", "We only prove here that if $A$ is invertible, then $A^{-1} B$ is a unique solution. If $A$ is invertible, then\n", "\n", "$$\n", "    A\\left(A^{-1} B\\right)=\\left(A A^{-1}\\right) B=I B=B\n", "$$\n", "\n", "and hence, $A^{-1} B$ is a solution. Now suppose $v$ is any solution, so $A v=B$. Then\n", "\n", "$$\n", "    v=I v=\\left(A^{-1} A\\right) v=A^{-1}(A v)=A^{-1} B\n", "$$\n", "\n", "Thus, the solution $A^{-1} B$ is unique."], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "\n", "# Load LaTeX proof\n", "proof_latex = read_proof(\"../../data/proofs/english/problem3/proof1.tex\")\n", "\n", "# Find the start and end positions\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "\n", "# Extract the content between \\begin{document} and \\end{document}\n", "informal_proof = proof_latex[start:end].strip()\n", "\n", "\n", "# Replace any \\section{...} with ## ...\n", "informal_proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", informal_proof)\n", "\n", "# Display the LaTeX content\n", "display(Latex(informal_proof))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Extract Triplet proofs from Informal Proofs"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["Triplet(entities=[Entity(id='theorem_3.10', name='Theorem 3.10', label='Theorem', type='Mathematical Statement'), Entity(id='matrix_A', name='Matrix A', label='Matrix', type='Matrix'), Entity(id='matrix_A_inverse', name='Matrix A^{-1}', label='Inverse Matrix', type='Matrix'), Entity(id='matrix_B', name='Matrix B', label='Matrix', type='Matrix'), Entity(id='identity_matrix', name='Identity Matrix I', label='Matrix', type='Matrix'), Entity(id='solution', name='A^{-1} B', label='Solution', type='Expression'), Entity(id='solution_v', name='v', label='Solution', type='Variable')], relations=[Relation(source='theorem_3.10', target='matrix_A', type='conditions', name='A is invertible'), Relation(source='matrix_A', target='solution', type='results_in', name='gives unique solution'), Relation(source='matrix_A_inverse', target='solution', type='results_in', name='provides solution'), Relation(source='solution_v', target='matrix_B', type='equivalence', name='is a solution'), Relation(source='solution', target='solution_v', type='uniqueness', name='is unique solution')])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.phase1.extract_triplets import extract_triplets\n", "\n", "# Extract triplets\n", "triplet = extract_triplets(informal_proof)\n", "triplet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Store Triplets into Neo4J DB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Initialize Neo4JUtils\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"fanavaran\", \"fanavaran\"))\n", "\n", "# Clean the database (delete all nodes and relationships)\n", "neo4j.clean_database()\n", "\n", "# Add nodes and relationships with step tracking\n", "for entity in triplet.entities:\n", "    neo4j.create_node(entity)  # Uses the current step (default is 0)\n", "for relation in triplet.relations:\n", "    neo4j.create_relation(relation)  # Uses the current step (default is 0)\n", "\n", "# Increment the step counter for the next set of changes\n", "neo4j.increment_step()\n", "\n", "# Clean the database (delete nodes and relationships with step > 1)\n", "neo4j.clean_database(step=1)\n", "\n", "# Add or modify nodes and relationships in the next step\n", "# Example:\n", "# neo4j.create_node(new_entity)  # This will use the updated step counter (1)\n", "# neo4j.create_relation(new_relation)  # This will use the updated step counter (1)\n", "\n", "# Close the connection\n", "neo4j.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}