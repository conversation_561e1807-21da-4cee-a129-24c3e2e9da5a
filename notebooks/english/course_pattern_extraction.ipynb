{"cells": [{"cell_type": "markdown", "id": "2a751e2f", "metadata": {}, "source": ["# Course Pattern Extraction and Application\n", "\n", "This notebook implements a two-phase approach for:\n", "1. Extracting graph-based patterns from a course about mathematical induction\n", "2. Using those patterns to construct knowledge graphs for specific proof examples\n", "\n", "---"]}, {"cell_type": "markdown", "id": "9aca23a8", "metadata": {}, "source": ["## Phase 1: Course Pattern Extraction\n", "\n", "First, we'll extract the graph-based pattern from the mathematical induction course."]}, {"cell_type": "code", "execution_count": 4, "id": "8d428c69", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../..\")))"]}, {"cell_type": "code", "execution_count": 5, "id": "eb83149d", "metadata": {}, "outputs": [{"data": {"text/latex": ["\n", "\n", "## What is Mathematical Induction?\n", "Mathematical induction is a powerful proof technique used in mathematics to prove statements that are asserted to be true for all natural numbers. It is especially useful for proving propositions about:\n", "\n", "    \\item Summations and series\n", "    \\item Divisibility properties\n", "    \\item Inequalities\n", "    \\item Combinatorial identities\n", "\n", "\n", "## The Principle of Mathematical Induction\n", "\\begin{theorem}[Principle of Mathematical Induction]\n", "    To prove that a proposition $P(n)$ is true for all natural numbers $n \\geq n_0$, it suffices to:\n", "    \\begin{enumerate}\n", "        \\item \\textbf{Base Case:} Verify $P(n_0)$ is true\n", "        \\item \\textbf{Inductive Step:} Show that if $P(k)$ is true for some arbitrary $k \\geq n_0$ (called the induction hypothesis), then $P(k+1)$ must also be true\n", "    \\end{enumerate}\n", "\\end{theorem}\n", "\n", "## The Domino Analogy\n", "Mathematical induction works like falling dominos:\n", "\n", "    \\item The base case is like knocking over the first domino\n", "    \\item The inductive step ensures each domino will knock over the next one\n", "    \\item Together, these guarantee that all dominos will fall\n", "\n", "\n", "## Key Points to Remember\n", "\n", "    \\item Always verify both the base case and inductive step\n", "    \\item The induction hypothesis is crucial - you must assume $P(k)$ is true\n", "    \\item Mathematical induction proves statements for \\textit{all} natural numbers beyond the base case\n", "    \\item Choose the appropriate base case ($n_0$) for your proposition\n"], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "from src.phase1.extract_triplets import extract_triplets\n", "from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Load the course content\n", "course_latex = read_proof(\"../data/courses/induction/induction.tex\")\n", "\n", "# Extract content between \\begin{document} and \\end{document}\n", "start = course_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = course_latex.find(r\"\\end{document}\")\n", "proof = course_latex[start:end].strip()\n", "\n", "# Convert LaTeX to markdown-like format\n", "proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", proof)\n", "proof = re.sub(r\"\\\\subsection\\{([^}]+)\\}\", r\"### \\1\", proof)\n", "proof = re.sub(r\"\\\\title\\{([^}]+)\\}\", r\"# \\1\", proof)\n", "proof = re.sub(r\"\\\\maketitle\", \"\", proof)\n", "proof = re.sub(r\"\\\\begin{itemize}\", \"\", proof)\n", "proof = re.sub(r\"\\\\end{itemize}\", \"\", proof)\n", "proof = re.sub(r\"\\\\item\\s+\\*\\*([^:]+):\\*\\*\", r\"- **\\1:**\", proof)\n", "\n", "# Display the course content\n", "display(Latex(proof))"]}, {"cell_type": "markdown", "id": "ff16d995", "metadata": {}, "source": ["### Extract Course Pattern\n", "\n", "We'll use a specialized prompt to extract the pattern of mathematical induction from the course content."]}, {"cell_type": "code", "execution_count": 6, "id": "bca4c3d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof and relationships in mathematical content and represent them as knowledge graph triplets.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematic course content in LaTeX format, extract the key steps of the mathematical proof in minimum steps and structure that form the knowledge graph triplets as the pattern of mathematical proofs for this course.\\n\\nFocus on identifying:\\n1. The core steps of mathematical proof\\n2. The relationships between these proof steps\\n3. The typical structure and flow of mathematical proofs of this type\\n4. Key steps and their relationships\\n5. The entities and relationships in the proof\\n6. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof. Please make sure the final graph is a single connected component and have label of the start and end nodes/entities.\\n\\nExtract triplets in the form <Source Step/Entity, Relationship, Target Step/Entity> that represent this pattern.\\n\\nCourse Content:\\n\\n\\n## What is Mathematical Induction?\\nMathematical induction is a powerful proof technique used in mathematics to prove statements that are asserted to be true for all natural numbers. It is especially useful for proving propositions about:\\n\\n    \\\\item Summations and series\\n    \\\\item Divisibility properties\\n    \\\\item Inequalities\\n    \\\\item Combinatorial identities\\n\\n\\n## The Principle of Mathematical Induction\\n\\\\begin{theorem}[Principle of Mathematical Induction]\\n    To prove that a proposition $P(n)$ is true for all natural numbers $n \\\\geq n_0$, it suffices to:\\n    \\\\begin{enumerate}\\n        \\\\item \\\\textbf{Base Case:} Verify $P(n_0)$ is true\\n        \\\\item \\\\textbf{Inductive Step:} Show that if $P(k)$ is true for some arbitrary $k \\\\geq n_0$ (called the induction hypothesis), then $P(k+1)$ must also be true\\n    \\\\end{enumerate}\\n\\\\end{theorem}\\n\\n## The Domino Analogy\\nMathematical induction works like falling dominos:\\n\\n    \\\\item The base case is like knocking over the first domino\\n    \\\\item The inductive step ensures each domino will knock over the next one\\n    \\\\item Together, these guarantee that all dominos will fall\\n\\n\\n## Key Points to Remember\\n\\n    \\\\item Always verify both the base case and inductive step\\n    \\\\item The induction hypothesis is crucial - you must assume $P(k)$ is true\\n    \\\\item Mathematical induction proves statements for \\\\textit{all} natural numbers beyond the base case\\n    \\\\item Choose the appropriate base case ($n_0$) for your proposition\\n\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='1', name='Base Case', label='Base Case Verification', type='step', start=True, end=False), Entity(id='2', name='Inductive Step', label='Inductive Step Verification', type='step', start=False, end=False), Entity(id='3', name='Induction Hypothesis', label='Assumption of P(k)', type='step', start=False, end=False), Entity(id='4', name='Conclusion', label='Conclusion of P(n) for all n', type='step', start=False, end=True)] relations=[Relation(source='1', target='2', type='grounds', name='Base case leads to inductive step'), Relation(source='2', target='3', type='explains', name='Inductive step relies on induction hypothesis'), Relation(source='3', target='4', type='concludes', name='Induction hypothesis leads to conclusion')]\n"]}], "source": ["SYSTEM_PROMPT = \"\"\"You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof and relationships in mathematical content and represent them as knowledge graph triplets.\"\"\"\n", "\n", "COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematic course content in LaTeX format, extract the key steps of the mathematical proof in minimum steps and structure that form the knowledge graph triplets as the pattern of mathematical proofs for this course.\n", "\n", "Focus on identifying:\n", "1. The core steps of mathematical proof\n", "2. The relationships between these proof steps\n", "3. The typical structure and flow of mathematical proofs of this type\n", "4. Key steps and their relationships\n", "5. The entities and relationships in the proof\n", "6. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof. Please make sure the final graph is a single connected component and have label of the start and end nodes/entities.\n", "\n", "Extract triplets in the form <Source Step/Entity, Relationship, Target Step/Entity> that represent this pattern.\n", "\n", "Course Content:\n", "{proof}\n", "\"\"\"\n", "\n", "# Extract the course pattern\n", "course_pattern = extract_triplets(\n", "    proof, \n", "    custom_prompt=COURSE_PATTERN_PROMPT,\n", "    system_message=SYSTEM_PROMPT\n", ")\n", "print(course_pattern)\n", "\n", "# Store the pattern in Neo4j\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"neo4j\", \"password\"))\n", "neo4j.clean_database()\n", "neo4j.store_triplets(course_pattern, \"course_pattern\")"]}, {"cell_type": "markdown", "id": "763d980b", "metadata": {}, "source": ["## Phase 2: Proof Pattern Application\n", "\n", "Now we'll use the extracted pattern to construct knowledge graphs for specific proof examples."]}, {"cell_type": "code", "execution_count": 7, "id": "72d488b5", "metadata": {}, "outputs": [{"data": {"text/latex": ["## Theorem\n", "Proof by Induction: Sum of the First \\( n \\) Natural Numbers\n", "\n", "The sum of the first \\( n \\) natural numbers is:\n", "\\[\n", "    1 + 2 + 3 + \\dots + n = \\frac{n(n+1)}{2}\n", "\\]\n", "\n", "## Proof by Induction\n", "We proceed by mathematical induction.\n", "\n", "### Base Case (\\( n = 1 \\))\n", "\\[\n", "    \\text{LHS} = 1, \\quad \\text{RHS} = \\frac{1(1+1)}{2} = 1\n", "\\]\n", "Since \\(\\text{LHS} = \\text{RHS}\\), the base case holds.\n", "\n", "### Inductive Hypothesis\n", "Assume the statement holds for some \\( k \\geq 1 \\):\n", "\\[\n", "    1 + 2 + \\dots + k = \\frac{k(k+1)}{2}\n", "\\]\n", "\n", "### Inductive Step (\\( n = k + 1 \\))\n", "We must show:\n", "\\[\n", "    1 + 2 + \\dots + k + (k+1) = \\frac{(k+1)(k+2)}{2}\n", "\\]\n", "Starting from the left-hand side:\n", "\\begin{align}\n", "    1 + 2 + \\dots + k + (k+1) & = \\left(1 + 2 + \\dots + k\\right) + (k+1)                              \\\\\n", "                              & = \\frac{k(k+1)}{2} + (k+1) \\quad \\text{(by the inductive hypothesis)} \\\\\n", "                              & = \\frac{k(k+1) + 2(k+1)}{2}                                           \\\\\n", "                              & = \\frac{(k+1)(k + 2)}{2}\n", "\\end{align}\n", "This matches the right-hand side for \\( n = k+1 \\).\n", "\n", "### Conclusion\n", "By the principle of mathematical induction, the formula holds for all \\( n \\geq 1 \\)."], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load a proof example\n", "proof_latex = read_proof(\"../data/proofs/english/sum_of_first_n_ints/proof1.tex\")\n", "\n", "# Extract content between \\begin{document} and \\end{document}\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "proof = proof_latex[start:end].strip()\n", "\n", "# Convert LaTeX to markdown-like format\n", "proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", proof)\n", "proof = re.sub(r\"\\\\subsection\\{([^}]+)\\}\", r\"### \\1\", proof)\n", "\n", "# Display the proof\n", "display(Latex(proof))"]}, {"cell_type": "code", "execution_count": 8, "id": "4e94bb5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are a helpful assistant that extracts entities and relations from mathematical proofs.', additional_kwargs={}, response_metadata={}), HumanMessage(content=\"\\nGiven the following mathematical proof and the given pattern of mathematical proof extracted from the course, construct a knowledge graph that follows the given pattern.\\n\\nThe pattern components are:\\nentities=[Entity(id='1', name='Base Case', label='Base Case Verification', type='step', start=True, end=False), Entity(id='2', name='Inductive Step', label='Inductive Step Verification', type='step', start=False, end=False), Entity(id='3', name='Induction Hypothesis', label='Assumption of P(k)', type='step', start=False, end=False), Entity(id='4', name='Conclusion', label='Conclusion of P(n) for all n', type='step', start=False, end=True)] relations=[Relation(source='1', target='2', type='grounds', name='Base case leads to inductive step'), Relation(source='2', target='3', type='explains', name='Inductive step relies on induction hypothesis'), Relation(source='3', target='4', type='concludes', name='Induction hypothesis leads to conclusion')]\\n\\nFor the given proof, extract triplets in the form <Source Entity, Relationship, Target Entity> that:\\n1. Follow the structure of mathematical proof pattern\\n2. Map to the steps identified in the course pattern\\n3. Capture the specific details and relationships in this proof which may be different from the course pattern\\n\\nProof:\\n## Theorem\\nProof by Induction: Sum of the First \\\\( n \\\\) Natural Numbers\\n\\nThe sum of the first \\\\( n \\\\) natural numbers is:\\n\\\\[\\n    1 + 2 + 3 + \\\\dots + n = \\\\frac{n(n+1)}{2}\\n\\\\]\\n\\n## Proof by Induction\\nWe proceed by mathematical induction.\\n\\n### Base Case (\\\\( n = 1 \\\\))\\n\\\\[\\n    \\\\text{LHS} = 1, \\\\quad \\\\text{RHS} = \\\\frac{1(1+1)}{2} = 1\\n\\\\]\\nSince \\\\(\\\\text{LHS} = \\\\text{RHS}\\\\), the base case holds.\\n\\n### Inductive Hypothesis\\nAssume the statement holds for some \\\\( k \\\\geq 1 \\\\):\\n\\\\[\\n    1 + 2 + \\\\dots + k = \\\\frac{k(k+1)}{2}\\n\\\\]\\n\\n### Inductive Step (\\\\( n = k + 1 \\\\))\\nWe must show:\\n\\\\[\\n    1 + 2 + \\\\dots + k + (k+1) = \\\\frac{(k+1)(k+2)}{2}\\n\\\\]\\nStarting from the left-hand side:\\n\\\\begin{align}\\n    1 + 2 + \\\\dots + k + (k+1) & = \\\\left(1 + 2 + \\\\dots + k\\\\right) + (k+1)                              \\\\\\\\\\n                              & = \\\\frac{k(k+1)}{2} + (k+1) \\\\quad \\\\text{(by the inductive hypothesis)} \\\\\\\\\\n                              & = \\\\frac{k(k+1) + 2(k+1)}{2}                                           \\\\\\\\\\n                              & = \\\\frac{(k+1)(k + 2)}{2}\\n\\\\end{align}\\nThis matches the right-hand side for \\\\( n = k+1 \\\\).\\n\\n### Conclusion\\nBy the principle of mathematical induction, the formula holds for all \\\\( n \\\\geq 1 \\\\).\\n\", additional_kwargs={}, response_metadata={})]\n", "[SystemMessage(content='You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof and relationships in mathematical content and represent them as knowledge graph triplets.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematic course content in LaTeX format, extract the key steps of the mathematical proof in minimum steps and structure that form the knowledge graph triplets as the pattern of mathematical proofs for this course.\\n\\nFocus on identifying:\\n1. The core steps of mathematical proof\\n2. The relationships between these proof steps\\n3. The typical structure and flow of mathematical proofs of this type\\n4. Key steps and their relationships\\n5. The entities and relationships in the proof\\n6. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof. Please make sure the final graph is a single connected component and have label of the start and end nodes/entities.\\n\\nExtract triplets in the form <Source Step/Entity, Relationship, Target Step/Entity> that represent this pattern.\\n\\nCourse Content:\\n## Theorem\\nProof by Induction: Sum of the First \\\\( n \\\\) Natural Numbers\\n\\nThe sum of the first \\\\( n \\\\) natural numbers is:\\n\\\\[\\n    1 + 2 + 3 + \\\\dots + n = \\\\frac{n(n+1)}{2}\\n\\\\]\\n\\n## Proof by Induction\\nWe proceed by mathematical induction.\\n\\n### Base Case (\\\\( n = 1 \\\\))\\n\\\\[\\n    \\\\text{LHS} = 1, \\\\quad \\\\text{RHS} = \\\\frac{1(1+1)}{2} = 1\\n\\\\]\\nSince \\\\(\\\\text{LHS} = \\\\text{RHS}\\\\), the base case holds.\\n\\n### Inductive Hypothesis\\nAssume the statement holds for some \\\\( k \\\\geq 1 \\\\):\\n\\\\[\\n    1 + 2 + \\\\dots + k = \\\\frac{k(k+1)}{2}\\n\\\\]\\n\\n### Inductive Step (\\\\( n = k + 1 \\\\))\\nWe must show:\\n\\\\[\\n    1 + 2 + \\\\dots + k + (k+1) = \\\\frac{(k+1)(k+2)}{2}\\n\\\\]\\nStarting from the left-hand side:\\n\\\\begin{align}\\n    1 + 2 + \\\\dots + k + (k+1) & = \\\\left(1 + 2 + \\\\dots + k\\\\right) + (k+1)                              \\\\\\\\\\n                              & = \\\\frac{k(k+1)}{2} + (k+1) \\\\quad \\\\text{(by the inductive hypothesis)} \\\\\\\\\\n                              & = \\\\frac{k(k+1) + 2(k+1)}{2}                                           \\\\\\\\\\n                              & = \\\\frac{(k+1)(k + 2)}{2}\\n\\\\end{align}\\nThis matches the right-hand side for \\\\( n = k+1 \\\\).\\n\\n### Conclusion\\nBy the principle of mathematical induction, the formula holds for all \\\\( n \\\\geq 1 \\\\).\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='1', name='Base Case (n = 1)', label='Base Case', type='step', start=True, end=False), Entity(id='2', name='Inductive Hypothesis', label='Inductive Hypothesis', type='step', start=False, end=False), Entity(id='3', name='Inductive Step (n = k + 1)', label='Inductive Step', type='step', start=False, end=False), Entity(id='4', name='Conclusion', label='Conclusion', type='step', start=False, end=True)] relations=[Relation(source='1', target='2', type='follows', name='Base case leads to inductive hypothesis'), Relation(source='2', target='3', type='follows', name='Inductive hypothesis leads to inductive step'), Relation(source='3', target='4', type='follows', name='Inductive step leads to conclusion')]\n", "\n", "## Visualization and Analysis\n", "\n", "You can visualize and analyze the graphs in Neo4j Browser using these queries:\n", "\n", "### View Course Pattern:\n", "```cypher\n", "MATCH p=()-[r]->() \n", "WHERE r.graph_type = 'course_pattern' \n", "RETURN p\n", "```\n", "\n", "### View Proof Graph:\n", "```cypher\n", "MATCH p=()-[r]->() \n", "WHERE r.graph_type = 'proof_example' \n", "RETURN p\n", "```\n", "\n", "### View Both Graphs Side by Side:\n", "```cypher\n", "MATCH pattern=()-[r1]->() \n", "WHERE r1.graph_type = 'course_pattern'\n", "WITH collect(pattern) as patterns\n", "MATCH proof=()-[r2]->() \n", "WHERE r2.graph_type = 'proof_example'\n", "WITH patterns, collect(proof) as proofs\n", "RETURN patterns, proofs\n", "```\n", "\n"]}], "source": ["PROOF_PATTERN_APPLICATION_PROMPT = \"\"\"\n", "Given the following mathematical proof and the given pattern of mathematical proof extracted from the course, construct a knowledge graph that follows the given pattern.\n", "\n", "The pattern components are:\n", "{course_pattern}\n", "\n", "For the given proof, extract triplets in the form <Source Entity, Relationship, Target Entity> that:\n", "1. Follow the structure of mathematical proof pattern\n", "2. Map to the steps identified in the course pattern\n", "3. Capture the specific details and relationships in this proof which may be different from the course pattern\n", "\n", "Proof:\n", "{{proof}}\n", "\"\"\"\n", "\n", "# # Format the prompt with course_pattern\n", "# formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n", "#     course_pattern=course_pattern\n", "# )\n", "\n", "# # Pass the formatted prompt and proof_content to extract_triplets\n", "# proof_triplets = extract_triplets(proof, formatted_prompt)\n", "\n", "# # # Apply the pattern to the proof\n", "# # proof_triplets = extract_triplets(proof_content, PROOF_PATTERN_APPLICATION_PROMPT)\n", "\n", "# # Store the proof graph in Neo4j\n", "# neo4j.store_triplets(proof_triplets, \"proof_example\")\n", "\n", "# Format the prompt with course_pattern\n", "formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n", "    course_pattern=course_pattern\n", ")\n", "\n", "# Pass the formatted prompt and proof_content to extract_triplets\n", "proof_triplets = extract_triplets(proof, formatted_prompt)\n", "\n", "# Clean the database to remove any existing graphs\n", "neo4j.clean_database()\n", "\n", "# Store the course pattern graph\n", "neo4j.store_triplets(course_pattern, \"course_pattern\")\n", "\n", "# Store the proof graph as a separate graph\n", "neo4j.store_triplets(proof_triplets, \"proof_example\")\n", "\n", "\n", "# Extract the course pattern\n", "course_pattern = extract_triplets(\n", "    proof, custom_prompt=COURSE_PATTERN_PROMPT, system_message=SYSTEM_PROMPT\n", ")\n", "print(course_pattern)\n", "\n", "# Display visualization queries\n", "print(neo4j.get_visualization_queries())"]}, {"cell_type": "markdown", "id": "1efd42b5", "metadata": {}, "source": ["## Visualization and Analysis\n", "\n", "You can visualize and analyze the graphs in Neo4j Browser using these queries:\n", "\n", "### View Course Pattern:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_type = 'course_pattern' RETURN p\n", "```\n", "\n", "### View Proof Graph:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_type = 'proof_example' RETURN p\n", "```\n", "\n", "### Compare Pattern and Proof:\n", "```cypher\n", "MATCH p=()-[r]->() RETURN p\n", "```"]}, {"cell_type": "markdown", "id": "03ff9175", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "5d23c448", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "58a55696", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "78696ce8", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}