{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Multi-Abstraction Triplet Extraction\n", "\n", "This notebook demonstrates how to extract multiple knowledge graphs from a single LaTeX proof at different levels of abstraction using LLM prompts.\n", "\n", "The goal is to generate:\n", "- **High-level abstract graph**: capturing broad proof steps\n", "- **Mid-level graph**: intermediate details\n", "- **Fine-grained graph**: detailed entities and relations\n", "\n", "Each graph will be stored in Neo4j with metadata to distinguish abstraction levels.\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON>up\n", "\n", "- Ensure Neo4j is running (`docker compose up -d`)\n", "- Configure Neo4j connection and LLM API keys if needed\n", "- Import necessary libraries"]}, {"cell_type": "markdown", "id": "20b17336", "metadata": {}, "source": ["Add src to the Python Path in the Notebook"]}, {"cell_type": "code", "execution_count": 7, "id": "5c6f7909", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "code", "execution_count": 8, "id": "f415dbc4", "metadata": {}, "outputs": [{"data": {"text/latex": ["# Proof That the Sum of the First Five Numbers Equals 15\n", "\n", "\n", "## Question\n", "We want to prove that the sum of the first four natural numbers equals 10:\n", "\\[ 1 + 2 + 3 + 4+5 = 15 \\]\n", "\n", "## Proof\n", "We compute the sum step by step:\n", "\n", "\\begin{align*}\n", "1 + 2 + 3 + 4 + 5 &= (1 + 2) + 3 + 4 +5 && \\text{(Group the first two terms)} \\\\\n", "              &= 3 + 3 + 4 + 5         && \\text{(Compute } 1 + 2 = 3\\text{)} \\\\\n", "              &= (3 + 3) + 4 +5       && \\text{(Group the next two terms)} \\\\\n", "              &= 6 + 4 + 5            && \\text{(Compute } 3 + 3 = 6\\text{)} \\\\\n", "              &= 10 +5               && \\text{(Compute } 6 + 4 = 10\\text{)} \\\\\n", "              &= 15.                    && \\text{(Compute } 10 + 5 = 15\\text{)}. \n", "\\end{align*}\n", "\n", "## Conclusion\n", "Thus, we have shown through step-by-step addition that:\n", "\\[ 1 + 2 + 3 + 4 + 5= 15 \\]"], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "\n", "# Load LaTeX proof\n", "proof_latex = read_proof(\n", "    \"../../data/proofs/english/sum_of_first_5_ints/proof1.tex\"\n", ")\n", "\n", "# Find the start and end positions\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "\n", "# Extract the content between \\begin{document} and \\end{document}\n", "informal_proof = proof_latex[start:end].strip()\n", "\n", "# Replace any \\section{...} with ## ...\n", "informal_proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\subsection\\{([^}]+)\\}\", r\"### \\1\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\title\\{([^}]+)\\}\", r\"# \\1\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\maketitle\", \"\", informal_proof)\n", "\n", "# Replace \\begin{itemize} and \\end{itemize} with Markdown-style lists\n", "informal_proof = re.sub(r\"\\\\begin{itemize}\", \"\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\end{itemize}\", \"\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\item\\s+\\*\\*([^:]+):\\*\\*\", r\"- **\\1:**\", informal_proof)\n", "\n", "# Replace \\begin{center} ... \\end{center} with \\[ ... \\]\n", "informal_proof = re.sub(\n", "    r\"\\\\begin{center}(.*?)\\\\end{center}\", r\"\\[\\1\\]\", informal_proof, flags=re.DOTALL\n", ")\n", "\n", "# Display the LaTeX content\n", "display(Latex(informal_proof))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Define Prompts for Different Abstraction Levels\n", "\n", "We customize the global `TRIPLET_EXTRACTION_PROMPT` before each extraction to instruct the LLM to extract triplets at various granularities."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Extract Triplets Using `extract_triplets()`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.phase1.extract_triplets import extract_triplets\n", "from src.phase1.prompts import (\n", "    TRIPLET_EXTRACTION_PROMPT,\n", "    HIGH_LEVEL_TRIPLET_EXTRACTION_PROMPT,\n", "    MID_LEVEL_TRIPLET_EXTRACTION_PROMPT,\n", "    FINE_GRAINED_TRIPLET_EXTRACTION_PROMPT,\n", ")\n", "# Original prompt\n", "ORIGINAL_PROMPT = TRIPLET_EXTRACTION_PROMPT\n", "original_triplets = extract_triplets(informal_proof, ORIGINAL_PROMPT)\n", "\n", "# High level\n", "high_level_triplets = extract_triplets(\n", "    informal_proof, HIGH_LEVEL_TRIPLET_EXTRACTION_PROMPT\n", ")\n", "\n", "# Mid level\n", "mid_level_triplets = extract_triplets(\n", "    informal_proof, MID_LEVEL_TRIPLET_EXTRACTION_PROMPT\n", ")\n", "\n", "# Fine-grained level\n", "fine_grained_triplets = extract_triplets(\n", "    informal_proof, FINE_GRAINED_TRIPLET_EXTRACTION_PROMPT\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Store Triplets in Neo4j with Abstraction Level Metadata\n", "\n", "Add a `graph_level` property to nodes and/or relationships to distinguish abstraction levels."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Initialize Neo4JUtils\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"neo4j\", \"password\"))\n", "\n", "# Clean the database (delete all nodes and relationships)\n", "neo4j.clean_database()\n", "\n", "# Example usage (assuming triplet objects with .entities and .relations)\n", "neo4j.store_triplets(original_triplets, \"original\")\n", "neo4j.store_triplets(high_level_triplets, \"high\")\n", "neo4j.store_triplets(mid_level_triplets, \"medium\")\n", "neo4j.store_triplets(fine_grained_triplets, \"low\")"]}, {"cell_type": "code", "execution_count": null, "id": "c03a40db", "metadata": {}, "outputs": [{"data": {"text/plain": ["Triplet(entities=[Entity(id='1', name='1', label='Number', type='Natural Number'), Entity(id='2', name='2', label='Number', type='Natural Number'), Entity(id='3', name='3', label='Number', type='Natural Number'), Entity(id='4', name='4', label='Number', type='Natural Number'), Entity(id='5', name='5', label='Number', type='Natural Number'), Entity(id='15', name='15', label='Result', type='Natural Number'), Entity(id='10', name='10', label='Intermediate Result', type='Natural Number'), Entity(id='6', name='6', label='Intermediate Result', type='Natural Number'), Entity(id='3_1', name='3', label='Intermediate Result', type='Natural Number')], relations=[Relation(source='1', target='2', type='Addition', name='Compute 1 + 2'), Relation(source='3_1', target='3', type='Addition', name='Compute 1 + 2 = 3'), Relation(source='3_1', target='4', type='Addition', name='Group the next two terms'), Relation(source='3', target='3', type='Addition', name='Compute 3 + 3'), Relation(source='3', target='4', type='Addition', name='Group the next two terms'), Relation(source='6', target='4', type='Addition', name='Compute 3 + 3 = 6'), Relation(source='6', target='4', type='Addition', name='Compute 6 + 4'), Relation(source='10', target='5', type='Addition', name='Compute 10 + 5'), Relation(source='10', target='15', type='Equality', name='Final Result'), Relation(source='1 + 2 + 3 + 4 + 5', target='15', type='Equality', name='Sum of the first five numbers')])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["fine_grained_triplets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualize and Compare Graphs in Neo4j Browser\n", "\n", "Use Cypher queries to filter graphs by abstraction level.\n", "\n", "### Show all graphs:\n", "```cypher\n", "MATCH p=()-[r]->() RETURN p\n", "```\n", "\n", "### Show only high-level graph:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_abstraction_level = 'high' RETURN p\n", "```\n", "\n", "### Show only mid-level graph:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_abstraction_level = 'medium' RETURN p\n", "```\n", "\n", "### Show only fine-grained graph:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_abstraction_level = 'low' RETURN p\n", "```\n", "\n", "---"]}, {"cell_type": "markdown", "id": "0ee1123b", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "816601c1", "metadata": {}, "source": ["## 6. Sequential Step-Based Triplet Extraction by Detail Level\n", "\n", "In this section, we generate multiple knowledge graphs by instructing the LLM to explain the proof sequentially at different levels of detail:\n", "\n", "- **high**: very detailed sequential steps\n", "- **medium**: moderate detail\n", "- **low**: only major steps, ignoring fine details\n", "\n", "This approach provides alternative graph views based on proof step granularity."]}, {"cell_type": "code", "execution_count": null, "id": "cf280937", "metadata": {}, "outputs": [], "source": ["from src.phase1.extract_triplets import extract_triplets\n", "\n", "detail_levels = [\"high\", \"medium\", \"low\"]\n", "sequential_triplets = {}\n", "\n", "for level in detail_levels:\n", "#     prompt_template = \"\"\"\n", "# Given the following LaTeX proof, explain the proof sequentially at a {level} level of detail.\n", "\n", "# three qualitative levels:\n", "# - \"high\": very detailed sequential steps\n", "# - \"medium\": moderate detail\n", "# - \"low\": only major steps, ignoring fine details\n", "\n", "# For each step, provide a one-word summary to be used as the node label.\n", "# Then, extract triplets in the form <Source Entity, Relationship, Target Entity> that connect these summarized steps.\n", "\n", "# Proof:\n", "# {{proof}}\n", "# \"\"\"\n", "\n", "    prompt_template = \"\"\"\n", "Given the following LaTeX proof, your task is to explain the proof sequentially at the **{level}** level of detail.\n", "\n", "The three qualitative levels are:\n", "- **high**: very detailed sequential steps, include all minor details and sub-steps.\n", "- **medium**: moderate detail, include key steps but omit minor details.\n", "- **low**: only major steps, ignore or summarize fine details.\n", "\n", "Please generate the explanation at the **{level}** level accordingly.\n", "\n", "For each step, provide a one-word summary to be used as the node label.\n", "\n", "Then, extract triplets in the form <Source Entity, Relationship, Target Entity> that connect these summarized steps.\n", "\n", "Proof:\n", "{{proof}}\n", "\"\"\"\n", "    # Pre-format the prompt template with the `level` variable\n", "    pre_formatted_prompt = prompt_template.format(level=level)\n", "    # Pass the pre-formatted prompt and the `proof` parameter to the extract_triplets function\n", "    triplets = extract_triplets(informal_proof, pre_formatted_prompt)\n", "\n", "    # Store the triplets in the dictionary\n", "    sequential_triplets[level] = triplets\n", "\n", "    # Store in Neo4j with metadata\n", "    neo4j.store_triplets(triplets, f\"sequential_{level}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3fce8e9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}