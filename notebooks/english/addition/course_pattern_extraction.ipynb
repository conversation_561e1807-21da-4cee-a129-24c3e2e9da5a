{"cells": [{"cell_type": "markdown", "id": "2a751e2f", "metadata": {}, "source": ["# Course Pattern Extraction and Application\n", "\n", "This notebook implements a two-phase approach for:\n", "1. Extracting graph-based patterns from a course about mathematical induction\n", "2. Using those patterns to construct knowledge graphs for specific proof examples\n", "\n", "---"]}, {"cell_type": "markdown", "id": "9aca23a8", "metadata": {}, "source": ["## Phase 1: Course Pattern Extraction\n", "\n", "First, we'll extract the graph-based pattern from the mathematical induction course."]}, {"cell_type": "code", "execution_count": 1, "id": "8d428c69", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "code", "execution_count": 2, "id": "eb83149d", "metadata": {}, "outputs": [], "source": ["from IPython.display import display, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "from src.phase1.extract_triplets import extract_triplets\n", "from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Load the course content\n", "course_latex = read_proof(\"../../data/courses/addition/course_2.tex\")"]}, {"cell_type": "markdown", "id": "ff16d995", "metadata": {}, "source": ["### Extract Course Pattern\n", "\n", "We'll use a specialized prompt to extract the pattern of mathematical induction from the course content."]}, {"cell_type": "code", "execution_count": 3, "id": "bca4c3d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\\n\\nFocus on identifying:\\n1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\\n2. The precise mathematical operations performed at each step\\n3. The exact sequence of operations, with clear predecessor-successor relationships\\n4. Intermediate results at each calculation stage\\n5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\\n\\nIMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\\n\\nThe final knowledge graph MUST:\\n1. Have clearly marked START node(s) (the initial example statement)\\n2. Have clearly marked END node(s) (the final result)\\n3. Include ALL intermediate calculation steps with no gaps in reasoning\\n4. Form a single connected component with a clear directional flow\\n5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\\n\\nExtract triplets in the given form of structured output that represent this detailed calculation process.\\n\\nNote:\\n- Entities or Nodes should be specified with short mathematical expressions.\\n- Relations at this mathematical calculation graph is only grounding or simple reasoning connecting one expression to another.\\n- Steps are as much as possible explaining all the minor steps of the reasoning of the calculation.\\n\\nCourse Content:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{amssymb}\\n\\n\\\\begin{document}\\n\\\\section{Title}\\nAddition Via Recursion\\n\\n\\\\section{Abstract}\\nRecursion is an effective way to add numbers. Using recursion, a more complicated addition is reduced to simpler additions. Hence, through a recursive procedure, it turns into several steps of addition by $1$, where $a+1$ denotes the next number.\\n\\\\section{Procedure}\\n\\\\[\\n    \\\\begin{aligned}\\n         & a + b                              \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & (a + (b-1)) + 1                    \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & ((a + (b-2)) + 1) + 1              \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & \\\\quad \\\\vdots                       \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & (\\\\cdots((a + 0) + 1) + \\\\cdots + 1) \\\\\\\\\\n    \\\\end{aligned}\\n\\\\]\\n\\\\section{Examples}\\n\\\\subsection{Example 1}\\n\\\\subsubsection{Question}\\nCalculate this \\\\(3 + 2\\\\):\\n\\\\subsubsection{Answer}\\n\\\\[\\n    \\\\begin{aligned}\\n        3 + 2 & = (3 + 1) + 1 \\\\quad       & \\\\text{(First decomposition)}  \\\\\\\\\\n              & = ((3 + 0) + 1) + 1 \\\\quad & \\\\text{(Second decomposition)} \\\\\\\\\\n              & = (3 + 1) + 1 \\\\quad       & \\\\text{(Base case applied)}    \\\\\\\\\\n              & = 4 + 1 \\\\quad             & \\\\text{(First increment)}      \\\\\\\\\\n              & = 5 \\\\quad                 & \\\\text{(Final result)}\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\subsection{Example 2}\\n\\\\subsubsection{Question}\\nCalculate the result of \\\\(a + b\\\\):\\n\\\\subsubsection{Answer}\\n\\\\[\\n    \\\\begin{aligned}\\n        a + b & = (a + (b-1)) + 1                                       \\\\\\\\\\n              & = ((a + (b-2)) + 1) + 1                                 \\\\\\\\\\n              & \\\\;\\\\;\\\\vdots                                              \\\\\\\\\\n              & = (\\\\cdots((a + 0) + 1) + \\\\cdots + 1)                    \\\\\\\\\\n              & = a + \\\\underbrace{1 + 1 + \\\\cdots + 1}_{b \\\\text{ times}} \\\\\\\\\\n              & = a + b\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\end{document}\\n\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='1', name='a + b', label='Initial Addition', type='step', start=True, end=False), Entity(id='2', name='a + (b-1) + 1', label='First Decomposition', type='step', start=False, end=False), Entity(id='3', name='(a + (b-2)) + 1 + 1', label='Second Decomposition', type='step', start=False, end=False), Entity(id='4', name='...', label='Recursive Steps', type='step', start=False, end=False), Entity(id='5', name='(a + 0) + 1 + ... + 1', label='Base Case', type='step', start=False, end=False), Entity(id='6', name='a + (1 + 1 + ... + 1)', label=\"Summation of 1's\", type='step', start=False, end=False), Entity(id='7', name='Final Result: a + b', label='Final Result', type='step', start=False, end=True)] relations=[Relation(source='1', target='2', type='grounds', name='applies_first_decomposition'), Relation(source='2', target='3', type='grounds', name='applies_second_decomposition'), Relation(source='3', target='4', type='grounds', name='continues_recursive_steps'), Relation(source='4', target='5', type='grounds', name='reaches_base_case'), Relation(source='5', target='6', type='grounds', name='simplifies_to_summation'), Relation(source='6', target='7', type='grounds', name='concludes_final_result')]\n"]}], "source": ["SYSTEM_PROMPT = \"\"\"You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.\"\"\"\n", "\n", "COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\n", "\n", "Focus on identifying:\n", "1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\n", "2. The precise mathematical operations performed at each step\n", "3. The exact sequence of operations, with clear predecessor-successor relationships\n", "4. Intermediate results at each calculation stage\n", "5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\n", "\n", "IMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\n", "\n", "The final knowledge graph MUST:\n", "1. Have clearly marked START node(s) (the initial example statement)\n", "2. Have clearly marked END node(s) (the final result)\n", "3. Include ALL intermediate calculation steps with no gaps in reasoning\n", "4. Form a single connected component with a clear directional flow\n", "5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\n", "\n", "Extract triplets in the given form of structured output that represent this detailed calculation process.\n", "\n", "Note:\n", "- Entities or Nodes should be specified with short mathematical expressions.\n", "- Relations at this mathematical calculation graph is only grounding or simple reasoning connecting one expression to another.\n", "- Steps are as much as possible explaining all the minor steps of the reasoning of the calculation.\n", "\n", "Course Content:\n", "```\n", "{course_latex}\n", "```\n", "\"\"\"\n", "\n", "\n", "# Extract the course pattern\n", "course_pattern = extract_triplets(\n", "    custom_prompt=COURSE_PATTERN_PROMPT.format(course_latex=course_latex),\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "print(course_pattern)\n", "\n", "# Store the pattern in Neo4j\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"neo4j\", \"password\"))\n", "neo4j.clean_database()\n", "neo4j.store_triplets(course_pattern, \"course\")"]}, {"cell_type": "markdown", "id": "763d980b", "metadata": {}, "source": ["## Phase 2: Proof Pattern Application\n", "\n", "Now we'll use the extracted pattern to construct knowledge graphs for specific proof examples."]}, {"cell_type": "code", "execution_count": 4, "id": "72d488b5", "metadata": {}, "outputs": [], "source": ["# Load a proof example\n", "example_latex = read_proof(\"../../data/courses/addition/example3.tex\")"]}, {"cell_type": "code", "execution_count": 5, "id": "4e94bb5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical calculation and the given pattern of mathematical calculation extracted from the course, construct a knowledge graph that follows the given pattern.\\n\\nThe pattern components are:\\nentities=[Entity(id=\\'1\\', name=\\'a + b\\', label=\\'Initial Addition\\', type=\\'step\\', start=True, end=False), Entity(id=\\'2\\', name=\\'a + (b-1) + 1\\', label=\\'First Decomposition\\', type=\\'step\\', start=False, end=False), Entity(id=\\'3\\', name=\\'(a + (b-2)) + 1 + 1\\', label=\\'Second Decomposition\\', type=\\'step\\', start=False, end=False), Entity(id=\\'4\\', name=\\'...\\', label=\\'Recursive Steps\\', type=\\'step\\', start=False, end=False), Entity(id=\\'5\\', name=\\'(a + 0) + 1 + ... + 1\\', label=\\'Base Case\\', type=\\'step\\', start=False, end=False), Entity(id=\\'6\\', name=\\'a + (1 + 1 + ... + 1)\\', label=\"Summation of 1\\'s\", type=\\'step\\', start=False, end=False), Entity(id=\\'7\\', name=\\'Final Result: a + b\\', label=\\'Final Result\\', type=\\'step\\', start=False, end=True)] relations=[Relation(source=\\'1\\', target=\\'2\\', type=\\'grounds\\', name=\\'applies_first_decomposition\\'), Relation(source=\\'2\\', target=\\'3\\', type=\\'grounds\\', name=\\'applies_second_decomposition\\'), Relation(source=\\'3\\', target=\\'4\\', type=\\'grounds\\', name=\\'continues_recursive_steps\\'), Relation(source=\\'4\\', target=\\'5\\', type=\\'grounds\\', name=\\'reaches_base_case\\'), Relation(source=\\'5\\', target=\\'6\\', type=\\'grounds\\', name=\\'simplifies_to_summation\\'), Relation(source=\\'6\\', target=\\'7\\', type=\\'grounds\\', name=\\'concludes_final_result\\')]\\n\\nFor the given proof, extract triplets in the form <Source Entity, Relationship, Target Entity> that:\\n1. Follow the structure of mathematical proof pattern\\n2. Map to the steps identified in the course pattern\\n3. Capture the specific details and relationships in this proof which may be different from the course pattern\\n\\nProof:\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{amssymb}\\n\\\\title{Introduction to Addition via Recursion}\\n\\\\author{}\\n\\\\date{}\\n\\n\\\\begin{document}\\n\\\\section{Question:}\\nWhat is the result of \\\\( 2 + 3 \\\\)?\\n\\\\section{Answer:}\\nBreaking down the recursion step-by-step:\\n\\\\[\\n    \\\\begin{aligned}\\n        2 + 3 & = (2 + 2) + 1             \\\\\\\\\\n              & = ((2 + 1) + 1) + 1       \\\\\\\\\\n              & = (((2 + 0) + 1) + 1) + 1 \\\\\\\\\\n              & = ((2 + 1) + 1) + 1       \\\\\\\\\\n              & = (3 + 1) + 1             \\\\\\\\\\n              & = 4 + 1                   \\\\\\\\\\n              & = 5\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\n\\n\\\\end{document}\\n', additional_kwargs={}, response_metadata={})]\n"]}], "source": ["PROOF_PATTERN_APPLICATION_PROMPT = \"\"\"\n", "Given the following mathematical calculation and the given pattern of mathematical calculation extracted from the course, construct a knowledge graph that follows the given pattern.\n", "\n", "The pattern components are:\n", "{course_pattern}\n", "\n", "For the given proof, extract triplets in the form <Source Entity, Relationship, Target Entity> that:\n", "1. Follow the structure of mathematical proof pattern\n", "2. Map to the steps identified in the course pattern\n", "3. Capture the specific details and relationships in this proof which may be different from the course pattern\n", "\n", "Proof:\n", "{example_latex}\n", "\"\"\"\n", "\n", "# Format the prompt with course_pattern and example_latex\n", "formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n", "    course_pattern=course_pattern, example_latex=example_latex\n", ")\n", "\n", "# Pass the formatted prompt and proof_content to extract_triplets\n", "example_triplets = extract_triplets(\n", "    custom_prompt=formatted_prompt, \n", "    system_message=SYSTEM_PROMPT\n", ")\n", "\n", "# Clean the database to remove any existing graphs\n", "neo4j.clean_database()\n", "\n", "# Store the course pattern graph\n", "neo4j.store_triplets(course_pattern, \"course\")\n", "\n", "# Store the example graph as a separate graph\n", "neo4j.store_triplets(example_triplets, \"example\")"]}, {"cell_type": "code", "execution_count": 6, "id": "3d5e93c0", "metadata": {}, "outputs": [], "source": ["# Load a test example\n", "test1_latex = read_proof(\"../../data/courses/addition/test1.tex\")"]}, {"cell_type": "code", "execution_count": 7, "id": "43e2f664", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following pattern of mathematical calculation extracted from the course and the given example of the calculation with the suitable answer, construct a knowledge graph that follows the given course pattern to answer to the question.\\n\\nThe pattern components are:\\n```\\nentities=[Entity(id=\\'1\\', name=\\'a + b\\', label=\\'Initial Addition\\', type=\\'step\\', start=True, end=False), Entity(id=\\'2\\', name=\\'a + (b-1) + 1\\', label=\\'First Decomposition\\', type=\\'step\\', start=False, end=False), Entity(id=\\'3\\', name=\\'(a + (b-2)) + 1 + 1\\', label=\\'Second Decomposition\\', type=\\'step\\', start=False, end=False), Entity(id=\\'4\\', name=\\'...\\', label=\\'Recursive Steps\\', type=\\'step\\', start=False, end=False), Entity(id=\\'5\\', name=\\'(a + 0) + 1 + ... + 1\\', label=\\'Base Case\\', type=\\'step\\', start=False, end=False), Entity(id=\\'6\\', name=\\'a + (1 + 1 + ... + 1)\\', label=\"Summation of 1\\'s\", type=\\'step\\', start=False, end=False), Entity(id=\\'7\\', name=\\'Final Result: a + b\\', label=\\'Final Result\\', type=\\'step\\', start=False, end=True)] relations=[Relation(source=\\'1\\', target=\\'2\\', type=\\'grounds\\', name=\\'applies_first_decomposition\\'), Relation(source=\\'2\\', target=\\'3\\', type=\\'grounds\\', name=\\'applies_second_decomposition\\'), Relation(source=\\'3\\', target=\\'4\\', type=\\'grounds\\', name=\\'continues_recursive_steps\\'), Relation(source=\\'4\\', target=\\'5\\', type=\\'grounds\\', name=\\'reaches_base_case\\'), Relation(source=\\'5\\', target=\\'6\\', type=\\'grounds\\', name=\\'simplifies_to_summation\\'), Relation(source=\\'6\\', target=\\'7\\', type=\\'grounds\\', name=\\'concludes_final_result\\')]\\n```\\n\\nThe example question and answer are:\\n\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{amssymb}\\n\\\\title{Introduction to Addition via Recursion}\\n\\\\author{}\\n\\\\date{}\\n\\n\\\\begin{document}\\n\\\\section{Question:}\\nWhat is the result of \\\\( 2 + 3 \\\\)?\\n\\\\section{Answer:}\\nBreaking down the recursion step-by-step:\\n\\\\[\\n    \\\\begin{aligned}\\n        2 + 3 & = (2 + 2) + 1             \\\\\\\\\\n              & = ((2 + 1) + 1) + 1       \\\\\\\\\\n              & = (((2 + 0) + 1) + 1) + 1 \\\\\\\\\\n              & = ((2 + 1) + 1) + 1       \\\\\\\\\\n              & = (3 + 1) + 1             \\\\\\\\\\n              & = 4 + 1                   \\\\\\\\\\n              & = 5\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\n\\n\\\\end{document}\\n```\\n\\nand the extracted knowledge graph triplet to this example is:\\n```\\nentities=[Entity(id=\\'1\\', name=\\'2 + 3\\', label=\\'Initial Addition\\', type=\\'step\\', start=True, end=False), Entity(id=\\'2\\', name=\\'(2 + 2) + 1\\', label=\\'First Decomposition\\', type=\\'step\\', start=False, end=False), Entity(id=\\'3\\', name=\\'((2 + 1) + 1) + 1\\', label=\\'Second Decomposition\\', type=\\'step\\', start=False, end=False), Entity(id=\\'4\\', name=\\'(((2 + 0) + 1) + 1) + 1\\', label=\\'Recursive Steps\\', type=\\'step\\', start=False, end=False), Entity(id=\\'5\\', name=\\'((2 + 1) + 1) + 1\\', label=\\'Revisiting Previous Step\\', type=\\'step\\', start=False, end=False), Entity(id=\\'6\\', name=\\'(3 + 1) + 1\\', label=\\'Simplifying to Next Step\\', type=\\'step\\', start=False, end=False), Entity(id=\\'7\\', name=\\'4 + 1\\', label=\\'Final Addition\\', type=\\'step\\', start=False, end=False), Entity(id=\\'8\\', name=\\'5\\', label=\\'Final Result\\', type=\\'step\\', start=False, end=True)] relations=[Relation(source=\\'1\\', target=\\'2\\', type=\\'grounds\\', name=\\'applies_first_decomposition\\'), Relation(source=\\'2\\', target=\\'3\\', type=\\'grounds\\', name=\\'applies_second_decomposition\\'), Relation(source=\\'3\\', target=\\'4\\', type=\\'grounds\\', name=\\'continues_recursive_steps\\'), Relation(source=\\'4\\', target=\\'5\\', type=\\'grounds\\', name=\\'revisits_previous_step\\'), Relation(source=\\'5\\', target=\\'6\\', type=\\'grounds\\', name=\\'simplifies_to_next_step\\'), Relation(source=\\'6\\', target=\\'7\\', type=\\'grounds\\', name=\\'concludes_final_addition\\'), Relation(source=\\'7\\', target=\\'8\\', type=\\'grounds\\', name=\\'arrives_at_final_result\\')]\\n```\\n\\nQuestion:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{amssymb}\\n\\n\\\\begin{document}\\n\\\\section{Question:}\\nWhat is the result of \\\\( 5 + 100 \\\\)?\\n\\\\end{document}\\n```\\n\\nFor the given question, extract triplets in the form <Source Entity, Relationship, Target Entity> that:\\n1. Follow the structure of mathematical calculation pattern\\n2. Map to the steps identified in the course pattern\\n3. Capture the specific details and relationships in this calculation which may be different from the course pattern\\n4. If you see calculation of more than 2 numbers please break the problem into two problems and solve them step by step.\\n5. The final step of the calculation should be the answer to the question as a single number.\\n', additional_kwargs={}, response_metadata={})]\n"]}], "source": ["PROOF_PATTERN_APPLICATION_PROMPT = \"\"\"\n", "Given the following pattern of mathematical calculation extracted from the course and the given example of the calculation with the suitable answer, construct a knowledge graph that follows the given course pattern to answer to the question.\n", "\n", "The pattern components are:\n", "```\n", "{course_pattern}\n", "```\n", "\n", "The example question and answer are:\n", "\n", "```\n", "{example_latex}\n", "```\n", "\n", "and the extracted knowledge graph triplet to this example is:\n", "```\n", "{example_triplets}\n", "```\n", "\n", "Question:\n", "```\n", "{test1_latex}\n", "```\n", "\n", "For the given question, extract triplets in the form <Source Entity, Relationship, Target Entity> that:\n", "1. Follow the structure of mathematical calculation pattern\n", "2. Map to the steps identified in the course pattern\n", "3. Capture the specific details and relationships in this calculation which may be different from the course pattern\n", "4. If you see calculation of more than 2 numbers please break the problem into two problems and solve them step by step.\n", "5. The final step of the calculation should be the answer to the question as a single number.\n", "\"\"\"\n", "\n", "# Format the prompt with course_pattern\n", "formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n", "    course_pattern=course_pattern,\n", "    example_latex=example_latex,\n", "    example_triplets=example_triplets,\n", "    test1_latex=test1_latex,\n", ")\n", "\n", "# Pass the formatted prompt and proof_content to extract_triplets\n", "test_triplets = extract_triplets(\n", "    custom_prompt=formatted_prompt, \n", "    system_message=SYSTEM_PROMPT\n", ")\n", "\n", "# Clean the database to remove any existing graphs\n", "neo4j.clean_database()\n", "\n", "# Store the course pattern graph\n", "neo4j.store_triplets(course_pattern, \"course\")\n", "\n", "# Store the proof graph as a separate graph\n", "neo4j.store_triplets(example_triplets, \"example\")\n", "\n", "# Store the test graph as a separate graph\n", "neo4j.store_triplets(test_triplets, \"test1\")"]}, {"cell_type": "markdown", "id": "1efd42b5", "metadata": {}, "source": ["## Visualization and Analysis\n", "\n", "You can visualize and analyze the graphs in Neo4j Browser using these queries:\n", "\n", "### View Course Pattern:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_type = 'course_pattern' RETURN p\n", "```\n", "\n", "### View Proof Graph:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_type = 'proof_example' RETURN p\n", "```\n", "\n", "### Compare Pattern and Proof:\n", "```cypher\n", "MATCH p=()-[r]->() RETURN p\n", "```"]}, {"cell_type": "markdown", "id": "03ff9175", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "5d23c448", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "58a55696", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "78696ce8", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}