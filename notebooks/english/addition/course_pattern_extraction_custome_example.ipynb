{"cells": [{"cell_type": "markdown", "id": "2a751e2f", "metadata": {}, "source": ["# Course Pattern Extraction and Application\n", "\n", "This notebook implements a two-phase approach for:\n", "1. Extracting graph-based patterns from a course about mathematical induction\n", "2. Using those patterns to construct knowledge graphs for specific proof examples\n", "\n", "---"]}, {"cell_type": "markdown", "id": "9aca23a8", "metadata": {}, "source": ["## Phase 1: Course Pattern Extraction\n", "\n", "First, we'll extract the graph-based pattern from the mathematical induction course."]}, {"cell_type": "code", "execution_count": 1, "id": "8d428c69", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "code", "execution_count": 2, "id": "eb83149d", "metadata": {}, "outputs": [{"data": {"text/latex": ["\n", "\n", "## Definition\n", "Addition of two non-negative integers \\( a \\) and \\( b \\), denoted \\( a + b \\), can be defined recursively as follows:\n", "\n", "\n", "    \\item \\textbf{Base Case}: If \\( b = 0 \\), then \\( a + b = a \\).\n", "    \\item \\textbf{Recursive Case}: If \\( b > 0 \\), then \\( a + b = (a + (b - 1)) + 1 \\).\n", "\n", "\n", "This definition reduces the problem of adding \\( a \\) and \\( b \\) to adding \\( a \\) and \\( b - 1 \\), incrementing the result by 1. This process continues recursively until the base case is reached."], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "from src.phase1.extract_triplets import extract_triplets\n", "from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Load the course content\n", "course_latex = read_proof(\"../../data/courses/addition/course.tex\")\n", "\n", "# Extract content between \\begin{document} and \\end{document}\n", "start = course_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = course_latex.find(r\"\\end{document}\")\n", "proof = course_latex[start:end].strip()\n", "\n", "# Convert LaTeX to markdown-like format\n", "proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", proof)\n", "proof = re.sub(r\"\\\\subsection\\{([^}]+)\\}\", r\"### \\1\", proof)\n", "proof = re.sub(r\"\\\\title\\{([^}]+)\\}\", r\"# \\1\", proof)\n", "proof = re.sub(r\"\\\\maketitle\", \"\", proof)\n", "proof = re.sub(r\"\\\\begin{itemize}\", \"\", proof)\n", "proof = re.sub(r\"\\\\end{itemize}\", \"\", proof)\n", "proof = re.sub(r\"\\\\item\\s+\\*\\*([^:]+):\\*\\*\", r\"- **\\1:**\", proof)\n", "\n", "# Display the course content\n", "display(Latex(proof))"]}, {"cell_type": "markdown", "id": "ff16d995", "metadata": {}, "source": ["### Extract Course Pattern\n", "\n", "We'll use a specialized prompt to extract the pattern of mathematical induction from the course content."]}, {"cell_type": "code", "execution_count": 3, "id": "bca4c3d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof and relationships in mathematical content and represent them as knowledge graph triplets.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematic course content in LaTeX format, extract the key steps of the mathematical proof in fine grainded detailed steps and structure these steps to form the knowledge graph triplets as the pattern of explanatory chain (reasoning) for this course.\\n\\nFocus on identifying:\\n1. The steps of mathematical proof in informal language\\n2. The relationships between these proof steps as a chain or sequence\\n3. The typical structure and flow of mathematical proofs of this type\\n4. Key steps and their relationships\\n5. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof. Please make sure the final graph is a single connected component and have label of the start and end nodes/entities.\\n\\nExtract triplets in the form <Source Step/Entity, Relationship, Target Step/Entity> that represent this pattern.\\n\\nCourse Content:\\n\\n\\n## Definition\\nAddition of two non-negative integers \\\\( a \\\\) and \\\\( b \\\\), denoted \\\\( a + b \\\\), can be defined recursively as follows:\\n\\n\\n    \\\\item \\\\textbf{Base Case}: If \\\\( b = 0 \\\\), then \\\\( a + b = a \\\\).\\n    \\\\item \\\\textbf{Recursive Case}: If \\\\( b > 0 \\\\), then \\\\( a + b = (a + (b - 1)) + 1 \\\\).\\n\\n\\nThis definition reduces the problem of adding \\\\( a \\\\) and \\\\( b \\\\) to adding \\\\( a \\\\) and \\\\( b - 1 \\\\), incrementing the result by 1. This process continues recursively until the base case is reached.\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='1', name='Base Case', label='Base Case of Addition', type='step', start=True, end=False), Entity(id='2', name='Recursive Case', label='Recursive Case of Addition', type='step', start=False, end=False), Entity(id='3', name='Reduction to Base Case', label='Reduction to Base Case', type='step', start=False, end=False), Entity(id='4', name='Final Result', label='Final Result of Addition', type='step', start=False, end=True)] relations=[Relation(source='1', target='2', type='grounds', name='Defines the base case for addition'), Relation(source='2', target='3', type='explains', name='Describes how to reduce the problem recursively'), Relation(source='3', target='4', type='concludes', name='Leads to the final result of addition')]\n"]}], "source": ["SYSTEM_PROMPT = \"\"\"You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof and relationships in mathematical content and represent them as knowledge graph triplets.\"\"\"\n", "\n", "COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematic course content in LaTeX format, extract the key steps of the mathematical proof in fine grainded detailed steps and structure these steps to form the knowledge graph triplets as the pattern of explanatory chain (reasoning) for this course.\n", "\n", "Focus on identifying:\n", "1. The steps of mathematical proof in informal language\n", "2. The relationships between these proof steps as a chain or sequence\n", "3. The typical structure and flow of mathematical proofs of this type\n", "4. Key steps and their relationships\n", "5. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof. Please make sure the final graph is a single connected component and have label of the start and end nodes/entities.\n", "\n", "Extract triplets in the form <Source Step/Entity, Relationship, Target Step/Entity> that represent this pattern.\n", "\n", "Course Content:\n", "{proof}\n", "\"\"\"\n", "\n", "# Extract the course pattern\n", "course_pattern = extract_triplets(\n", "    proof, \n", "    custom_prompt=COURSE_PATTERN_PROMPT,\n", "    system_message=SYSTEM_PROMPT\n", ")\n", "print(course_pattern)\n", "\n", "# Store the pattern in Neo4j\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"neo4j\", \"password\"))\n", "neo4j.clean_database()\n", "neo4j.store_triplets(course_pattern, \"course_pattern\")"]}, {"cell_type": "markdown", "id": "1efd42b5", "metadata": {}, "source": ["## Answering to the Test Question\n", "\n", "Using only the cosntructed pattern of the course in the Triplet format answer to the bellow math question."]}, {"cell_type": "markdown", "id": "ffd79637", "metadata": {}, "source": ["Now let's see the question in the latex format:"]}, {"cell_type": "code", "execution_count": 4, "id": "8565e6ea", "metadata": {}, "outputs": [{"data": {"text/latex": ["## Question:\n", "\n", "What is the result of \\( 5 + 8 \\)? Please use recursion to solve this problem."], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load a proof example\n", "proof_latex = read_proof(\"../../data/courses/addition/test1.tex\")\n", "\n", "# Extract content between \\begin{document} and \\end{document}\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "proof = proof_latex[start:end].strip()\n", "\n", "# Convert LaTeX to markdown-like format\n", "proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", proof)\n", "proof = re.sub(r\"\\\\subsection\\{([^}]+)\\}\", r\"### \\1\", proof)\n", "\n", "# Display the proof\n", "display(Latex(proof))"]}, {"cell_type": "markdown", "id": "66e5422d", "metadata": {}, "source": ["## Use the course pattern to answer to the question"]}, {"cell_type": "code", "execution_count": 5, "id": "0fd703c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof/question and relationships in mathematical content and represent them as knowledge graph triplets.', additional_kwargs={}, response_metadata={}), HumanMessage(content=\"\\nGiven the following mathematical proof/question and the given pattern of mathematical proof/reasoning extracted from the course, construct a knowledge graph triplet that follows the given pattern and answer to the mathematical question.\\n\\nThe extracted pattern for the reasoning is:\\n```\\nentities=[Entity(id='1', name='Base Case', label='Base Case of Addition', type='step', start=True, end=False), Entity(id='2', name='Recursive Case', label='Recursive Case of Addition', type='step', start=False, end=False), Entity(id='3', name='Reduction to Base Case', label='Reduction to Base Case', type='step', start=False, end=False), Entity(id='4', name='Final Result', label='Final Result of Addition', type='step', start=False, end=True)] relations=[Relation(source='1', target='2', type='grounds', name='Defines the base case for addition'), Relation(source='2', target='3', type='explains', name='Describes how to reduce the problem recursively'), Relation(source='3', target='4', type='concludes', name='Leads to the final result of addition')]\\n```\\nMathematical Proof/Question:\\n```\\n## Question:\\n\\nWhat is the result of \\\\( 5 + 8 \\\\)? Please use recursion to solve this problem.\\n```\\n\\nAnswer to the given math proof/question by extracting triplets in the form <Source Entity, Relationship, Target Entity> that:\\nFocus on identifying:\\n1. The steps of mathematical proof/reasoning in according to the course pattern\\n2. The relationships between these proof/reasoning steps as a chain or sequence\\n3. Key steps and their relationships\\n4. Sometimes the proof/reasoning may be a loop or a cycle that use the same pattern over and over to reach to the final step, please make sure the final graph is a single connected component.\\n4. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof.\\n\", additional_kwargs={}, response_metadata={})]\n"]}], "source": ["SYSTEM_PROMPT = \"\"\"You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof/question and relationships in mathematical content and represent them as knowledge graph triplets.\"\"\"\n", "\n", "PROOF_PATTERN_APPLICATION_PROMPT = \"\"\"\n", "Given the following mathematical proof/question and the given pattern of mathematical proof/reasoning extracted from the course, construct a knowledge graph triplet that follows the given pattern and answer to the mathematical question.\n", "\n", "The extracted pattern for the reasoning is:\n", "```\n", "{course_pattern}\n", "```\n", "Mathematical Proof/Question:\n", "```\n", "{{proof}}\n", "```\n", "\n", "Answer to the given math proof/question by extracting triplets in the form <Source Entity, Relationship, Target Entity> that:\n", "Focus on identifying:\n", "1. The steps of mathematical proof/reasoning in according to the course pattern\n", "2. The relationships between these proof/reasoning steps as a chain or sequence\n", "3. Key steps and their relationships\n", "4. Sometimes the proof/reasoning may be a loop or a cycle that use the same pattern over and over to reach to the final step, please make sure the final graph is a single connected component.\n", "4. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof.\n", "\"\"\"\n", "\n", "# Format the prompt with course_pattern\n", "formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n", "    course_pattern=course_pattern\n", ")\n", "\n", "# Pass the formatted prompt and proof_content to extract_triplets\n", "proof_triplets = extract_triplets(proof,\n", "                                  custom_prompt=formatted_prompt, system_message=SYSTEM_PROMPT)\n", "\n", "# Clean the database to remove any existing graphs\n", "neo4j.clean_database()\n", "\n", "# Store the course pattern graph\n", "neo4j.store_triplets(course_pattern, \"course_pattern\")\n", "\n", "# Store the proof graph as a separate graph\n", "neo4j.store_triplets(proof_triplets, \"proof_example\")\n", "\n", "\n", "# # Extract the course pattern\n", "# course_pattern = extract_triplets(\n", "#     proof, custom_prompt=COURSE_PATTERN_PROMPT, system_message=SYSTEM_PROMPT\n", "# )\n", "# print(course_pattern)\n", "\n", "# Display visualization queries\n", "# print(neo4j.get_visualization_queries())"]}, {"cell_type": "markdown", "id": "03ff9175", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "5d23c448", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "58a55696", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "78696ce8", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}