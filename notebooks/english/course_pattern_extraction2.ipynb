{"cells": [{"cell_type": "markdown", "id": "2a751e2f", "metadata": {}, "source": ["# Course Pattern Extraction and Application\n", "\n", "This notebook implements a two-phase approach for:\n", "1. Extracting graph-based patterns from a course about mathematical induction\n", "2. Using those patterns to construct knowledge graphs for specific proof examples\n", "\n", "---"]}, {"cell_type": "markdown", "id": "9aca23a8", "metadata": {}, "source": ["## Phase 1: Course Pattern Extraction\n", "\n", "First, we'll extract the graph-based pattern from the mathematical induction course."]}, {"cell_type": "code", "execution_count": 1, "id": "8d428c69", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../..\")))"]}, {"cell_type": "code", "execution_count": 2, "id": "eb83149d", "metadata": {}, "outputs": [{"data": {"text/latex": ["\n", "\n", "## What is Mathematical Induction?\n", "Mathematical induction is a powerful proof technique used in mathematics to prove statements that are asserted to be true for all natural numbers. It is especially useful for proving propositions about:\n", "\n", "    \\item Summations and series\n", "    \\item Divisibility properties\n", "    \\item Inequalities\n", "    \\item Combinatorial identities\n", "\n", "\n", "## The Principle of Mathematical Induction\n", "\\begin{theorem}[Principle of Mathematical Induction]\n", "    To prove that a proposition $P(n)$ is true for all natural numbers $n \\geq n_0$, it suffices to:\n", "    \\begin{enumerate}\n", "        \\item \\textbf{Base Case:} Verify $P(n_0)$ is true\n", "        \\item \\textbf{Inductive Step:} Show that if $P(k)$ is true for some arbitrary $k \\geq n_0$ (called the induction hypothesis), then $P(k+1)$ must also be true\n", "    \\end{enumerate}\n", "\\end{theorem}\n", "\n", "## The Domino Analogy\n", "Mathematical induction works like falling dominos:\n", "\n", "    \\item The base case is like knocking over the first domino\n", "    \\item The inductive step ensures each domino will knock over the next one\n", "    \\item Together, these guarantee that all dominos will fall\n", "\n", "\n", "## Key Points to Remember\n", "\n", "    \\item Always verify both the base case and inductive step\n", "    \\item The induction hypothesis is crucial - you must assume $P(k)$ is true\n", "    \\item Mathematical induction proves statements for \\textit{all} natural numbers beyond the base case\n", "    \\item Choose the appropriate base case ($n_0$) for your proposition\n"], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "from src.phase1.extract_triplets import extract_triplets\n", "from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Load the course content\n", "course_latex = read_proof(\"../data/courses/induction/induction.tex\")\n", "\n", "# Extract content between \\begin{document} and \\end{document}\n", "start = course_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = course_latex.find(r\"\\end{document}\")\n", "proof = course_latex[start:end].strip()\n", "\n", "# Convert LaTeX to markdown-like format\n", "proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", proof)\n", "proof = re.sub(r\"\\\\subsection\\{([^}]+)\\}\", r\"### \\1\", proof)\n", "proof = re.sub(r\"\\\\title\\{([^}]+)\\}\", r\"# \\1\", proof)\n", "proof = re.sub(r\"\\\\maketitle\", \"\", proof)\n", "proof = re.sub(r\"\\\\begin{itemize}\", \"\", proof)\n", "proof = re.sub(r\"\\\\end{itemize}\", \"\", proof)\n", "proof = re.sub(r\"\\\\item\\s+\\*\\*([^:]+):\\*\\*\", r\"- **\\1:**\", proof)\n", "\n", "# Display the course content\n", "display(Latex(proof))"]}, {"cell_type": "markdown", "id": "ff16d995", "metadata": {}, "source": ["### Extract Course Pattern\n", "\n", "We'll use a specialized prompt to extract the pattern of mathematical induction from the course content."]}, {"cell_type": "code", "execution_count": 3, "id": "bca4c3d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof and relationships in mathematical content and represent them as knowledge graph triplets.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematic course content in LaTeX format, extract the key steps of the mathematical proof in minimum steps and structure that form the knowledge graph triplets as the pattern of mathematical proofs for this course.\\n\\nFocus on identifying:\\n1. The core steps of mathematical proof\\n2. The relationships between these proof steps\\n3. The typical structure and flow of mathematical proofs of this type\\n4. Key steps and their relationships\\n5. The entities and relationships in the proof\\n6. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof. Please make sure the final graph is a single connected component and have label of the start and end nodes/entities.\\n\\nExtract triplets in the form <Source Step/Entity, Relationship, Target Step/Entity> that represent this pattern.\\n\\nCourse Content:\\n```\\n\\n\\n## What is Mathematical Induction?\\nMathematical induction is a powerful proof technique used in mathematics to prove statements that are asserted to be true for all natural numbers. It is especially useful for proving propositions about:\\n\\n    \\\\item Summations and series\\n    \\\\item Divisibility properties\\n    \\\\item Inequalities\\n    \\\\item Combinatorial identities\\n\\n\\n## The Principle of Mathematical Induction\\n\\\\begin{theorem}[Principle of Mathematical Induction]\\n    To prove that a proposition $P(n)$ is true for all natural numbers $n \\\\geq n_0$, it suffices to:\\n    \\\\begin{enumerate}\\n        \\\\item \\\\textbf{Base Case:} Verify $P(n_0)$ is true\\n        \\\\item \\\\textbf{Inductive Step:} Show that if $P(k)$ is true for some arbitrary $k \\\\geq n_0$ (called the induction hypothesis), then $P(k+1)$ must also be true\\n    \\\\end{enumerate}\\n\\\\end{theorem}\\n\\n## The Domino Analogy\\nMathematical induction works like falling dominos:\\n\\n    \\\\item The base case is like knocking over the first domino\\n    \\\\item The inductive step ensures each domino will knock over the next one\\n    \\\\item Together, these guarantee that all dominos will fall\\n\\n\\n## Key Points to Remember\\n\\n    \\\\item Always verify both the base case and inductive step\\n    \\\\item The induction hypothesis is crucial - you must assume $P(k)$ is true\\n    \\\\item Mathematical induction proves statements for \\\\textit{all} natural numbers beyond the base case\\n    \\\\item Choose the appropriate base case ($n_0$) for your proposition\\n\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='1', name='Base Case', label='Verify Base Case', type='step', start=True, end=False), Entity(id='2', name='Inductive Step', label='Show Inductive Step', type='step', start=False, end=False), Entity(id='3', name='Induction Hypothesis', label='Assume P(k) is true', type='concept', start=False, end=False), Entity(id='4', name='Conclusion', label='Prove P(n) for all n >= n0', type='conclusion', start=False, end=True)] relations=[Relation(source='1', target='2', type='leads to', name='Base Case leads to Inductive Step'), Relation(source='2', target='3', type='uses', name='Inductive Step uses Induction Hypothesis'), Relation(source='3', target='4', type='supports', name='Induction Hypothesis supports Conclusion')]\n"]}], "source": ["SYSTEM_PROMPT = \"\"\"You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof and relationships in mathematical content and represent them as knowledge graph triplets.\"\"\"\n", "\n", "COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematic course content in LaTeX format, extract the key steps of the mathematical proof in minimum steps and structure that form the knowledge graph triplets as the pattern of mathematical proofs for this course.\n", "\n", "Focus on identifying:\n", "1. The core steps of mathematical proof\n", "2. The relationships between these proof steps\n", "3. The typical structure and flow of mathematical proofs of this type\n", "4. Key steps and their relationships\n", "5. The entities and relationships in the proof\n", "6. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof. Please make sure the final graph is a single connected component and have label of the start and end nodes/entities.\n", "\n", "Extract triplets in the form <Source Step/Entity, Relationship, Target Step/Entity> that represent this pattern.\n", "\n", "Course Content:\n", "```\n", "{proof}\n", "```\n", "\"\"\"\n", "\n", "# Extract the course pattern\n", "course_pattern = extract_triplets(\n", "    proof, \n", "    custom_prompt=COURSE_PATTERN_PROMPT,\n", "    system_message=SYSTEM_PROMPT\n", ")\n", "print(course_pattern)\n", "\n", "# Store the pattern in Neo4j\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"neo4j\", \"password\"))\n", "neo4j.clean_database()\n", "neo4j.store_triplets(course_pattern, \"course_pattern\")"]}, {"cell_type": "markdown", "id": "763d980b", "metadata": {}, "source": ["## Phase 2: Proof Pattern Application\n", "\n", "Now we'll use the extracted pattern to construct knowledge graphs for specific proof examples."]}, {"cell_type": "code", "execution_count": 4, "id": "72d488b5", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\title{Proof That the Sum of the First Four Numbers Equals 10}\n", "\\maketitle\n", "\n", "## Question\n", "We want to prove that the sum of the first four natural numbers equals 10:\n", "\\[ 1 + 2 + 3 + 4 = 10 \\]\n", "\n", "## Proof\n", "We compute the sum step by step:\n", "\n", "\\begin{align*}\n", "1 + 2 + 3 + 4 &= (1 + 2) + 3 + 4 && \\text{(Group the first two terms)} \\\\\n", "              &= 3 + 3 + 4         && \\text{(Compute } 1 + 2 = 3\\text{)} \\\\\n", "              &= (3 + 3) + 4        && \\text{(Group the next two terms)} \\\\\n", "              &= 6 + 4             && \\text{(Compute } 3 + 3 = 6\\text{)} \\\\\n", "              &= 10                && \\text{(Compute } 6 + 4 = 10\\text{)}\n", "\\end{align*}\n", "\n", "## Conclusion\n", "Thus, we have shown through step-by-step addition that:\n", "\\[ 1 + 2 + 3 + 4 = 10 \\]"], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load a proof example\n", "proof_latex = read_proof(\"../data/proofs/english/sum_of_first_4_ints/proof1.tex\")\n", "\n", "# Extract content between \\begin{document} and \\end{document}\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "proof = proof_latex[start:end].strip()\n", "\n", "# Convert LaTeX to markdown-like format\n", "proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", proof)\n", "proof = re.sub(r\"\\\\subsection\\{([^}]+)\\}\", r\"### \\1\", proof)\n", "\n", "# Display the proof\n", "display(Latex(proof))"]}, {"cell_type": "code", "execution_count": 5, "id": "4e94bb5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are a helpful assistant that extracts entities and relations from mathematical proofs.', additional_kwargs={}, response_metadata={}), HumanMessage(content=\"\\nGiven the following mathematical proof text in LaTeX format and the given  mathematical proof pattern extracted as a knowledge graph triplets, Construct knowledge graph triplets for the proof that follows the given triplets pattern.\\n\\n- The pattern knowledge graph triplets are:\\n```\\nentities=[Entity(id='1', name='Base Case', label='Verify Base Case', type='step', start=True, end=False), Entity(id='2', name='Inductive Step', label='Show Inductive Step', type='step', start=False, end=False), Entity(id='3', name='Induction Hypothesis', label='Assume P(k) is true', type='concept', start=False, end=False), Entity(id='4', name='Conclusion', label='Prove P(n) for all n >= n0', type='conclusion', start=False, end=True)] relations=[Relation(source='1', target='2', type='leads to', name='Base Case leads to Inductive Step'), Relation(source='2', target='3', type='uses', name='Inductive Step uses Induction Hypothesis'), Relation(source='3', target='4', type='supports', name='Induction Hypothesis supports Conclusion')]\\n```\\n\\n- Proof:\\n```\\n\\\\title{Proof That the Sum of the First Four Numbers Equals 10}\\n\\\\maketitle\\n\\n## Question\\nWe want to prove that the sum of the first four natural numbers equals 10:\\n\\\\[ 1 + 2 + 3 + 4 = 10 \\\\]\\n\\n## Proof\\nWe compute the sum step by step:\\n\\n\\\\begin{align*}\\n1 + 2 + 3 + 4 &= (1 + 2) + 3 + 4 && \\\\text{(Group the first two terms)} \\\\\\\\\\n              &= 3 + 3 + 4         && \\\\text{(Compute } 1 + 2 = 3\\\\text{)} \\\\\\\\\\n              &= (3 + 3) + 4        && \\\\text{(Group the next two terms)} \\\\\\\\\\n              &= 6 + 4             && \\\\text{(Compute } 3 + 3 = 6\\\\text{)} \\\\\\\\\\n              &= 10                && \\\\text{(Compute } 6 + 4 = 10\\\\text{)}\\n\\\\end{align*}\\n\\n## Conclusion\\nThus, we have shown through step-by-step addition that:\\n\\\\[ 1 + 2 + 3 + 4 = 10 \\\\]\\n```\\n\\nFor the given proof, extract triplets in the form <Source Entity, Relationship, Target Entity> that:\\n1. Follow the structure of mathematical proof pattern\\n2. Map to the steps identified in the course pattern\\n3. Capture the specific details and relationships in this proof which may be slightly different from the course pattern but still follow the pattern structure.\\n\\n\", additional_kwargs={}, response_metadata={})]\n", "[SystemMessage(content='You are an expert in mathematical proof analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key steps in a mathematical proof and relationships in mathematical content and represent them as knowledge graph triplets.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematic course content in LaTeX format, extract the key steps of the mathematical proof in minimum steps and structure that form the knowledge graph triplets as the pattern of mathematical proofs for this course.\\n\\nFocus on identifying:\\n1. The core steps of mathematical proof\\n2. The relationships between these proof steps\\n3. The typical structure and flow of mathematical proofs of this type\\n4. Key steps and their relationships\\n5. The entities and relationships in the proof\\n6. The final triplets of the knowledge graph should have a single or multiple start and single or multiple end nodes/entities which are the steps in the proof. Please make sure the final graph is a single connected component and have label of the start and end nodes/entities.\\n\\nExtract triplets in the form <Source Step/Entity, Relationship, Target Step/Entity> that represent this pattern.\\n\\nCourse Content:\\n```\\n\\\\title{Proof That the Sum of the First Four Numbers Equals 10}\\n\\\\maketitle\\n\\n## Question\\nWe want to prove that the sum of the first four natural numbers equals 10:\\n\\\\[ 1 + 2 + 3 + 4 = 10 \\\\]\\n\\n## Proof\\nWe compute the sum step by step:\\n\\n\\\\begin{align*}\\n1 + 2 + 3 + 4 &= (1 + 2) + 3 + 4 && \\\\text{(Group the first two terms)} \\\\\\\\\\n              &= 3 + 3 + 4         && \\\\text{(Compute } 1 + 2 = 3\\\\text{)} \\\\\\\\\\n              &= (3 + 3) + 4        && \\\\text{(Group the next two terms)} \\\\\\\\\\n              &= 6 + 4             && \\\\text{(Compute } 3 + 3 = 6\\\\text{)} \\\\\\\\\\n              &= 10                && \\\\text{(Compute } 6 + 4 = 10\\\\text{)}\\n\\\\end{align*}\\n\\n## Conclusion\\nThus, we have shown through step-by-step addition that:\\n\\\\[ 1 + 2 + 3 + 4 = 10 \\\\]\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='step1', name='Group the first two terms', label='Step 1', type='step', start=True, end=False), Entity(id='step2', name='Compute 1 + 2 = 3', label='Step 2', type='step', start=False, end=False), Entity(id='step3', name='Group the next two terms', label='Step 3', type='step', start=False, end=False), Entity(id='step4', name='Compute 3 + 3 = 6', label='Step 4', type='step', start=False, end=False), Entity(id='step5', name='Compute 6 + 4 = 10', label='Step 5', type='step', start=False, end=True), Entity(id='conclusion', name='Sum of the first four natural numbers equals 10', label='Conclusion', type='conclusion', start=False, end=True)] relations=[Relation(source='step1', target='step2', type='leads to', name='Step 1 leads to Step 2'), Relation(source='step2', target='step3', type='leads to', name='Step 2 leads to Step 3'), Relation(source='step3', target='step4', type='leads to', name='Step 3 leads to Step 4'), Relation(source='step4', target='step5', type='leads to', name='Step 4 leads to Step 5'), Relation(source='step5', target='conclusion', type='concludes', name='Step 5 concludes the proof')]\n", "\n", "## Visualization and Analysis\n", "\n", "You can visualize and analyze the graphs in Neo4j Browser using these queries:\n", "\n", "### View Course Pattern:\n", "```cypher\n", "MATCH p=()-[r]->() \n", "WHERE r.graph_type = 'course_pattern' \n", "RETURN p\n", "```\n", "\n", "### View Proof Graph:\n", "```cypher\n", "MATCH p=()-[r]->() \n", "WHERE r.graph_type = 'proof_example' \n", "RETURN p\n", "```\n", "\n", "### View Both Graphs Side by Side:\n", "```cypher\n", "MATCH pattern=()-[r1]->() \n", "WHERE r1.graph_type = 'course_pattern'\n", "WITH collect(pattern) as patterns\n", "MATCH proof=()-[r2]->() \n", "WHERE r2.graph_type = 'proof_example'\n", "WITH patterns, collect(proof) as proofs\n", "RETURN patterns, proofs\n", "```\n", "\n"]}], "source": ["PROOF_PATTERN_APPLICATION_PROMPT = \"\"\"\n", "Given the following mathematical proof text in LaTeX format and the given  mathematical proof pattern extracted as a knowledge graph triplets, Construct knowledge graph triplets for the proof that follows the given triplets pattern.\n", "\n", "- The pattern knowledge graph triplets are:\n", "```\n", "{course_pattern}\n", "```\n", "\n", "- Proof:\n", "```\n", "{{proof}}\n", "```\n", "\n", "For the given proof, extract triplets in the form <Source Entity, Relationship, Target Entity> that:\n", "1. Follow the structure of mathematical proof pattern\n", "2. Map to the steps identified in the course pattern\n", "3. Capture the specific details and relationships in this proof which may be slightly different from the course pattern but still follow the pattern structure.\n", "\n", "\"\"\"\n", "\n", "# # Format the prompt with course_pattern\n", "# formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n", "#     course_pattern=course_pattern\n", "# )\n", "\n", "# # Pass the formatted prompt and proof_content to extract_triplets\n", "# proof_triplets = extract_triplets(proof, formatted_prompt)\n", "\n", "# # # Apply the pattern to the proof\n", "# # proof_triplets = extract_triplets(proof_content, PROOF_PATTERN_APPLICATION_PROMPT)\n", "\n", "# # Store the proof graph in Neo4j\n", "# neo4j.store_triplets(proof_triplets, \"proof_example\")\n", "\n", "# Format the prompt with course_pattern\n", "formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n", "    course_pattern=course_pattern\n", ")\n", "\n", "# Pass the formatted prompt and proof_content to extract_triplets\n", "proof_triplets = extract_triplets(proof, formatted_prompt)\n", "\n", "# Clean the database to remove any existing graphs\n", "neo4j.clean_database()\n", "\n", "# Store the course pattern graph\n", "neo4j.store_triplets(course_pattern, \"course_pattern\")\n", "\n", "# Store the proof graph as a separate graph\n", "neo4j.store_triplets(proof_triplets, \"proof_example\")\n", "\n", "\n", "# Extract the course pattern\n", "course_pattern = extract_triplets(\n", "    proof, custom_prompt=COURSE_PATTERN_PROMPT, system_message=SYSTEM_PROMPT\n", ")\n", "print(course_pattern)\n", "\n", "# Display visualization queries\n", "print(neo4j.get_visualization_queries())"]}, {"cell_type": "markdown", "id": "1efd42b5", "metadata": {}, "source": ["## Visualization and Analysis\n", "\n", "You can visualize and analyze the graphs in Neo4j Browser using these queries:\n", "\n", "### View Course Pattern:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_type = 'course_pattern' RETURN p\n", "```\n", "\n", "### View Proof Graph:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_type = 'proof_example' RETURN p\n", "```\n", "\n", "### Compare Pattern and Proof:\n", "```cypher\n", "MATCH p=()-[r]->() RETURN p\n", "```"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}