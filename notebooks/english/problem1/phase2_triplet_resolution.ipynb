{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Phase 2: Resolving Triplets into a directed acyclic graphs (DAGs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.phase2.resolve_triplets import resolve_triplets\n", "from src.utils.neo4j_utils import Neo4JUtils\n", "from src.utils.file_utils import read_triplet\n", "\n", "# Load extracted triplets\n", "triplet = read_triplet(\"data/triplets/triplet1.json\")\n", "\n", "# Resolve triplets\n", "resolved_triplet = resolve_triplets(triplet)\n", "\n", "# Store resolved graph in Neo4J\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"fanavaran\", \"fanavaran\"))\n", "for entity in resolved_triplet.entities:\n", "    neo4j.create_node(entity)\n", "for relation in resolved_triplet.relations:\n", "    neo4j.create_relation(relation)\n", "neo4j.close()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}