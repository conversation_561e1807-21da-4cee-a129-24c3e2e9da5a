{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Phase 1: Extracting Triplets from Informal Proofs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add src to the Python Path in the Notebook"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Read Informal Proves\n", "Read the LaTeX file. This file contains the informal proofs of the theorems in the book."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/latex": ["## Question:\n", "\n", "Prove Theorem 2.3(iv): $(A B)^{T}=B^{T} A^{T}$.\n", "\n", "## Answer:\n", "\n", "Let $A=\\left[a_{i k}\\right]$ and $B=\\left[b_{k j}\\right]$. Then the $i j$-entry of $A B$ is\n", "\n", "$$\n", "    a_{i 1} b_{1 j}+a_{i 2} b_{2 j}+\\cdots+a_{i m} b_{m j}\n", "$$\n", "\n", "This is the $j i$-entry (reverse order) of $(A B)^{T}$. Now column $j$ of $B$ becomes row $j$ of $B^{T}$, and row $i$ of $A$ becomes column $i$ of $A^{T}$. Thus, the $i j$-entry of $B^{T} A^{T}$ is\n", "\n", "$$\n", "    \\left[b_{1 j}, b_{2 j}, \\ldots, b_{m j}\\right]\\left[a_{i 1}, a_{i 2}, \\ldots, a_{i m}\\right]^{T}=b_{1 j} a_{i 1}+b_{2 j} a_{i 2}+\\cdots+b_{m j} a_{i m}\n", "$$\n", "\n", "Thus, $(A B)^{T}=B^{T} A^{T}$ on because the corresponding entries are equal."], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "\n", "# Load LaTeX proof\n", "proof_latex = read_proof(\"../../data/proofs/english/problem1/proof1.tex\")\n", "\n", "# Find the start and end positions\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "\n", "# Extract the content between \\begin{document} and \\end{document}\n", "informal_proof = proof_latex[start:end].strip()\n", "\n", "\n", "# Replace any \\section{...} with ## ...\n", "informal_proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", informal_proof)\n", "\n", "# Display the LaTeX content\n", "display(Latex(informal_proof))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Extract Triplet proofs from Informal Proofs"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["Triplet(entities=[Entity(id='theorem_2.3_iv', name='Theorem 2.3(iv)', label='Theorem', type='Mathematical Statement'), Entity(id='matrix_A', name='Matrix A', label='Matrix', type='Matrix'), Entity(id='matrix_B', name='Matrix B', label='Matrix', type='Matrix'), Entity(id='entry_AB', name='Entry of AB', label='Matrix Entry', type='Matrix Entry'), Entity(id='entry_AT', name='Entry of A^T', label='Matrix Entry', type='Matrix Entry'), Entity(id='entry_BT', name='Entry of B^T', label='Matrix Entry', type='Matrix Entry'), Entity(id='entry_BT_AT', name='Entry of B^T A^T', label='Matrix Entry', type='Matrix Entry'), Entity(id='equality_AB_BT_AT', name='Equality of (AB)^T and B^T A^T', label='Equality', type='Mathematical Statement')], relations=[Relation(source='theorem_2.3_iv', target='equality_AB_BT_AT', type='concludes', name='proves'), Relation(source='matrix_A', target='entry_AT', type='transforms', name='transforms to'), Relation(source='matrix_B', target='entry_BT', type='transforms', name='transforms to'), Relation(source='entry_AB', target='entry_BT_AT', type='corresponds', name='corresponds to'), Relation(source='entry_AB', target='equality_AB_BT_AT', type='grounds', name='grounds the proof'), Relation(source='entry_BT', target='entry_BT_AT', type='corresponds', name='corresponds to')])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.phase1.extract_triplets import extract_triplets\n", "\n", "# Extract triplets\n", "triplet = extract_triplets(informal_proof)\n", "triplet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Store Triplets into Neo4J DB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Initialize Neo4JUtils\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"fanavaran\", \"fanavaran\"))\n", "\n", "# Clean the database (delete all nodes and relationships)\n", "neo4j.clean_database()\n", "\n", "# Add nodes and relationships with step tracking\n", "for entity in triplet.entities:\n", "    neo4j.create_node(entity)  # Uses the current step (default is 0)\n", "for relation in triplet.relations:\n", "    neo4j.create_relation(relation)  # Uses the current step (default is 0)\n", "\n", "# Increment the step counter for the next set of changes\n", "neo4j.increment_step()\n", "\n", "# Clean the database (delete nodes and relationships with step > 1)\n", "neo4j.clean_database(step=1)\n", "\n", "# Add or modify nodes and relationships in the next step\n", "# Example:\n", "# neo4j.create_node(new_entity)  # This will use the updated step counter (1)\n", "# neo4j.create_relation(new_relation)  # This will use the updated step counter (1)\n", "\n", "# Close the connection\n", "neo4j.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}