{"cells": [{"cell_type": "markdown", "id": "2a751e2f", "metadata": {}, "source": ["# Course Pattern Extraction and Application\n", "\n", "This notebook implements a two-phase approach for:\n", "1. Extracting graph-based patterns from a course about mathematical induction\n", "2. Using those patterns to construct knowledge graphs for specific proof examples\n", "\n", "---"]}, {"cell_type": "markdown", "id": "9aca23a8", "metadata": {}, "source": ["## Phase 1: Course Pattern Extraction\n", "\n", "First, we'll extract the graph-based pattern from the mathematical induction course."]}, {"cell_type": "code", "execution_count": 1, "id": "8d428c69", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "code", "execution_count": 2, "id": "eb83149d", "metadata": {}, "outputs": [], "source": ["from IPython.display import display, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "from src.phase1.extract_triplets import extract_triplets\n", "from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Load the course 1 content\n", "course_latex_1 = read_proof(\"../../data/courses/mixed/prob1_course.tex\")\n", "\n", "# Load the course 2 content\n", "course_latex_2 = read_proof(\"../../data/courses/mixed/prob2_course.tex\")\n", "\n", "# Load the course 3 content\n", "course_latex_3 = read_proof(\"../../data/courses/mixed/prob3_course.tex\")"]}, {"cell_type": "markdown", "id": "ff16d995", "metadata": {}, "source": ["### Extract Course Pattern\n", "\n", "We'll use a specialized prompt to extract the pattern of mathematical induction from the course content."]}, {"cell_type": "code", "execution_count": 3, "id": "bca4c3d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical proof/reasoning analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key detailed steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\\n\\nFocus on identifying:\\n1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\\n2. The precise mathematical operations performed at each step\\n3. The exact sequence of operations, with clear predecessor-successor relationships\\n4. Intermediate results at each calculation stage\\n5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\\n\\nIMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\\n\\nThe final knowledge graph MUST:\\n1. Have clearly marked START node(s) (the initial problem statement)\\n2. Have clearly marked END node(s) (the final result)\\n3. Include ALL intermediate calculation steps with no gaps in reasoning\\n4. Form a single connected component with a clear directional flow\\n5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\\n\\nExtract triplets in the given form of structured output that represent this detailed calculation process.\\n\\nCourse Content:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{enumitem}\\n\\\\begin{document}\\n\\n\\\\title{Tutorial: Addition of Two Two-Digit Numbers}\\n\\\\date{}\\n\\\\maketitle\\n\\n\\\\section*{Problem A}\\nWhat is $12 + 15$?\\n\\n\\\\section*{Step-by-Step Solution}\\n\\\\begin{enumerate}[label=Step \\\\arabic*:, leftmargin=*]\\n    \\\\item \\\\textbf{Break into digits:}\\n          \\\\[\\n              12 = 10 + 2,\\\\quad 15 = 10 + 5\\n          \\\\]\\n\\n    \\\\item \\\\textbf{Add the units:} $2 + 5 = 7$\\n\\n    \\\\item \\\\textbf{Add the tens:} $10 + 10 = 20$\\n\\n    \\\\item \\\\textbf{Combine the results:} $20 + 7 = 27$\\n\\\\end{enumerate}\\n\\n\\\\section*{Conclusion}\\n\\\\[\\n    12 + 15 = 27\\n\\\\]\\n\\n\\\\end{document}\\n\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='step1', name='Break into digits', label='Step 1', type='step', start=True, end=False), Entity(id='step2', name='Add the units', label='Step 2', type='step', start=False, end=False), Entity(id='step3', name='Add the tens', label='Step 3', type='step', start=False, end=False), Entity(id='step4', name='Combine the results', label='Step 4', type='step', start=False, end=False), Entity(id='conclusion', name='Final result: 12 + 15 = 27', label='Conclusion', type='conclusion', start=False, end=True), Entity(id='digits', name='12 = 10 + 2, 15 = 10 + 5', label='Digits Representation', type='intermediate', start=False, end=False), Entity(id='units_sum', name='2 + 5 = 7', label='Units Sum', type='intermediate', start=False, end=False), Entity(id='tens_sum', name='10 + 10 = 20', label='Tens Sum', type='intermediate', start=False, end=False), Entity(id='final_sum', name='20 + 7 = 27', label='Final Sum', type='intermediate', start=False, end=False)] relations=[Relation(source='step1', target='digits', type='explains', name='Breaks numbers into components'), Relation(source='digits', target='step2', type='uses', name='Uses digit representation for addition'), Relation(source='step2', target='units_sum', type='calculates', name='Calculates sum of units'), Relation(source='units_sum', target='step3', type='uses', name='Uses units sum for further addition'), Relation(source='step3', target='tens_sum', type='calculates', name='Calculates sum of tens'), Relation(source='tens_sum', target='step4', type='uses', name='Uses tens sum for final addition'), Relation(source='step4', target='final_sum', type='calculates', name='Combines results to get final sum'), Relation(source='final_sum', target='conclusion', type='presents', name='Presents final result')]\n", "[SystemMessage(content='You are an expert in mathematical proof/reasoning analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key detailed steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical course content in LaTeX format and a previously extracted calculation pattern, extract a VERY DETAILED step-by-step explanatory chain for this new content.\\n\\nThe previously extracted pattern is:\\n```\\nentities=[Entity(id=\\'step1\\', name=\\'Break into digits\\', label=\\'Step 1\\', type=\\'step\\', start=True, end=False), Entity(id=\\'step2\\', name=\\'Add the units\\', label=\\'Step 2\\', type=\\'step\\', start=False, end=False), Entity(id=\\'step3\\', name=\\'Add the tens\\', label=\\'Step 3\\', type=\\'step\\', start=False, end=False), Entity(id=\\'step4\\', name=\\'Combine the results\\', label=\\'Step 4\\', type=\\'step\\', start=False, end=False), Entity(id=\\'conclusion\\', name=\\'Final result: 12 + 15 = 27\\', label=\\'Conclusion\\', type=\\'conclusion\\', start=False, end=True), Entity(id=\\'digits\\', name=\\'12 = 10 + 2, 15 = 10 + 5\\', label=\\'Digits Representation\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'units_sum\\', name=\\'2 + 5 = 7\\', label=\\'Units Sum\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'tens_sum\\', name=\\'10 + 10 = 20\\', label=\\'Tens Sum\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'final_sum\\', name=\\'20 + 7 = 27\\', label=\\'Final Sum\\', type=\\'intermediate\\', start=False, end=False)] relations=[Relation(source=\\'step1\\', target=\\'digits\\', type=\\'explains\\', name=\\'Breaks numbers into components\\'), Relation(source=\\'digits\\', target=\\'step2\\', type=\\'uses\\', name=\\'Uses digit representation for addition\\'), Relation(source=\\'step2\\', target=\\'units_sum\\', type=\\'calculates\\', name=\\'Calculates sum of units\\'), Relation(source=\\'units_sum\\', target=\\'step3\\', type=\\'uses\\', name=\\'Uses units sum for further addition\\'), Relation(source=\\'step3\\', target=\\'tens_sum\\', type=\\'calculates\\', name=\\'Calculates sum of tens\\'), Relation(source=\\'tens_sum\\', target=\\'step4\\', type=\\'uses\\', name=\\'Uses tens sum for final addition\\'), Relation(source=\\'step4\\', target=\\'final_sum\\', type=\\'calculates\\', name=\\'Combines results to get final sum\\'), Relation(source=\\'final_sum\\', target=\\'conclusion\\', type=\\'presents\\', name=\\'Presents final result\\')]\\n```\\n\\nIMPORTANT: The new course (multiplication) builds upon concepts from the previous course (addition). Your extracted knowledge graph MUST maintain this hierarchical relationship by:\\n1. Incorporating the relevant nodes and relationships from the previous pattern when they are used in the new content\\n2. Adding new nodes and relationships specific to the new calculation process\\n3. Creating explicit connections between the previous concepts and the new ones\\n4. Preserving the hierarchical structure where multiplication operations may use addition operations as sub-steps\\n\\nUse this pattern as a guide, but adapt it to the specific calculation process in the new content. You can reuse elements from the previous pattern if they apply to the new content. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish in this new content.\\n\\nFocus on identifying:\\n1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\\n2. The precise mathematical operations performed at each step\\n3. The exact sequence of operations, with clear predecessor-successor relationships\\n4. Intermediate results at each calculation stage\\n5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\\n\\nIMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\\n\\nThe final knowledge graph MUST:\\n1. Have clearly marked START node(s) (the initial problem statement)\\n2. Have clearly marked END node(s) (the final result)\\n3. Include ALL intermediate calculation steps with no gaps in reasoning\\n4. Form a single connected component with a clear directional flow\\n5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\\n6. Show how multiplication operations may use addition operations as sub-components\\n\\nExtract triplets in the given form of structured output that represent this detailed calculation process.\\n\\nNew Course Content:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{enumitem}\\n\\\\begin{document}\\n\\n\\\\title{Tutorial: Multiplication as Repeated Addition}\\n\\\\date{}\\n\\\\maketitle\\n\\n\\\\section*{Problem B}\\nWhat is $3 \\\\times 12$?\\n\\n\\\\section*{Step-by-Step Solution}\\n\\\\begin{enumerate}[label=Step \\\\arabic*:, leftmargin=*]\\n    \\\\item \\\\textbf{Interpret multiplication as repeated addition:}\\n          \\\\[\\n              3 \\\\times 12 = 12 + 12 + 12\\n          \\\\]\\n\\n    \\\\item \\\\textbf{First addition:} $12 + 12 = 24$\\n\\n    \\\\item \\\\textbf{Second addition:} $24 + 12 = 36$\\n\\\\end{enumerate}\\n\\n\\\\section*{Conclusion}\\n\\\\[\\n    3 \\\\times 12 = 36\\n\\\\]\\n\\n\\\\end{document}\\n\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='step1', name='Interpret multiplication as repeated addition', label='Step 1', type='step', start=True, end=False), Entity(id='repeated_addition', name='3 × 12 = 12 + 12 + 12', label='Repeated Addition Representation', type='intermediate', start=False, end=False), Entity(id='step2', name='First addition: 12 + 12 = 24', label='Step 2', type='step', start=False, end=False), Entity(id='first_addition_result', name='12 + 12 = 24', label='First Addition Result', type='intermediate', start=False, end=False), Entity(id='step3', name='Second addition: 24 + 12 = 36', label='Step 3', type='step', start=False, end=False), Entity(id='second_addition_result', name='24 + 12 = 36', label='Second Addition Result', type='intermediate', start=False, end=False), Entity(id='conclusion', name='Final result: 3 × 12 = 36', label='Conclusion', type='conclusion', start=False, end=True), Entity(id='addition_step1', name='Add the units', label='Addition Step 1', type='step', start=False, end=False), Entity(id='addition_step2', name='Add the tens', label='Addition Step 2', type='step', start=False, end=False), Entity(id='addition_step3', name='Combine the results', label='Addition Step 3', type='step', start=False, end=False), Entity(id='digits', name='12 = 10 + 2', label='Digits Representation', type='intermediate', start=False, end=False), Entity(id='units_sum', name='2 + 2 = 4', label='Units Sum', type='intermediate', start=False, end=False), Entity(id='tens_sum', name='10 + 10 = 20', label='Tens Sum', type='intermediate', start=False, end=False), Entity(id='final_sum', name='20 + 4 = 24', label='Final Sum', type='intermediate', start=False, end=False)] relations=[Relation(source='step1', target='repeated_addition', type='explains', name='Interprets multiplication as repeated addition'), Relation(source='repeated_addition', target='step2', type='uses', name='Uses repeated addition representation for calculation'), Relation(source='step2', target='first_addition_result', type='calculates', name='Calculates first addition result'), Relation(source='first_addition_result', target='step3', type='uses', name='Uses first addition result for next addition'), Relation(source='step3', target='second_addition_result', type='calculates', name='Calculates second addition result'), Relation(source='second_addition_result', target='conclusion', type='presents', name='Presents final result'), Relation(source='step1', target='addition_step1', type='uses', name='Uses addition steps from previous course'), Relation(source='addition_step1', target='digits', type='explains', name='Breaks numbers into components for addition'), Relation(source='digits', target='addition_step2', type='uses', name='Uses digit representation for addition'), Relation(source='addition_step2', target='units_sum', type='calculates', name='Calculates sum of units'), Relation(source='units_sum', target='addition_step3', type='uses', name='Uses units sum for final addition'), Relation(source='addition_step3', target='final_sum', type='calculates', name='Combines results to get final sum'), Relation(source='final_sum', target='conclusion', type='presents', name='Presents final result')]\n", "[SystemMessage(content='You are an expert in mathematical proof/reasoning analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key detailed steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical course content in LaTeX format and two previously extracted calculation patterns, extract a VERY DETAILED step-by-step explanatory chain for this new content.\\n\\nThe previously extracted patterns are:\\n\\nPattern 1 (Addition):\\n```\\nentities=[Entity(id=\\'step1\\', name=\\'Break into digits\\', label=\\'Step 1\\', type=\\'step\\', start=True, end=False), Entity(id=\\'step2\\', name=\\'Add the units\\', label=\\'Step 2\\', type=\\'step\\', start=False, end=False), Entity(id=\\'step3\\', name=\\'Add the tens\\', label=\\'Step 3\\', type=\\'step\\', start=False, end=False), Entity(id=\\'step4\\', name=\\'Combine the results\\', label=\\'Step 4\\', type=\\'step\\', start=False, end=False), Entity(id=\\'conclusion\\', name=\\'Final result: 12 + 15 = 27\\', label=\\'Conclusion\\', type=\\'conclusion\\', start=False, end=True), Entity(id=\\'digits\\', name=\\'12 = 10 + 2, 15 = 10 + 5\\', label=\\'Digits Representation\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'units_sum\\', name=\\'2 + 5 = 7\\', label=\\'Units Sum\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'tens_sum\\', name=\\'10 + 10 = 20\\', label=\\'Tens Sum\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'final_sum\\', name=\\'20 + 7 = 27\\', label=\\'Final Sum\\', type=\\'intermediate\\', start=False, end=False)] relations=[Relation(source=\\'step1\\', target=\\'digits\\', type=\\'explains\\', name=\\'Breaks numbers into components\\'), Relation(source=\\'digits\\', target=\\'step2\\', type=\\'uses\\', name=\\'Uses digit representation for addition\\'), Relation(source=\\'step2\\', target=\\'units_sum\\', type=\\'calculates\\', name=\\'Calculates sum of units\\'), Relation(source=\\'units_sum\\', target=\\'step3\\', type=\\'uses\\', name=\\'Uses units sum for further addition\\'), Relation(source=\\'step3\\', target=\\'tens_sum\\', type=\\'calculates\\', name=\\'Calculates sum of tens\\'), Relation(source=\\'tens_sum\\', target=\\'step4\\', type=\\'uses\\', name=\\'Uses tens sum for final addition\\'), Relation(source=\\'step4\\', target=\\'final_sum\\', type=\\'calculates\\', name=\\'Combines results to get final sum\\'), Relation(source=\\'final_sum\\', target=\\'conclusion\\', type=\\'presents\\', name=\\'Presents final result\\')]\\n```\\n\\nPattern 2 (Multiplication):\\n```\\nentities=[Entity(id=\\'step1\\', name=\\'Interpret multiplication as repeated addition\\', label=\\'Step 1\\', type=\\'step\\', start=True, end=False), Entity(id=\\'repeated_addition\\', name=\\'3 × 12 = 12 + 12 + 12\\', label=\\'Repeated Addition Representation\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'step2\\', name=\\'First addition: 12 + 12 = 24\\', label=\\'Step 2\\', type=\\'step\\', start=False, end=False), Entity(id=\\'first_addition_result\\', name=\\'12 + 12 = 24\\', label=\\'First Addition Result\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'step3\\', name=\\'Second addition: 24 + 12 = 36\\', label=\\'Step 3\\', type=\\'step\\', start=False, end=False), Entity(id=\\'second_addition_result\\', name=\\'24 + 12 = 36\\', label=\\'Second Addition Result\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'conclusion\\', name=\\'Final result: 3 × 12 = 36\\', label=\\'Conclusion\\', type=\\'conclusion\\', start=False, end=True), Entity(id=\\'addition_step1\\', name=\\'Add the units\\', label=\\'Addition Step 1\\', type=\\'step\\', start=False, end=False), Entity(id=\\'addition_step2\\', name=\\'Add the tens\\', label=\\'Addition Step 2\\', type=\\'step\\', start=False, end=False), Entity(id=\\'addition_step3\\', name=\\'Combine the results\\', label=\\'Addition Step 3\\', type=\\'step\\', start=False, end=False), Entity(id=\\'digits\\', name=\\'12 = 10 + 2\\', label=\\'Digits Representation\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'units_sum\\', name=\\'2 + 2 = 4\\', label=\\'Units Sum\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'tens_sum\\', name=\\'10 + 10 = 20\\', label=\\'Tens Sum\\', type=\\'intermediate\\', start=False, end=False), Entity(id=\\'final_sum\\', name=\\'20 + 4 = 24\\', label=\\'Final Sum\\', type=\\'intermediate\\', start=False, end=False)] relations=[Relation(source=\\'step1\\', target=\\'repeated_addition\\', type=\\'explains\\', name=\\'Interprets multiplication as repeated addition\\'), Relation(source=\\'repeated_addition\\', target=\\'step2\\', type=\\'uses\\', name=\\'Uses repeated addition representation for calculation\\'), Relation(source=\\'step2\\', target=\\'first_addition_result\\', type=\\'calculates\\', name=\\'Calculates first addition result\\'), Relation(source=\\'first_addition_result\\', target=\\'step3\\', type=\\'uses\\', name=\\'Uses first addition result for next addition\\'), Relation(source=\\'step3\\', target=\\'second_addition_result\\', type=\\'calculates\\', name=\\'Calculates second addition result\\'), Relation(source=\\'second_addition_result\\', target=\\'conclusion\\', type=\\'presents\\', name=\\'Presents final result\\'), Relation(source=\\'step1\\', target=\\'addition_step1\\', type=\\'uses\\', name=\\'Uses addition steps from previous course\\'), Relation(source=\\'addition_step1\\', target=\\'digits\\', type=\\'explains\\', name=\\'Breaks numbers into components for addition\\'), Relation(source=\\'digits\\', target=\\'addition_step2\\', type=\\'uses\\', name=\\'Uses digit representation for addition\\'), Relation(source=\\'addition_step2\\', target=\\'units_sum\\', type=\\'calculates\\', name=\\'Calculates sum of units\\'), Relation(source=\\'units_sum\\', target=\\'addition_step3\\', type=\\'uses\\', name=\\'Uses units sum for final addition\\'), Relation(source=\\'addition_step3\\', target=\\'final_sum\\', type=\\'calculates\\', name=\\'Combines results to get final sum\\'), Relation(source=\\'final_sum\\', target=\\'conclusion\\', type=\\'presents\\', name=\\'Presents final result\\')]\\n```\\n\\nIMPORTANT: The new course (distributive property) builds upon concepts from both previous courses (addition and multiplication). Your extracted knowledge graph MUST maintain this hierarchical relationship by:\\n1. Incorporating the relevant nodes and relationships from both previous patterns when they are used in the new content\\n2. Adding new nodes and relationships specific to the new calculation process\\n3. Creating explicit connections between the previous concepts and the new ones\\n4. Preserving the hierarchical structure where distributive property operations may use both multiplication and addition operations as sub-steps\\n\\nUse these patterns as a guide, but adapt them to the specific calculation process in the new content. You can reuse elements from the previous patterns if they apply to the new content. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish in this new content.\\n\\nFocus on identifying:\\n1. Every individual calculation step, no matter how small (e.g., \"Apply distributive property\", \"Multiply terms\", etc.)\\n2. The precise mathematical operations performed at each step\\n3. The exact sequence of operations, with clear predecessor-successor relationships\\n4. Intermediate results at each calculation stage\\n5. The mathematical justification for each step (e.g., \"By the distributive property\", \"By substituting value from step 3\")\\n\\nIMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\\n\\nThe final knowledge graph MUST:\\n1. Have clearly marked START node(s) (the initial problem statement)\\n2. Have clearly marked END node(s) (the final result)\\n3. Include ALL intermediate calculation steps with no gaps in reasoning\\n4. Form a single connected component with a clear directional flow\\n5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"multiplies_terms\", \"simplifies_expression\")\\n6. Show how distributive property operations may use both multiplication and addition operations as sub-components\\n\\nExtract triplets in the given form of structured output that represent this detailed calculation process.\\n\\nNew Course Content:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{enumitem}\\n\\\\begin{document}\\n\\n\\\\title{Tutorial: Distributive Multiplication over Addition}\\n\\\\date{}\\n\\\\maketitle\\n\\n\\\\section*{Problem C}\\nWhat is $3 \\\\times (12 + 15)$?\\n\\n\\\\section*{Step-by-Step Solution}\\n\\nWe will solve this in two phases:\\n\\\\begin{enumerate}[label=Step \\\\arabic*:, leftmargin=*]\\n    \\\\item \\\\textbf{Evaluate the expression inside the parentheses:}\\n          \\\\[\\n              12 + 15 = 27\\n          \\\\]\\n          (As we learned in Problem A)\\n\\n    \\\\item \\\\textbf{Multiply the result by 3:}\\n          \\\\[\\n              3 \\\\times 27 = 27 + 27 + 27\\n          \\\\]\\n\\n    \\\\item \\\\textbf{First addition:} $27 + 27 = 54$\\n\\n    \\\\item \\\\textbf{Second addition:} $54 + 27 = 81$\\n\\\\end{enumerate}\\n\\n\\\\section*{Conclusion}\\n\\\\[\\n    3 \\\\times (12 + 15) = 81\\n\\\\]\\n\\n\\\\end{document}\\n\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='step1', name='Evaluate the expression inside the parentheses', label='Step 1', type='step', start=True, end=False), Entity(id='addition_result', name='12 + 15 = 27', label='Addition Result', type='intermediate', start=False, end=False), Entity(id='step2', name='Multiply the result by 3', label='Step 2', type='step', start=False, end=False), Entity(id='repeated_addition', name='3 × 27 = 27 + 27 + 27', label='Repeated Addition Representation', type='intermediate', start=False, end=False), Entity(id='step3', name='First addition: 27 + 27 = 54', label='Step 3', type='step', start=False, end=False), Entity(id='first_addition_result', name='27 + 27 = 54', label='First Addition Result', type='intermediate', start=False, end=False), Entity(id='step4', name='Second addition: 54 + 27 = 81', label='Step 4', type='step', start=False, end=False), Entity(id='second_addition_result', name='54 + 27 = 81', label='Second Addition Result', type='intermediate', start=False, end=False), Entity(id='conclusion', name='Final result: 3 × (12 + 15) = 81', label='Conclusion', type='conclusion', start=False, end=True), Entity(id='step1_addition', name='Add the units', label='Addition Step 1', type='step', start=False, end=False), Entity(id='step2_addition', name='Add the tens', label='Addition Step 2', type='step', start=False, end=False), Entity(id='step3_addition', name='Combine the results', label='Addition Step 3', type='step', start=False, end=False), Entity(id='digits', name='12 = 10 + 2, 15 = 10 + 5', label='Digits Representation', type='intermediate', start=False, end=False), Entity(id='units_sum', name='2 + 5 = 7', label='Units Sum', type='intermediate', start=False, end=False), Entity(id='tens_sum', name='10 + 10 = 20', label='Tens Sum', type='intermediate', start=False, end=False), Entity(id='final_sum', name='20 + 7 = 27', label='Final Sum', type='intermediate', start=False, end=False)] relations=[Relation(source='step1', target='addition_result', type='calculates', name='Calculates sum inside parentheses'), Relation(source='addition_result', target='step2', type='uses', name='Uses result of addition for multiplication'), Relation(source='step2', target='repeated_addition', type='explains', name='Interprets multiplication as repeated addition'), Relation(source='repeated_addition', target='step3', type='uses', name='Uses repeated addition representation for calculation'), Relation(source='step3', target='first_addition_result', type='calculates', name='Calculates first addition result'), Relation(source='first_addition_result', target='step4', type='uses', name='Uses first addition result for next addition'), Relation(source='step4', target='second_addition_result', type='calculates', name='Calculates second addition result'), Relation(source='second_addition_result', target='conclusion', type='presents', name='Presents final result'), Relation(source='step1', target='step1_addition', type='uses', name='Uses addition steps from previous course'), Relation(source='step1_addition', target='digits', type='explains', name='Breaks numbers into components for addition'), Relation(source='digits', target='step2_addition', type='uses', name='Uses digit representation for addition'), Relation(source='step2_addition', target='units_sum', type='calculates', name='Calculates sum of units'), Relation(source='units_sum', target='step3_addition', type='uses', name='Uses units sum for final addition'), Relation(source='step3_addition', target='final_sum', type='calculates', name='Combines results to get final sum'), Relation(source='final_sum', target='addition_result', type='presents', name='Presents final addition result')]\n"]}], "source": ["SYSTEM_PROMPT = \"\"\"You are an expert in mathematical proof/reasoning analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key detailed steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.\"\"\"\n", "\n", "COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\n", "\n", "Focus on identifying:\n", "1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\n", "2. The precise mathematical operations performed at each step\n", "3. The exact sequence of operations, with clear predecessor-successor relationships\n", "4. Intermediate results at each calculation stage\n", "5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\n", "\n", "IMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\n", "\n", "The final knowledge graph MUST:\n", "1. Have clearly marked START node(s) (the initial problem statement)\n", "2. Have clearly marked END node(s) (the final result)\n", "3. Include ALL intermediate calculation steps with no gaps in reasoning\n", "4. Form a single connected component with a clear directional flow\n", "5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\n", "\n", "Extract triplets in the given form of structured output that represent this detailed calculation process.\n", "\n", "Course Content:\n", "```\n", "{proof}\n", "```\n", "\"\"\"\n", "\n", "\n", "# Extract the course pattern\n", "course_pattern_1 = extract_triplets(\n", "    proof=course_latex_1,\n", "    custom_prompt=COURSE_PATTERN_PROMPT,\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "print(course_pattern_1)\n", "\n", "# Store the pattern in Neo4j\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"neo4j\", \"password\"))\n", "neo4j.clean_database()\n", "neo4j.store_triplets(course_pattern_1, \"course_pattern_1\")\n", "\n", "# Create a prompt for the second course using the first pattern\n", "COURSE_PATTERN_TRANSFER_PROMPT = \"\"\"\n", "Given the following mathematical course content in LaTeX format and a previously extracted calculation pattern, extract a VERY DETAILED step-by-step explanatory chain for this new content.\n", "\n", "The previously extracted pattern is:\n", "```\n", "{previous_pattern}\n", "```\n", "\n", "IMPORTANT: The new course (multiplication) builds upon concepts from the previous course (addition). Your extracted knowledge graph MUST maintain this hierarchical relationship by:\n", "1. Incorporating the relevant nodes and relationships from the previous pattern when they are used in the new content\n", "2. Adding new nodes and relationships specific to the new calculation process\n", "3. Creating explicit connections between the previous concepts and the new ones\n", "4. Preserving the hierarchical structure where multiplication operations may use addition operations as sub-steps\n", "\n", "Use this pattern as a guide, but adapt it to the specific calculation process in the new content. You can reuse elements from the previous pattern if they apply to the new content. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish in this new content.\n", "\n", "Focus on identifying:\n", "1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\n", "2. The precise mathematical operations performed at each step\n", "3. The exact sequence of operations, with clear predecessor-successor relationships\n", "4. Intermediate results at each calculation stage\n", "5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\n", "\n", "IMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\n", "\n", "The final knowledge graph MUST:\n", "1. Have clearly marked START node(s) (the initial problem statement)\n", "2. Have clearly marked END node(s) (the final result)\n", "3. Include ALL intermediate calculation steps with no gaps in reasoning\n", "4. Form a single connected component with a clear directional flow\n", "5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\n", "6. Show how multiplication operations may use addition operations as sub-components\n", "\n", "Extract triplets in the given form of structured output that represent this detailed calculation process.\n", "\n", "New Course Content:\n", "```\n", "{{proof}}\n", "```\n", "\"\"\"\n", "\n", "# Extract the pattern for the second course using the first pattern as a guide\n", "course_pattern_2 = extract_triplets(\n", "    proof=course_latex_2,\n", "    custom_prompt=COURSE_PATTERN_TRANSFER_PROMPT.format(\n", "        previous_pattern=course_pattern_1\n", "    ),\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "print(course_pattern_2)\n", "\n", "# Store the second pattern in Neo4j\n", "neo4j.store_triplets(course_pattern_2, \"course_pattern_2\")\n", "\n", "# Create a prompt for the third course using both previous patterns\n", "COURSE_PATTERN_TRANSFER_PROMPT_3 = \"\"\"\n", "Given the following mathematical course content in LaTeX format and two previously extracted calculation patterns, extract a VERY DETAILED step-by-step explanatory chain for this new content.\n", "\n", "The previously extracted patterns are:\n", "\n", "Pattern 1 (Addition):\n", "```\n", "{pattern_1}\n", "```\n", "\n", "Pattern 2 (Multiplication):\n", "```\n", "{pattern_2}\n", "```\n", "\n", "IMPORTANT: The new course (distributive property) builds upon concepts from both previous courses (addition and multiplication). Your extracted knowledge graph MUST maintain this hierarchical relationship by:\n", "1. Incorporating the relevant nodes and relationships from both previous patterns when they are used in the new content\n", "2. Adding new nodes and relationships specific to the new calculation process\n", "3. Creating explicit connections between the previous concepts and the new ones\n", "4. Preserving the hierarchical structure where distributive property operations may use both multiplication and addition operations as sub-steps\n", "\n", "Use these patterns as a guide, but adapt them to the specific calculation process in the new content. You can reuse elements from the previous patterns if they apply to the new content. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish in this new content.\n", "\n", "Focus on identifying:\n", "1. Every individual calculation step, no matter how small (e.g., \"Apply distributive property\", \"Multiply terms\", etc.)\n", "2. The precise mathematical operations performed at each step\n", "3. The exact sequence of operations, with clear predecessor-successor relationships\n", "4. Intermediate results at each calculation stage\n", "5. The mathematical justification for each step (e.g., \"By the distributive property\", \"By substituting value from step 3\")\n", "\n", "IMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\n", "\n", "The final knowledge graph MUST:\n", "1. Have clearly marked START node(s) (the initial problem statement)\n", "2. Have clearly marked END node(s) (the final result)\n", "3. Include ALL intermediate calculation steps with no gaps in reasoning\n", "4. Form a single connected component with a clear directional flow\n", "5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"multiplies_terms\", \"simplifies_expression\")\n", "6. Show how distributive property operations may use both multiplication and addition operations as sub-components\n", "\n", "Extract triplets in the given form of structured output that represent this detailed calculation process.\n", "\n", "New Course Content:\n", "```\n", "{{proof}}\n", "```\n", "\"\"\"\n", "\n", "# Extract the pattern for the third course using the first and second patterns as guides\n", "course_pattern_3 = extract_triplets(\n", "    proof=course_latex_3,\n", "    custom_prompt=COURSE_PATTERN_TRANSFER_PROMPT_3.format(\n", "        pattern_1=course_pattern_1, pattern_2=course_pattern_2\n", "    ),\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "print(course_pattern_3)\n", "\n", "# Store the third pattern in Neo4j\n", "neo4j.store_triplets(course_pattern_3, \"course_pattern_3\")"]}, {"cell_type": "markdown", "id": "763d980b", "metadata": {}, "source": ["## Phase 2: Create a Pattern from another Pattern\n", "\n", "Now we'll use the extracted pattern 1 to construct knowledge graph for new pattern 2."]}, {"cell_type": "code", "execution_count": 4, "id": "72d488b5", "metadata": {}, "outputs": [], "source": ["# Load the course 1 content\n", "course_latex_1 = read_proof(\"../../data/courses/addition/course.tex\")\n", "\n", "# Load the course 2 content\n", "course_latex_2 = read_proof(\"../../data/courses/multiplication/course.tex\")"]}, {"cell_type": "code", "execution_count": 5, "id": "6a7e0a46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical proof/reasoning analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key detailed steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\\n\\nFocus on identifying:\\n1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\\n2. The precise mathematical operations performed at each step\\n3. The exact sequence of operations, with clear predecessor-successor relationships\\n4. Intermediate results at each calculation stage\\n5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\\n\\nIMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\\n\\nThe final knowledge graph MUST:\\n1. Have clearly marked START node(s) (the initial problem statement)\\n2. Have clearly marked END node(s) (the final result)\\n3. Include ALL intermediate calculation steps with no gaps in reasoning\\n4. Form a single connected component with a clear directional flow\\n5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\\n\\nExtract triplets in the given form of structured output that represent this detailed calculation process.\\n\\nCourse Content:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{amssymb}\\n\\\\title{Introduction to Addition via Recursion}\\n\\\\author{}\\n\\\\date{}\\n\\n\\\\begin{document}\\n\\\\maketitle\\n\\n\\\\section{Recursive Definition}\\nFor non-negative integers \\\\(a\\\\) and \\\\(b\\\\):\\n\\n\\\\[\\n    a + b = \\\\begin{cases}\\n        a                 & \\\\text{if } b = 0 \\\\quad \\\\text{(Base Case)}      \\\\\\\\\\n        (a + (b - 1)) + 1 & \\\\text{if } b > 0 \\\\quad \\\\text{(Recursive Case)}\\n    \\\\end{cases}\\n\\\\]\\n\\n\\\\section{Expanded Recursion Steps}\\nThe recursive case systematically reduces any addition problem to successive simpler cases through these steps:\\n\\n\\\\begin{enumerate}\\n    \\\\item \\\\textbf{Initial Problem}: \\\\(a + b\\\\) where \\\\(b > 0\\\\)\\n\\n    \\\\item \\\\textbf{Recursive Decomposition}:\\n          \\\\[\\n              a + b = (a + \\\\underbrace{(b - 1)}_{\\\\text{Simpler term}}) + 1\\n          \\\\]\\n          This creates:\\n          \\\\begin{itemize}\\n              \\\\item A simpler subproblem: \\\\(a + (b - 1)\\\\)\\n              \\\\item A pending operation: \\\\(+ 1\\\\)\\n          \\\\end{itemize}\\n\\n    \\\\item \\\\textbf{Iterative Reduction}:\\n          Repeat until reaching base case:\\n          \\\\[\\n              \\\\begin{aligned}\\n                   & a + b                              \\\\\\\\\\n                   & \\\\Downarrow                         \\\\\\\\\\n                   & (a + (b-1)) + 1                    \\\\\\\\\\n                   & \\\\Downarrow                         \\\\\\\\\\n                   & ((a + (b-2)) + 1) + 1              \\\\\\\\\\n                   & \\\\Downarrow                         \\\\\\\\\\n                   & \\\\quad \\\\vdots                       \\\\\\\\\\n                   & \\\\Downarrow                         \\\\\\\\\\n                   & (\\\\cdots((a + 0) + 1) + \\\\cdots + 1) \\\\\\\\\\n              \\\\end{aligned}\\n          \\\\]\\n\\n    \\\\item \\\\textbf{Base Case Resolution}:\\n          When \\\\(b - n = 0\\\\):\\n          \\\\[\\n              \\\\underbrace{(\\\\cdots((a + 0)}_{\\\\text{Base case}} \\\\underbrace{+ 1) + \\\\cdots + 1)}_{b \\\\text{ times}}\\n          \\\\]\\n\\n    \\\\item \\\\textbf{Result Construction}:\\n          \\\\[\\n              a + \\\\underbrace{1 + 1 + \\\\cdots + 1}_{b \\\\text{ times}} = a + b\\n          \\\\]\\n\\\\end{enumerate}\\n\\n\\\\section{Example}\\nComplete Recursion Example, for \\\\(3 + 2\\\\):\\n\\n\\\\[\\n    \\\\begin{aligned}\\n        3 + 2 & = (3 + 1) + 1 \\\\quad       & \\\\text{(First decomposition)}  \\\\\\\\\\n              & = ((3 + 0) + 1) + 1 \\\\quad & \\\\text{(Second decomposition)} \\\\\\\\\\n              & = (3 + 1) + 1 \\\\quad       & \\\\text{(Base case applied)}    \\\\\\\\\\n              & = 4 + 1 \\\\quad             & \\\\text{(First increment)}      \\\\\\\\\\n              & = 5 \\\\quad                 & \\\\text{(Final result)}\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\section*{Recursion Pattern}\\nGeneral form for \\\\(a + b\\\\):\\n\\n\\\\[\\n    \\\\begin{aligned}\\n        a + b & = (a + (b-1)) + 1                                       \\\\\\\\\\n              & = ((a + (b-2)) + 1) + 1                                 \\\\\\\\\\n              & \\\\;\\\\;\\\\vdots                                              \\\\\\\\\\n              & = (\\\\cdots((a + 0) + 1) + \\\\cdots + 1)                    \\\\\\\\\\n              & = a + \\\\underbrace{1 + 1 + \\\\cdots + 1}_{b \\\\text{ times}} \\\\\\\\\\n              & = a + b\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\end{document}\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "entities=[Entity(id='step1', name='Initial Problem: a + b', label='Initial Problem', type='step', start=True, end=False), Entity(id='step2', name='Recursive Decomposition: a + b = (a + (b - 1)) + 1', label='Recursive Decomposition', type='step', start=False, end=False), Entity(id='step3', name='Create simpler subproblem: a + (b - 1)', label='Subproblem Creation', type='step', start=False, end=False), Entity(id='step4', name='Pending operation: + 1', label='Pending Operation', type='step', start=False, end=False), Entity(id='step5', name='Iterative Reduction: a + b to (a + (b - 1)) + 1', label='Iterative Reduction', type='step', start=False, end=False), Entity(id='step6', name='Iterate to ((a + (b - 2)) + 1) + 1', label='Iterative Step', type='step', start=False, end=False), Entity(id='step7', name='Continue until reaching base case: (a + 0) + 1 + ... + 1', label='Continue Iteration', type='step', start=False, end=False), Entity(id='step8', name='Base Case Resolution: a + 0', label='Base Case Resolution', type='step', start=False, end=False), Entity(id='step9', name='Result Construction: a + (1 + 1 + ... + 1)', label='Result Construction', type='step', start=False, end=False), Entity(id='step10', name='Final Result: a + b', label='Final Result', type='step', start=False, end=True)] relations=[Relation(source='step1', target='step2', type='transitions_to', name='Initial Problem'), Relation(source='step2', target='step3', type='applies_recursive_decomposition', name='Decomposes into simpler term'), Relation(source='step2', target='step4', type='creates_pending_operation', name='Identifies pending operation'), Relation(source='step2', target='step5', type='iterates', name='Initiates iterative reduction'), Relation(source='step5', target='step6', type='iterates', name='Continues to reduce'), Relation(source='step6', target='step7', type='iterates', name='Continues until base case is reached'), Relation(source='step7', target='step8', type='resolves_base_case', name='Base case resolution'), Relation(source='step8', target='step9', type='constructs_result', name='Constructs final result'), Relation(source='step9', target='step10', type='finalizes', name='Final result obtained')]\n"]}], "source": ["SYSTEM_PROMPT = \"\"\"You are an expert in mathematical proof/reasoning analysis, specializing in extracting structured knowledge graph from mathematical texts. Your task is to identify key detailed steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.\"\"\"\n", "\n", "COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\n", "\n", "Focus on identifying:\n", "1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\n", "2. The precise mathematical operations performed at each step\n", "3. The exact sequence of operations, with clear predecessor-successor relationships\n", "4. Intermediate results at each calculation stage\n", "5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\n", "\n", "IMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\n", "\n", "The final knowledge graph MUST:\n", "1. Have clearly marked START node(s) (the initial problem statement)\n", "2. Have clearly marked END node(s) (the final result)\n", "3. Include ALL intermediate calculation steps with no gaps in reasoning\n", "4. Form a single connected component with a clear directional flow\n", "5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\n", "\n", "Extract triplets in the given form of structured output that represent this detailed calculation process.\n", "\n", "Course Content:\n", "```\n", "{proof}\n", "```\n", "\"\"\"\n", "\n", "\n", "# Extract the course pattern\n", "course_pattern = extract_triplets(\n", "    proof=course_latex_1,\n", "    custom_prompt=COURSE_PATTERN_PROMPT,\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "print(course_pattern)\n", "\n", "# Store the pattern in Neo4j\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"neo4j\", \"password\"))\n", "neo4j.clean_database()\n", "neo4j.store_triplets(course_pattern, \"course_1_pattern\")"]}, {"cell_type": "code", "execution_count": 6, "id": "4e94bb5a", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'proof' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 36\u001b[39m\n\u001b[32m     31\u001b[39m formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n\u001b[32m     32\u001b[39m     course_pattern=course_pattern\n\u001b[32m     33\u001b[39m )\n\u001b[32m     35\u001b[39m \u001b[38;5;66;03m# Pass the formatted prompt and proof_content to extract_triplets\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m proof_triplets = extract_triplets(\u001b[43mproof\u001b[49m, formatted_prompt)\n\u001b[32m     38\u001b[39m \u001b[38;5;66;03m# Clean the database to remove any existing graphs\u001b[39;00m\n\u001b[32m     39\u001b[39m neo4j.clean_database()\n", "\u001b[31mNameError\u001b[39m: name 'proof' is not defined"]}], "source": ["PROOF_PATTERN_APPLICATION_PROMPT = \"\"\"\n", "Given the following mathematical proof and the given pattern of mathematical proof extracted from the course, construct a knowledge graph that follows the given pattern.\n", "\n", "The pattern components are:\n", "{course_pattern}\n", "\n", "For the given proof, extract triplets in the form <Source Entity, Relationship, Target Entity> that:\n", "1. Follow the structure of mathematical proof pattern\n", "2. Map to the steps identified in the course pattern\n", "3. Capture the specific details and relationships in this proof which may be different from the course pattern\n", "\n", "Proof:\n", "{{proof}}\n", "\"\"\"\n", "\n", "# # Format the prompt with course_pattern\n", "# formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n", "#     course_pattern=course_pattern\n", "# )\n", "\n", "# # Pass the formatted prompt and proof_content to extract_triplets\n", "# proof_triplets = extract_triplets(proof, formatted_prompt)\n", "\n", "# # # Apply the pattern to the proof\n", "# # proof_triplets = extract_triplets(proof_content, PROOF_PATTERN_APPLICATION_PROMPT)\n", "\n", "# # Store the proof graph in Neo4j\n", "# neo4j.store_triplets(proof_triplets, \"proof_example\")\n", "\n", "# Format the prompt with course_pattern\n", "formatted_prompt = PROOF_PATTERN_APPLICATION_PROMPT.format(\n", "    course_pattern=course_pattern\n", ")\n", "\n", "# Pass the formatted prompt and proof_content to extract_triplets\n", "proof_triplets = extract_triplets(proof, formatted_prompt)\n", "\n", "# Clean the database to remove any existing graphs\n", "neo4j.clean_database()\n", "\n", "# Store the course pattern graph\n", "neo4j.store_triplets(course_pattern, \"course_pattern\")\n", "\n", "# Store the proof graph as a separate graph\n", "neo4j.store_triplets(proof_triplets, \"proof_example\")\n", "\n", "\n", "# Extract the course pattern\n", "course_pattern = extract_triplets(\n", "    proof, custom_prompt=COURSE_PATTERN_PROMPT, system_message=SYSTEM_PROMPT\n", ")\n", "print(course_pattern)\n", "\n", "# Display visualization queries\n", "print(neo4j.get_visualization_queries())"]}, {"cell_type": "markdown", "id": "1efd42b5", "metadata": {}, "source": ["## Visualization and Analysis\n", "\n", "You can visualize and analyze the graphs in Neo4j Browser using these queries:\n", "\n", "### View Course Pattern:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_type = 'course_pattern' RETURN p\n", "```\n", "\n", "### View Proof Graph:\n", "```cypher\n", "MATCH p=()-[r]->() WHERE r.graph_type = 'proof_example' RETURN p\n", "```\n", "\n", "### Compare Pattern and Proof:\n", "```cypher\n", "MATCH p=()-[r]->() RETURN p\n", "```"]}, {"cell_type": "markdown", "id": "03ff9175", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "5d23c448", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "58a55696", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "78696ce8", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}