{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Phase 1: Extracting Triplets from Informal Proofs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add src to the Python Path in the Notebook"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Read Informal Proves\n", "Read the LaTeX file. This file contains the informal proofs of the theorems in the book."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/latex": ["# Proof: \\( 8 \\) Divides the Difference of Squares of Two Odd Numbers\n", "\n", "\n", "## Theorem\n", "Let \\( a \\) and \\( b \\) be odd integers. Then:\n", "\\[\n", "    8 \\mid (a^2 - b^2)\n", "\\]\n", "\n", "## Proof\n", "We prove this by expressing odd numbers in their general form and simplifying.\n", "\n", "### Step 1: Representation of Odd Numbers\n", "Any odd integer can be written as:\n", "\\[\n", "    a = 2k + 1, \\quad b = 2m + 1 \\quad \\text{where } k, m \\in \\mathbb{Z}.\n", "\\]\n", "\n", "### Step 2: <PERSON>mp<PERSON> \\( a^2 - b^2 \\)\n", "Using the difference of squares:\n", "\\[\n", "    a^2 - b^2 = (a - b)(a + b).\n", "\\]\n", "Substitute \\( a \\) and \\( b \\):\n", "\\[\n", "    a^2 - b^2 = (2k + 1 - 2m - 1)(2k + 1 + 2m + 1) = 2(k - m) \\cdot 2(k + m + 1).\n", "\\]\n", "Simplify:\n", "\\[\n", "    a^2 - b^2 = 4(k - m)(k + m + 1).\n", "\\]\n", "\n", "### Step 3: Divisibility by 8\n", "We show that \\( 4(k - m)(k + m + 1) \\) is divisible by 8:\n", "\n", "    \\item **Case 1**: If \\( k - m \\) is even, then \\( (k - m) = 2n \\) for some \\( n \\in \\mathbb{Z} \\). Thus:\n", "          \\[\n", "              a^2 - b^2 = 4(2n)(k + m + 1) = 8n(k + m + 1).\n", "          \\]\n", "    \\item **Case 2**: If \\( k - m \\) is odd, then \\( k + m + 1 \\) must be even (since the sum of an odd and even term is even). Let \\( k + m + 1 = 2n \\). Thus:\n", "          \\[\n", "              a^2 - b^2 = 4(k - m)(2n) = 8n(k - m).\n", "          \\]\n", "\n", "In both cases, \\( a^2 - b^2 \\) is divisible by 8.\n", "\n", "### Conclusion\n", "For any two odd integers \\( a \\) and \\( b \\), \\( 8 \\) divides \\( a^2 - b^2 \\)."], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "\n", "# Load LaTeX proof\n", "proof_latex = read_proof(\"../../data/proofs/english/divisible_by_eight/proof1.tex\")\n", "\n", "# Find the start and end positions\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "\n", "# Extract the content between \\begin{document} and \\end{document}\n", "informal_proof = proof_latex[start:end].strip()\n", "\n", "# Replace any \\section{...} with ## ...\n", "informal_proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\subsection\\{([^}]+)\\}\", r\"### \\1\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\title\\{([^}]+)\\}\", r\"# \\1\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\maketitle\", \"\", informal_proof)\n", "\n", "# Replace \\begin{itemize} and \\end{itemize} with Markdown-style lists\n", "informal_proof = re.sub(r\"\\\\begin{itemize}\", \"\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\end{itemize}\", \"\", informal_proof)\n", "informal_proof = re.sub(r\"\\\\item\\s+\\*\\*([^:]+):\\*\\*\", r\"- **\\1:**\", informal_proof)\n", "\n", "# Display the LaTeX content\n", "display(Latex(informal_proof))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Extract Triplet proofs from Informal Proofs"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Failed to multipart ingest runs: langsmith.utils.LangSmithError: Failed to POST https://api.smith.langchain.com/runs/multipart in LangSmith API. HTTPError('403 Client Error: Forbidden for url: https://api.smith.langchain.com/runs/multipart', '{\"error\":\"Forbidden\"}\\n')\n"]}, {"data": {"text/plain": ["Triplet(entities=[Entity(id='1', name='a', label='Odd Integer', type='Variable'), Entity(id='2', name='b', label='Odd Integer', type='Variable'), Entity(id='3', name='k', label='Integer', type='Variable'), Entity(id='4', name='m', label='Integer', type='Variable'), Entity(id='5', name='n', label='Integer', type='Variable'), Entity(id='6', name='8', label='Integer', type='Constant'), Entity(id='7', name='a^2 - b^2', label='Difference of Squares', type='Expression'), Entity(id='8', name='4(k - m)(k + m + 1)', label='Expression for Difference of Squares', type='Expression'), Entity(id='9', name='8n(k + m + 1)', label='Expression for Case 1', type='Expression'), Entity(id='10', name='8n(k - m)', label='Expression for Case 2', type='Expression')], relations=[Relation(source='1', target='3', type='represents', name='Representation of a'), Relation(source='2', target='4', type='represents', name='Representation of b'), Relation(source='3', target='5', type='is an element of', name='k and m are integers'), Relation(source='6', target='7', type='divides', name='8 divides the difference of squares'), Relation(source='7', target='8', type='is expressed as', name='Difference of squares expression'), Relation(source='8', target='9', type='is divisible by', name='Case 1 shows divisibility by 8'), Relation(source='8', target='10', type='is divisible by', name='Case 2 shows divisibility by 8'), Relation(source='1', target='2', type='is a pair of', name='a and b are odd integers'), Relation(source='6', target='7', type='concludes', name='Conclusion of the proof')])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["Failed to send compressed multipart ingest: langsmith.utils.LangSmithError: Failed to POST https://api.smith.langchain.com/runs/multipart in LangSmith API. HTTPError('403 Client Error: Forbidden for url: https://api.smith.langchain.com/runs/multipart', '{\"error\":\"Forbidden\"}\\n')\n"]}], "source": ["from src.phase1.extract_triplets import extract_triplets\n", "\n", "# Extract triplets\n", "triplet = extract_triplets(informal_proof)\n", "triplet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Store Triplets into Neo4J DB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Initialize Neo4JUtils\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"fanavaran\", \"fanavaran\"))\n", "\n", "# Clean the database (delete all nodes and relationships)\n", "neo4j.clean_database()\n", "\n", "# Add nodes and relationships with step tracking\n", "for entity in triplet.entities:\n", "    neo4j.create_node(entity)  # Uses the current step (default is 0)\n", "for relation in triplet.relations:\n", "    neo4j.create_relation(relation)  # Uses the current step (default is 0)\n", "\n", "# Increment the step counter for the next set of changes\n", "neo4j.increment_step()\n", "\n", "# Clean the database (delete nodes and relationships with step > 1)\n", "neo4j.clean_database(step=1)\n", "\n", "# Add or modify nodes and relationships in the next step\n", "# Example:\n", "# neo4j.create_node(new_entity)  # This will use the updated step counter (1)\n", "# neo4j.create_relation(new_relation)  # This will use the updated step counter (1)\n", "\n", "# Close the connection\n", "neo4j.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}