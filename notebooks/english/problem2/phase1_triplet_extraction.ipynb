{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Phase 1: Extracting Triplets from Informal Proofs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add src to the Python Path in the Notebook"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Read Informal Proves\n", "Read the LaTeX file. This file contains the informal proofs of the theorems in the book."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/latex": ["## Question:\n", "\n", "Let $A=\\left[a_{i j}\\right]$ and $B=\\left[b_{i j}\\right]$ be upper triangular matrices. Prove that $A B$ is upper triangular with diagonal $a_{11} b_{11}, a_{22} b_{22}, \\ldots, a_{n n} b_{n n}$.\n", "\n", "## Answer:\n", "\n", "Let $A B=\\left[c_{i j}\\right]$. Then $c_{i j}=\\sum_{k=1}^{n} a_{i k} b_{k j}$ and $c_{i i}=\\sum_{k=1}^{n} a_{i k} b_{k i}$. Suppose $i>j$. Then, for any $k$, either $i>k$ or $k>j$, so that either $a_{i k}=0$ or $b_{k j}=0$. Thus, $c_{i j}=0$, and $A B$ is upper triangular. Suppose $i=j$. Then, for $k<i$, we have $a_{i k}=0$; and, for $k>i$, we have $b_{k i}=0$. Hence, $c_{i i}=a_{i i} b_{i i}$, as claimed. [This proves one part of Theorem 2.5(i); the statements for $A+B$ and $k A$ are left as exercises.]"], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "\n", "# Load LaTeX proof\n", "proof_latex = read_proof(\"../../data/proofs/english/problem2/proof1.tex\")\n", "\n", "# Find the start and end positions\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "\n", "# Extract the content between \\begin{document} and \\end{document}\n", "informal_proof = proof_latex[start:end].strip()\n", "\n", "\n", "# Replace any \\section{...} with ## ...\n", "informal_proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", informal_proof)\n", "\n", "# Display the LaTeX content\n", "display(Latex(informal_proof))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Extract Triplet proofs from Informal Proofs"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["Triplet(entities=[Entity(id='A', name='A', label='Matrix A', type='Matrix'), Entity(id='B', name='B', label='Matrix B', type='Matrix'), Entity(id='c_{ij}', name='c_{ij}', label='Element of product matrix', type='Element'), Entity(id='a_{ij}', name='a_{ij}', label='Element of matrix A', type='Element'), Entity(id='b_{ij}', name='b_{ij}', label='Element of matrix B', type='Element'), Entity(id='Theorem 2.5(i)', name='Theorem 2.5(i)', label='Theorem 2.5(i)', type='Theorem')], relations=[Relation(source='A', target='B', type='proves', name='Matrix multiplication of upper triangular matrices results in upper triangular matrix'), Relation(source='c_{ij}', target='a_{ik}', type='defines', name='Element of product matrix defined by elements of A and B'), Relation(source='c_{ij}', target='b_{kj}', type='defines', name='Element of product matrix defined by elements of A and B'), Relation(source='c_{ii}', target='a_{ii}', type='defines', name='Diagonal element of product matrix defined by diagonal elements of A and B'), Relation(source='c_{ii}', target='b_{ii}', type='defines', name='Diagonal element of product matrix defined by diagonal elements of A and B'), Relation(source='A B', target='Theorem 2.5(i)', type='concludes', name='Product of A and B is upper triangular as stated in Theorem 2.5(i)')])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.phase1.extract_triplets import extract_triplets\n", "\n", "# Extract triplets\n", "triplet = extract_triplets(informal_proof)\n", "triplet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Store Triplets into Neo4J DB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Initialize Neo4JUtils\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"fanavaran\", \"fanavaran\"))\n", "\n", "# Clean the database (delete all nodes and relationships)\n", "neo4j.clean_database()\n", "\n", "# Add nodes and relationships with step tracking\n", "for entity in triplet.entities:\n", "    neo4j.create_node(entity)  # Uses the current step (default is 0)\n", "for relation in triplet.relations:\n", "    neo4j.create_relation(relation)  # Uses the current step (default is 0)\n", "\n", "# Increment the step counter for the next set of changes\n", "neo4j.increment_step()\n", "\n", "# Clean the database (delete nodes and relationships with step > 1)\n", "neo4j.clean_database(step=1)\n", "\n", "# Add or modify nodes and relationships in the next step\n", "# Example:\n", "# neo4j.create_node(new_entity)  # This will use the updated step counter (1)\n", "# neo4j.create_relation(new_relation)  # This will use the updated step counter (1)\n", "\n", "# Close the connection\n", "neo4j.close()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}