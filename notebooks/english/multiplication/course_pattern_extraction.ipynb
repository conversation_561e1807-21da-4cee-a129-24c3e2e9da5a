```python
# First, let's import the necessary libraries and set up our environment
%load_ext autoreload
%autoreload 2
import sys
import os

# Add the project root directory to the Python path
sys.path.append(os.path.abspath(os.path.join("../../..")))

from IPython.display import display, Latex
import re
from src.utils.file_utils import read_proof
from src.phase1.extract_triplets import extract_triplets
from src.utils.neo4j_utils import Neo4JUtils

# Load the course content for addition and multiplication
addition_course_latex = read_proof("../../data/courses/addition/course_2.tex")
multiplication_course_latex = read_proof("../../data/courses/multiplication/course_2.tex")
```

```markdown
# Hierarchical Pattern Extraction for Mathematical Operations

In this notebook, we'll extract knowledge graph patterns for mathematical operations in a hierarchical manner:

1. First, we'll extract the pattern for "Addition by Recursion" from the addition course
2. Then, we'll use this pattern to inform the extraction of the "Multiplication by Recursion" pattern
3. This approach will create a hierarchical knowledge representation where multiplication builds upon addition

This hierarchical approach reflects the natural relationship between mathematical operations, where multiplication can be defined in terms of repeated addition.
```

```python
# Define the system prompt for our LLM
SYSTEM_PROMPT = """You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning."""

# Define the prompt for extracting the addition course pattern
ADDITION_COURSE_PATTERN_PROMPT = """
Given the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.

Focus on identifying:
1. Every individual calculation step, no matter how small (e.g., "Add 2 to both sides", "Apply distributive property", etc.)
2. The precise mathematical operations performed at each step
3. The exact sequence of operations, with clear predecessor-successor relationships
4. Intermediate results at each calculation stage
5. The mathematical justification for each step (e.g., "By the associative property", "By substituting value from step 3")

IMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.

The final knowledge graph MUST:
1. Have clearly marked START node(s) (the initial example statement)
2. Have clearly marked END node(s) (the final result)
3. Include ALL intermediate calculation steps with no gaps in reasoning
4. Form a single connected component with a clear directional flow
5. Use relationship types that precisely describe the mathematical operation performed (e.g., "applies_distributive_property", "substitutes_value", "simplifies_expression")

Extract triplets in the given form of structured output that represent this detailed calculation process.

Note:
- Entities or Nodes should be specified with short mathematical expressions.
- Relations at this mathematical calculation graph is only grounding or simple reasoning connecting one expression to another.
- Steps are as much as possible explaining all the minor steps of the reasoning of the calculation.

Course Content:
```
{course_latex}
```
"""

# Extract the addition course pattern
addition_course_pattern = extract_triplets(
    custom_prompt=ADDITION_COURSE_PATTERN_PROMPT.format(course_latex=addition_course_latex),
    system_message=SYSTEM_PROMPT,
)
print("Addition Course Pattern:")
print(addition_course_pattern)
```

```markdown
## Hierarchical Pattern Extraction for Multiplication

Now that we have extracted the pattern for addition by recursion, we'll use it to inform the extraction of the multiplication pattern. 

The key insight is that multiplication can be defined as repeated addition, so the multiplication pattern should incorporate the addition pattern as a sub-component. This creates a hierarchical relationship between the two operations.
```

```python
# Define the prompt for extracting the multiplication course pattern using the addition pattern
MULTIPLICATION_COURSE_PATTERN_PROMPT = """
Given the following mathematical course content in LaTeX format and a previously extracted addition pattern, extract a VERY DETAILED step-by-step explanatory chain for multiplication by recursion. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.

The previously extracted addition pattern is:
```
{addition_pattern}
```

IMPORTANT: Multiplication builds upon addition. Your extracted knowledge graph MUST maintain this hierarchical relationship by:
1. Incorporating the addition pattern as a sub-component of the multiplication process
2. Showing how multiplication operations decompose into addition operations
3. Creating explicit connections between multiplication steps and their corresponding addition steps
4. Preserving the hierarchical structure where multiplication is defined in terms of addition

Focus on identifying:
1. Every individual calculation step in the multiplication process
2. How multiplication recursively decomposes into simpler multiplication problems
3. How the base case of multiplication connects to addition
4. The precise sequence of operations with clear predecessor-successor relationships
5. Intermediate results at each calculation stage

The final knowledge graph MUST:
1. Have clearly marked START node(s) (the initial multiplication problem)
2. Have clearly marked END node(s) (the final result)
3. Include ALL intermediate calculation steps with no gaps in reasoning
4. Form a single connected component with a clear directional flow
5. Use relationship types that precisely describe the mathematical operations
6. Show the hierarchical relationship between multiplication and addition

Extract triplets in the given form of structured output that represent this detailed calculation process.

Note:
- Entities or Nodes should be specified with short mathematical expressions.
- Relations should clearly indicate both multiplication-specific operations and how they connect to addition operations.
- Steps should explain all the minor steps of the reasoning, including how multiplication steps decompose into addition steps.

Course Content:
```
{course_latex}
```
"""

# Extract the multiplication course pattern using the addition pattern
multiplication_course_pattern = extract_triplets(
    custom_prompt=MULTIPLICATION_COURSE_PATTERN_PROMPT.format(
        addition_pattern=addition_course_pattern,
        course_latex=multiplication_course_latex
    ),
    system_message=SYSTEM_PROMPT,
)
print("Multiplication Course Pattern:")
print(multiplication_course_pattern)
```

```markdown
## Storing Patterns in Neo4j

Now we'll store both patterns in Neo4j to visualize the hierarchical relationship between addition and multiplication. This will allow us to see how multiplication operations build upon addition operations.
```

```python
# Store the patterns in Neo4j
neo4j = Neo4JUtils("bolt://localhost:7687", ("neo4j", "password"))
neo4j.clean_database()

# Store the addition pattern
neo4j.store_triplets(addition_course_pattern, "addition_course")

# Store the multiplication pattern
neo4j.store_triplets(multiplication_course_pattern, "multiplication_course")

print("Patterns stored in Neo4j database.")
```

```markdown
## Visualization Queries

Here are some Neo4j queries that can be used to visualize the hierarchical relationship between addition and multiplication patterns:

1. View the complete knowledge graph:
```cypher
MATCH (n) 
RETURN n
```

2. View the addition pattern:
```cypher
MATCH (n)-[r]-(m)
WHERE n.graph_id = 'addition_course' OR m.graph_id = 'addition_course'
RETURN n, r, m
```

3. View the multiplication pattern:
```cypher
MATCH (n)-[r]-(m)
WHERE n.graph_id = 'multiplication_course' OR m.graph_id = 'multiplication_course'
RETURN n, r, m
```

4. View the hierarchical relationship:
```cypher
MATCH path = (start)-[*]-(end)
WHERE start.graph_id = 'multiplication_course' 
  AND end.graph_id = 'addition_course'
RETURN path
```