{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# First, let's import the necessary libraries and set up our environment\n", "%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))  \n", "\n", "from IPython.display import display, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "from src.phase1.extract_triplets import extract_calculation_graph\n", "from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Load the course content for addition and multiplication\n", "addition_course_latex = read_proof(\"../../data/courses/addition/course_2.tex\")\n", "multiplication_course_latex = read_proof(\"../../data/courses/multiplication/course_2.tex\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Hierarchical Pattern Extraction for Mathematical Operations\n", "\n", "In this notebook, we'll extract knowledge graph patterns for mathematical operations in a hierarchical manner:\n", "\n", "1. First, we'll extract the pattern for \"Addition by Recursion\" from the addition course\n", "2. Then, we'll use this pattern to inform the extraction of the \"Multiplication by Recursion\" pattern\n", "3. This approach will create a hierarchical knowledge representation where multiplication builds upon addition\n", "\n", "This hierarchical approach reflects the natural relationship between mathematical operations, where multiplication can be defined in terms of repeated addition."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\\n\\nFocus on identifying:\\n1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\\n2. The precise mathematical operations performed at each step\\n3. The exact sequence of operations, with clear predecessor-successor relationships\\n4. Intermediate results at each calculation stage\\n5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\\n\\nIMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\\n\\nThe final knowledge graph MUST:\\n1. Have clearly marked START node(s) (the initial example statement)\\n2. Have clearly marked END node(s) (the final result)\\n3. Include ALL intermediate calculation steps with no gaps in reasoning\\n4. Form a single connected component with a clear directional flow\\n5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\\n\\nExtract triplets in the given form of structured output that represent this detailed calculation process.\\n\\nNote:\\n- Entities or Nodes should be specified with short mathematical expressions.\\n- Relations at this mathematical calculation graph is only grounding or simple reasoning connecting one expression to another.\\n- Steps are as much as possible explaining all the minor steps of the reasoning of the calculation.\\n\\nCourse Content:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{amssymb}\\n\\n\\\\begin{document}\\n\\\\section{Title}\\nAddition Via Recursion\\n\\n\\\\section{Abstract}\\nRecursion is an effective way to add numbers. Using recursion, a more complicated addition is reduced to simpler additions. Hence, through a recursive procedure, it turns into several steps of addition by $1$, where $a+1$ denotes the next number.\\n\\\\section{Procedure}\\n\\\\[\\n    \\\\begin{aligned}\\n         & a + b                              \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & (a + (b-1)) + 1                    \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & ((a + (b-2)) + 1) + 1              \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & \\\\quad \\\\vdots                       \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & (\\\\cdots((a + 0) + 1) + \\\\cdots + 1) \\\\\\\\\\n    \\\\end{aligned}\\n\\\\]\\n\\\\section{Examples}\\n\\\\subsection{Example 1}\\n\\\\subsubsection{Question}\\nCalculate this \\\\(3 + 2\\\\):\\n\\\\subsubsection{Answer}\\n\\\\[\\n    \\\\begin{aligned}\\n        3 + 2 & = (3 + 1) + 1 \\\\quad       & \\\\text{(First decomposition)}  \\\\\\\\\\n              & = ((3 + 0) + 1) + 1 \\\\quad & \\\\text{(Second decomposition)} \\\\\\\\\\n              & = (3 + 1) + 1 \\\\quad       & \\\\text{(Base case applied)}    \\\\\\\\\\n              & = 4 + 1 \\\\quad             & \\\\text{(First increment)}      \\\\\\\\\\n              & = 5 \\\\quad                 & \\\\text{(Final result)}\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\subsection{Example 2}\\n\\\\subsubsection{Question}\\nCalculate the result of \\\\(a + b\\\\):\\n\\\\subsubsection{Answer}\\n\\\\[\\n    \\\\begin{aligned}\\n        a + b & = (a + (b-1)) + 1                                       \\\\\\\\\\n              & = ((a + (b-2)) + 1) + 1                                 \\\\\\\\\\n              & \\\\;\\\\;\\\\vdots                                              \\\\\\\\\\n              & = (\\\\cdots((a + 0) + 1) + \\\\cdots + 1)                    \\\\\\\\\\n              & = a + \\\\underbrace{1 + 1 + \\\\cdots + 1}_{b \\\\text{ times}} \\\\\\\\\\n              & = a + b\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\end{document}\\n\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "Addition Course Pattern:\n", "steps=[MathStep(id='step1', expression='a + b', operation='initial_expression', is_start=True, is_end=False), MathStep(id='step2', expression='(a + (b-1)) + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step3', expression='((a + (b-2)) + 1) + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step4', expression='\\x7f\\x7f\\x7f', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step5', expression='(a + 0) + 1 + 1 + \\x7f\\x7f\\x7f', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step6', expression='(a + 0) + 1 + 1 + \\x7f\\x7f\\x7f', operation='base_case', is_start=False, is_end=False), MathStep(id='step7', expression='a + (1 + 1 + \\x7f\\x7f\\x7f)', operation='simplify_expression', is_start=False, is_end=False), MathStep(id='step8', expression='a + b', operation='final_result', is_start=False, is_end=True)] transitions=[MathTransition(source='step1', target='step2', rule='decomposition', explanation='Decomposed b into (b-1) and added 1.'), MathTransition(source='step2', target='step3', rule='decomposition', explanation='Decomposed b-1 into (b-2) and added 1.'), MathTransition(source='step3', target='step4', rule='continue_decomposition', explanation='Continued the decomposition process.'), MathTransition(source='step4', target='step5', rule='continue_decomposition', explanation='Continued the decomposition process.'), MathTransition(source='step5', target='step6', rule='base_case', explanation='Reached the base case where b=0.'), MathTransition(source='step6', target='step7', rule='simplification', explanation='Simplified the expression to a + (1 + 1 + ... + 1).'), MathTransition(source='step7', target='step8', rule='final_result', explanation=\"Final result is obtained by recognizing the sum of 1's.\")]\n"]}], "source": ["# Define the system prompt for our LLM\n", "SYSTEM_PROMPT = \"\"\"You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.\"\"\"\n", "\n", "# Define the prompt for extracting the addition course pattern\n", "ADDITION_COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\n", "\n", "Focus on identifying:\n", "1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\n", "2. The precise mathematical operations performed at each step\n", "3. The exact sequence of operations, with clear predecessor-successor relationships\n", "4. Intermediate results at each calculation stage\n", "5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\n", "\n", "IMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\n", "\n", "The final knowledge graph MUST:\n", "1. Have clearly marked START node(s) (the initial example statement)\n", "2. Have clearly marked END node(s) (the final result)\n", "3. Include ALL intermediate calculation steps with no gaps in reasoning\n", "4. Form a single connected component with a clear directional flow\n", "5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\n", "\n", "Extract triplets in the given form of structured output that represent this detailed calculation process.\n", "\n", "Note:\n", "- Entities or Nodes should be specified with short mathematical expressions.\n", "- Relations at this mathematical calculation graph is only grounding or simple reasoning connecting one expression to another.\n", "- Steps are as much as possible explaining all the minor steps of the reasoning of the calculation.\n", "\n", "Course Content:\n", "```\n", "{course_latex}\n", "```\n", "\"\"\"\n", "\n", "# Extract the addition course pattern\n", "addition_course_pattern = extract_calculation_graph(\n", "    custom_prompt=ADDITION_COURSE_PATTERN_PROMPT.format(\n", "        course_latex=addition_course_latex\n", "    ),\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "print(\"Addition Course Pattern:\")\n", "print(addition_course_pattern)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hierarchical Pattern Extraction for Multiplication\n", "\n", "Now that we have extracted the pattern for addition by recursion, we'll use it to inform the extraction of the multiplication pattern. \n", "\n", "The key insight is that multiplication can be defined as repeated addition, so the multiplication pattern should incorporate the addition pattern as a sub-component. This creates a hierarchical relationship between the two operations."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical course content in LaTeX format and a previously extracted course pattern in the knowledge graph triplets format, extract a VERY DETAILED step-by-step explanatory chain for this course. Create a reasoning graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\\n\\nThe previously extracted course pattern is:\\n```\\nsteps=[MathStep(id=\\'step1\\', expression=\\'a + b\\', operation=\\'initial_expression\\', is_start=True, is_end=False), MathStep(id=\\'step2\\', expression=\\'(a + (b-1)) + 1\\', operation=\\'decompose_b\\', is_start=False, is_end=False), MathStep(id=\\'step3\\', expression=\\'((a + (b-2)) + 1) + 1\\', operation=\\'decompose_b\\', is_start=False, is_end=False), MathStep(id=\\'step4\\', expression=\\'\\\\x7f\\\\x7f\\\\x7f\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step5\\', expression=\\'(a + 0) + 1 + 1 + \\\\x7f\\\\x7f\\\\x7f\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step6\\', expression=\\'(a + 0) + 1 + 1 + \\\\x7f\\\\x7f\\\\x7f\\', operation=\\'base_case\\', is_start=False, is_end=False), MathStep(id=\\'step7\\', expression=\\'a + (1 + 1 + \\\\x7f\\\\x7f\\\\x7f)\\', operation=\\'simplify_expression\\', is_start=False, is_end=False), MathStep(id=\\'step8\\', expression=\\'a + b\\', operation=\\'final_result\\', is_start=False, is_end=True)] transitions=[MathTransition(source=\\'step1\\', target=\\'step2\\', rule=\\'decomposition\\', explanation=\\'Decomposed b into (b-1) and added 1.\\'), MathTransition(source=\\'step2\\', target=\\'step3\\', rule=\\'decomposition\\', explanation=\\'Decomposed b-1 into (b-2) and added 1.\\'), MathTransition(source=\\'step3\\', target=\\'step4\\', rule=\\'continue_decomposition\\', explanation=\\'Continued the decomposition process.\\'), MathTransition(source=\\'step4\\', target=\\'step5\\', rule=\\'continue_decomposition\\', explanation=\\'Continued the decomposition process.\\'), MathTransition(source=\\'step5\\', target=\\'step6\\', rule=\\'base_case\\', explanation=\\'Reached the base case where b=0.\\'), MathTransition(source=\\'step6\\', target=\\'step7\\', rule=\\'simplification\\', explanation=\\'Simplified the expression to a + (1 + 1 + ... + 1).\\'), MathTransition(source=\\'step7\\', target=\\'step8\\', rule=\\'final_result\\', explanation=\"Final result is obtained by recognizing the sum of 1\\'s.\")]\\n```\\n\\nIMPORTANT: Courses builds upon each other. For example, a pattern can incorporate multiple pattern of the child pattern. The extracted course pattern should incorporate the child patterns and use them even if they are expelicitly stated in the course content.\\n\\nThe final knowledge graph MUST:\\n1. Have clearly marked START node(s) (the initial calculation problem)\\n2. Have clearly marked END node(s) (the final result)\\n3. Include ALL intermediate calculation steps with no gaps in reasoning\\n4. Form a single connected component with a clear directional flow\\n5. Use relationship types that precisely describe the mathematical operations\\n\\nExtract triplets in the given form of structured output that represent this detailed calculation process.\\n\\nNote:\\n- Entities or Nodes should be specified with short mathematical expressions.\\n- Steps should explain all the minor steps of the reasoning.\\n\\nCourse Content:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{amssymb}\\n\\\\begin{document}\\n\\n\\\\section{Title}\\nMultiplication Via Recursion\\n\\n\\\\section{Abstract}\\nMultiplication via recursion breaks down complex multiplication into simpler steps through two phases: first decomposing multiplication into repeated addition, then applying addition via recursion to compute the final result. This approach systematically reduces complex calculations to elementary operations.\\n\\n\\\\section{Recursive Definition}\\nFor non-negative integers \\\\(a\\\\) and \\\\(b\\\\):\\n\\n\\\\[\\n    a \\\\times b = \\\\begin{cases}\\n        0                      & \\\\text{if } b = 0 \\\\quad \\\\text{(Base Case)}      \\\\\\\\\\n        a + (a \\\\times (b - 1)) & \\\\text{if } b > 0 \\\\quad \\\\text{(Recursive Case)}\\n    \\\\end{cases}\\n\\\\]\\n\\n\\\\section{Complete Calculation Procedure}\\nThe complete calculation of \\\\(a \\\\times b\\\\) follows these sequential steps:\\n\\n\\\\subsection{Phase 1: Multiplication Decomposition}\\n\\\\subsubsection{Start with the initial problem: \\\\(a \\\\times b\\\\)}\\n\\\\subsubsection{Apply the recursive formula: \\\\(a \\\\times b = a + (a \\\\times (b-1))\\\\)}\\n\\\\subsubsection{Continue decomposing until reaching the base case:}\\n\\\\[\\n    \\\\begin{aligned}\\n        a \\\\times b & = a + (a \\\\times (b-1))                                             \\\\\\\\\\n                   & = a + (a + (a \\\\times (b-2)))                                       \\\\\\\\\\n                   & = a + a + (a \\\\times (b-3))                                         \\\\\\\\\\n                   & \\\\vdots                                                             \\\\\\\\\\n                   & = \\\\underbrace{a + a + \\\\cdots + a}_{b \\\\text{ times}} + (a \\\\times 0) \\\\\\\\\\n                   & = \\\\underbrace{a + a + \\\\cdots + a}_{b \\\\text{ times}} + 0            \\\\\\\\\\n                   & = \\\\underbrace{a + a + \\\\cdots + a}_{b \\\\text{ times}}\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\subsection{Phase 2: Addition Calculation}\\nAfter decomposing multiplication into repeated addition, we must calculate each addition operation separately. This requires multiple applications of the addition via recursion process:\\n\\n\\\\subsubsection{Identify all addition operations}\\nThe result of Phase 1 is an expression with \\\\(b\\\\) instances of \\\\(a\\\\) being added together:\\n\\\\[\\n    \\\\underbrace{a + a + \\\\cdots + a}_{b \\\\text{ times}}\\n\\\\]\\n\\n\\\\subsubsection{Apply addition via recursion to each pair of terms}\\nFor each addition operation in the sequence, we must apply the addition via recursion pattern separately:\\n\\n\\\\[\\n    \\\\begin{aligned}\\n        \\\\text{First addition:} \\\\quad a + a & = (a + (a-1)) + 1                    \\\\\\\\\\n                                           & = ((a + (a-2)) + 1) + 1              \\\\\\\\\\n                                           & \\\\vdots                               \\\\\\\\\\n                                           & = (\\\\cdots((a + 0) + 1) + \\\\cdots + 1)\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\subsubsection{Process additions sequentially}\\nAfter calculating the first addition, we must continue with the next addition operation:\\n\\\\[\\n    \\\\begin{aligned}\\n        \\\\text{Second addition:} \\\\quad (a + a) + a & = ((a + a) + (a-1)) + 1       \\\\\\\\\\n                                                  & = (((a + a) + (a-2)) + 1) + 1 \\\\\\\\\\n                                                  & \\\\vdots\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\subsubsection{Continue until all additions are resolved}\\nThis process continues for all \\\\(b-1\\\\) addition operations in the expression:\\n\\\\[\\n    \\\\begin{aligned}\\n        \\\\text{Third addition:} \\\\quad ((a + a) + a) + a & = \\\\ldots \\\\\\\\\\n        \\\\vdots\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\subsubsection{Combine all results}\\nAfter applying addition via recursion to each addition operation, we obtain the final result:\\n\\\\[\\n    \\\\underbrace{a + a + \\\\cdots + a}_{b \\\\text{ times}} = a \\\\times b\\n\\\\]\\n\\n\\\\section{Summary}\\nMultiplication via recursion follows a clear two-phase process:\\n\\\\begin{enumerate}\\n    \\\\item Phase 1: Decompose multiplication into repeated addition, resulting in \\\\(b\\\\) instances of \\\\(a\\\\) being added together\\n    \\\\item Phase 2: Calculate each addition operation separately using addition via recursion, requiring \\\\(b-1\\\\) distinct applications of the addition process\\n\\\\end{enumerate}\\n\\nThis approach demonstrates how complex operations (multiplication) can be systematically reduced to simpler operations (multiple separate additions) and ultimately to the most basic operation (adding 1). The key insight is that each addition operation in the sequence must be calculated individually using the addition via recursion process.\\n\\n\\\\end{document}\\n\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "Multiplication Course Pattern:\n", "steps=[MathStep(id='step1', expression='a \\\\times b', operation='initial_expression', is_start=True, is_end=False), MathStep(id='step2', expression='a + (a \\\\times (b-1))', operation='apply_recursive_formula', is_start=False, is_end=False), MathStep(id='step3', expression='a + (a + (a \\\\times (b-2)))', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step4', expression='a + a + (a \\\\times (b-3))', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step5', expression='\\\\underbrace{a + a + \\\\cdots + a}_{b \\\\text{ times}} + (a \\\\times 0)', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step6', expression='\\\\underbrace{a + a + \\\\cdots + a}_{b \\\\text{ times}} + 0', operation='base_case', is_start=False, is_end=False), MathStep(id='step7', expression='\\\\underbrace{a + a + \\\\cdots + a}_{b \\\\text{ times}}', operation='final_simplification', is_start=False, is_end=False), MathStep(id='step8', expression='a \\\\times b', operation='final_result', is_start=False, is_end=True)] transitions=[MathTransition(source='step1', target='step2', rule='recursive_definition', explanation='Applied the recursive formula to express multiplication as addition.'), MathTransition(source='step2', target='step3', rule='decomposition', explanation='Decomposed (b-1) into (b-2) and added a.'), MathTransition(source='step3', target='step4', rule='decomposition', explanation='Decomposed (b-2) into (b-3) and added a.'), MathTransition(source='step4', target='step5', rule='continue_decomposition', explanation='Continued the decomposition process until reaching the base case.'), MathTransition(source='step5', target='step6', rule='base_case', explanation='Reached the base case where (a \\\\times 0) equals 0.'), MathTransition(source='step6', target='step7', rule='final_simplification', explanation='Simplified the expression to show the sum of a added b times.'), MathTransition(source='step7', target='step8', rule='final_result', explanation='Final result is obtained by recognizing the sum of a added b times equals a \\\\times b.')]\n"]}], "source": ["# Define the prompt for extracting the multiplication course pattern using the addition pattern\n", "MULTIPLICATION_COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematical course content in LaTeX format and a previously extracted course pattern in the knowledge graph triplets format, extract a VERY DETAILED step-by-step explanatory chain for this course. Create a reasoning graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\n", "\n", "The previously extracted course pattern is:\n", "```\n", "{addition_pattern}\n", "```\n", "\n", "IMPORTANT: Courses builds upon each other. For example, a pattern can incorporate multiple pattern of the child pattern. The extracted course pattern should incorporate the child patterns and use them even if they are expelicitly stated in the course content.\n", "\n", "The final knowledge graph MUST:\n", "1. Have clearly marked START node(s) (the initial calculation problem)\n", "2. Have clearly marked END node(s) (the final result)\n", "3. Include ALL intermediate calculation steps with no gaps in reasoning\n", "4. Form a single connected component with a clear directional flow\n", "5. Use relationship types that precisely describe the mathematical operations\n", "\n", "Extract triplets in the given form of structured output that represent this detailed calculation process.\n", "\n", "Note:\n", "- Entities or Nodes should be specified with short mathematical expressions.\n", "- Steps should explain all the minor steps of the reasoning.\n", "\n", "Course Content:\n", "```\n", "{course_latex}\n", "```\n", "\"\"\"\n", "\n", "# Extract the multiplication course pattern using the addition pattern\n", "multiplication_course_pattern = extract_calculation_graph(\n", "    custom_prompt=MULTIPLICATION_COURSE_PATTERN_PROMPT.format(\n", "        addition_pattern=addition_course_pattern,\n", "        course_latex=multiplication_course_latex,\n", "    ),\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "print(\"Multiplication Course Pattern:\")\n", "print(multiplication_course_pattern)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Storing Patterns in Neo4j\n", "\n", "Now we'll store both patterns in Neo4j to visualize the hierarchical relationship between addition and multiplication. This will allow us to see how multiplication operations build upon addition operations."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Patterns stored in Neo4j database.\n"]}], "source": ["# Store the patterns in Neo4j\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"neo4j\", \"password\"))\n", "neo4j.clean_database()\n", "\n", "\n", "# Function to sanitize LaTeX commands in relationship names\n", "def sanitize_latex(text):\n", "    # Replace LaTeX commands with plain text alternatives\n", "    sanitized = text.replace(\"\\\\underbrace\", \"\")\n", "    sanitized = sanitized.replace(\"\\\\text\", \"\")\n", "    sanitized = sanitized.replace(\"\\\\cdots\", \"...\")\n", "    sanitized = sanitized.replace(\"\\\\times\", \"×\")\n", "    # Remove LaTeX subscripts and superscripts\n", "    sanitized = re.sub(r\"_{.*?}\", \"\", sanitized)\n", "    sanitized = re.sub(r\"\\^{.*?}\", \"\", sanitized)\n", "    return sanitized\n", "\n", "\n", "# Sanitize relationship names in multiplication pattern\n", "for step in multiplication_course_pattern.steps:\n", "    step.expression = sanitize_latex(step.expression)\n", "\n", "# # Sanitize entity names in multiplication pattern\n", "# for entity in multiplication_course_pattern.entities:\n", "#     entity.name = sanitize_latex(entity.name)\n", "#     if hasattr(entity, \"label\") and entity.label:\n", "#         entity.label = sanitize_latex(entity.label)\n", "\n", "# Store the addition pattern\n", "neo4j.store_calculation_graph(addition_course_pattern, \"addition_course\")\n", "\n", "# Store the multiplication pattern\n", "neo4j.store_calculation_graph(multiplication_course_pattern, \"multiplication_course\")\n", "\n", "print(\"Patterns stored in Neo4j database.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization Queries\n", "\n", "Here are some Neo4j queries that can be used to visualize the hierarchical relationship between addition and multiplication patterns:\n", "\n", "1. View the complete knowledge graph:\n", "```cypher\n", "MATCH (n) \n", "RETURN n\n", "```\n", "\n", "2. View the addition pattern:\n", "```cypher\n", "MATCH (n)-[r]-(m)\n", "WHERE n.graph_id = 'addition_course' OR m.graph_id = 'addition_course'\n", "RETURN n, r, m\n", "```\n", "\n", "3. View the multiplication pattern:\n", "```cypher\n", "MATCH (n)-[r]-(m)\n", "WHERE n.graph_id = 'multiplication_course' OR m.graph_id = 'multiplication_course'\n", "RETURN n, r, m\n", "```\n", "\n", "4. View the hierarchical relationship:\n", "```cypher\n", "MATCH path = (start)-[*]-(end)\n", "WHERE start.graph_id = 'multiplication_course' \n", "  AND end.graph_id = 'addition_course'\n", "RETURN path\n", "```"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following multiplication problem and the hierarchical patterns for addition and multiplication, \\ncreate a detailed step-by-step solution that follows these patterns.\\n\\nAddition Pattern:\\n```\\nsteps=[MathStep(id=\\'step1\\', expression=\\'a + b\\', operation=\\'initial_expression\\', is_start=True, is_end=False), MathStep(id=\\'step2\\', expression=\\'(a + (b-1)) + 1\\', operation=\\'decompose_b\\', is_start=False, is_end=False), MathStep(id=\\'step3\\', expression=\\'((a + (b-2)) + 1) + 1\\', operation=\\'decompose_b\\', is_start=False, is_end=False), MathStep(id=\\'step4\\', expression=\\'\\\\x7f\\\\x7f\\\\x7f\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step5\\', expression=\\'(a + 0) + 1 + 1 + \\\\x7f\\\\x7f\\\\x7f\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step6\\', expression=\\'(a + 0) + 1 + 1 + \\\\x7f\\\\x7f\\\\x7f\\', operation=\\'base_case\\', is_start=False, is_end=False), MathStep(id=\\'step7\\', expression=\\'a + (1 + 1 + \\\\x7f\\\\x7f\\\\x7f)\\', operation=\\'simplify_expression\\', is_start=False, is_end=False), MathStep(id=\\'step8\\', expression=\\'a + b\\', operation=\\'final_result\\', is_start=False, is_end=True)] transitions=[MathTransition(source=\\'step1\\', target=\\'step2\\', rule=\\'decomposition\\', explanation=\\'Decomposed b into (b-1) and added 1.\\'), MathTransition(source=\\'step2\\', target=\\'step3\\', rule=\\'decomposition\\', explanation=\\'Decomposed b-1 into (b-2) and added 1.\\'), MathTransition(source=\\'step3\\', target=\\'step4\\', rule=\\'continue_decomposition\\', explanation=\\'Continued the decomposition process.\\'), MathTransition(source=\\'step4\\', target=\\'step5\\', rule=\\'continue_decomposition\\', explanation=\\'Continued the decomposition process.\\'), MathTransition(source=\\'step5\\', target=\\'step6\\', rule=\\'base_case\\', explanation=\\'Reached the base case where b=0.\\'), MathTransition(source=\\'step6\\', target=\\'step7\\', rule=\\'simplification\\', explanation=\\'Simplified the expression to a + (1 + 1 + ... + 1).\\'), MathTransition(source=\\'step7\\', target=\\'step8\\', rule=\\'final_result\\', explanation=\"Final result is obtained by recognizing the sum of 1\\'s.\")]\\n```\\n\\nMultiplication Pattern:\\n```\\nsteps=[MathStep(id=\\'step1\\', expression=\\'a × b\\', operation=\\'initial_expression\\', is_start=True, is_end=False), MathStep(id=\\'step2\\', expression=\\'a + (a × (b-1))\\', operation=\\'apply_recursive_formula\\', is_start=False, is_end=False), MathStep(id=\\'step3\\', expression=\\'a + (a + (a × (b-2)))\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step4\\', expression=\\'a + a + (a × (b-3))\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step5\\', expression=\\'{a + a + ... + a}} + (a × 0)\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step6\\', expression=\\'{a + a + ... + a}} + 0\\', operation=\\'base_case\\', is_start=False, is_end=False), MathStep(id=\\'step7\\', expression=\\'{a + a + ... + a}}\\', operation=\\'final_simplification\\', is_start=False, is_end=False), MathStep(id=\\'step8\\', expression=\\'a × b\\', operation=\\'final_result\\', is_start=False, is_end=True)] transitions=[MathTransition(source=\\'step1\\', target=\\'step2\\', rule=\\'recursive_definition\\', explanation=\\'Applied the recursive formula to express multiplication as addition.\\'), MathTransition(source=\\'step2\\', target=\\'step3\\', rule=\\'decomposition\\', explanation=\\'Decomposed (b-1) into (b-2) and added a.\\'), MathTransition(source=\\'step3\\', target=\\'step4\\', rule=\\'decomposition\\', explanation=\\'Decomposed (b-2) into (b-3) and added a.\\'), MathTransition(source=\\'step4\\', target=\\'step5\\', rule=\\'continue_decomposition\\', explanation=\\'Continued the decomposition process until reaching the base case.\\'), MathTransition(source=\\'step5\\', target=\\'step6\\', rule=\\'base_case\\', explanation=\\'Reached the base case where (a \\\\\\\\times 0) equals 0.\\'), MathTransition(source=\\'step6\\', target=\\'step7\\', rule=\\'final_simplification\\', explanation=\\'Simplified the expression to show the sum of a added b times.\\'), MathTransition(source=\\'step7\\', target=\\'step8\\', rule=\\'final_result\\', explanation=\\'Final result is obtained by recognizing the sum of a added b times equals a \\\\\\\\times b.\\')]\\n```\\n\\nProblem: 5 × 3\\n\\nProvide a detailed solution that:\\n1. First applies the multiplication pattern to break down the problem\\n2. Then applies the addition pattern for the necessary addition steps\\n3. Shows all intermediate steps and calculations\\n4. Maintains the hierarchical relationship between multiplication and addition\\n\\nExtract triplets in the form <Source Entity, Relationship, Target Entity> that represent this solution.\\n', additional_kwargs={}, response_metadata={})]\n", "Solution for test problem:\n", "steps=[MathStep(id='step1', expression='5 × 3', operation='initial_expression', is_start=True, is_end=False), MathStep(id='step2', expression='5 + (5 × (3-1))', operation='apply_recursive_formula', is_start=False, is_end=False), MathStep(id='step3', expression='5 + (5 + (5 × (3-2)))', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step4', expression='5 + 5 + (5 × (3-3))', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step5', expression='5 + 5 + (5 × 0)', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step6', expression='5 + 5 + 0', operation='base_case', is_start=False, is_end=False), MathStep(id='step7', expression='5 + 5', operation='final_simplification', is_start=False, is_end=False), MathStep(id='step8', expression='10', operation='final_result', is_start=False, is_end=True)] transitions=[MathTransition(source='step1', target='step2', rule='recursive_definition', explanation='Applied the recursive formula to express multiplication as addition.'), MathTransition(source='step2', target='step3', rule='decomposition', explanation='Decomposed (3-1) into (3-2) and added 5.'), MathTransition(source='step3', target='step4', rule='decomposition', explanation='Decomposed (3-2) into (3-3) and added 5.'), MathTransition(source='step4', target='step5', rule='continue_decomposition', explanation='Continued the decomposition process until reaching the base case.'), MathTransition(source='step5', target='step6', rule='base_case', explanation='Reached the base case where (5 × 0) equals 0.'), MathTransition(source='step6', target='step7', rule='final_simplification', explanation='Simplified the expression to show the sum of 5 added twice.'), MathTransition(source='step7', target='step8', rule='final_result', explanation='Final result is obtained by recognizing the sum of 5 added twice equals 10.')]\n", "Test solution stored in Neo4j database.\n"]}], "source": ["# Example: Load a test problem and apply the hierarchical pattern\n", "test_problem = \"5 × 3\"\n", "\n", "# Define a prompt to apply the hierarchical pattern to the test problem\n", "TEST_PROBLEM_PROMPT = \"\"\"\n", "Given the following multiplication problem and the hierarchical patterns for addition and multiplication, \n", "create a detailed step-by-step solution that follows these patterns.\n", "\n", "Addition Pattern:\n", "```\n", "{addition_pattern}\n", "```\n", "\n", "Multiplication Pattern:\n", "```\n", "{multiplication_pattern}\n", "```\n", "\n", "Problem: {problem}\n", "\n", "Provide a detailed solution that:\n", "1. First applies the multiplication pattern to break down the problem\n", "2. Then applies the addition pattern for the necessary addition steps\n", "3. Shows all intermediate steps and calculations\n", "4. Maintains the hierarchical relationship between multiplication and addition\n", "\n", "Extract triplets in the form <Source Entity, Relationship, Target Entity> that represent this solution.\n", "\"\"\"\n", "\n", "# Apply the hierarchical pattern to the test problem\n", "test_solution = extract_calculation_graph(\n", "    custom_prompt=TEST_PROBLEM_PROMPT.format(\n", "        addition_pattern=addition_course_pattern,\n", "        multiplication_pattern=multiplication_course_pattern,\n", "        problem=test_problem,\n", "    ),\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "\n", "print(\"Solution for test problem:\")\n", "print(test_solution)\n", "\n", "# Store the test solution in Neo4j\n", "neo4j.store_calculation_graph(test_solution, \"test_solution\")\n", "print(\"Test solution stored in Neo4j database.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}