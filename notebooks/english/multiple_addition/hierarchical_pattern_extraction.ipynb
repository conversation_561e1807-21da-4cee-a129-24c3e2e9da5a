{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# First, let's import the necessary libraries and set up our environment\n", "%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))  \n", "\n", "from IPython.display import display, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "from src.phase1.extract_triplets import extract_calculation_graph\n", "from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Load the course content for addition and multiple addition\n", "addition_course_latex = read_proof(\"../../data/courses/addition/course_2.tex\")\n", "multiple_addition_course_latex = read_proof(\"../../data/courses/multiple_addition/course.tex\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Hierarchical Pattern Extraction for Multiple Addition\n", "\n", "In this notebook, we'll extract knowledge graph patterns for mathematical operations in a hierarchical manner:\n", "\n", "1. First, we'll extract the pattern for \"Addition by Recursion\" from the addition course\n", "2. Then, we'll use this pattern to inform the extraction of the \"Multiple Addition\" pattern\n", "3. This approach will create a hierarchical knowledge representation where multiple addition builds upon binary addition\n", "\n", "This hierarchical approach reflects the natural relationship between mathematical operations, where multiple addition can be defined in terms of repeated binary addition."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\\n\\nFocus on identifying:\\n1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\\n2. The precise mathematical operations performed at each step\\n3. The exact sequence of operations, with clear predecessor-successor relationships\\n4. Intermediate results at each calculation stage\\n5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\\n\\nIMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\\n\\nThe final knowledge graph MUST:\\n1. Have clearly marked START node(s) (the initial example statement)\\n2. Have clearly marked END node(s) (the final result)\\n3. Include ALL intermediate calculation steps with no gaps in reasoning\\n4. Form a single connected component with a clear directional flow\\n5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\\n\\nExtract a calculation graph with steps and transitions that represent this detailed calculation process.\\n\\nNote:\\n- Steps should be specified with short mathematical expressions.\\n- Transitions should explain all the minor steps of the reasoning of the calculation.\\n\\nCourse Content:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{amssymb}\\n\\n\\\\begin{document}\\n\\\\section{Title}\\nAddition Via Recursion\\n\\n\\\\section{Abstract}\\nRecursion is an effective way to add numbers. Using recursion, a more complicated addition is reduced to simpler additions. Hence, through a recursive procedure, it turns into several steps of addition by $1$, where $a+1$ denotes the next number.\\n\\\\section{Procedure}\\n\\\\[\\n    \\\\begin{aligned}\\n         & a + b                              \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & (a + (b-1)) + 1                    \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & ((a + (b-2)) + 1) + 1              \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & \\\\quad \\\\vdots                       \\\\\\\\\\n         & \\\\Downarrow                         \\\\\\\\\\n         & (\\\\cdots((a + 0) + 1) + \\\\cdots + 1) \\\\\\\\\\n    \\\\end{aligned}\\n\\\\]\\n\\\\section{Examples}\\n\\\\subsection{Example 1}\\n\\\\subsubsection{Question}\\nCalculate this \\\\(3 + 2\\\\):\\n\\\\subsubsection{Answer}\\n\\\\[\\n    \\\\begin{aligned}\\n        3 + 2 & = (3 + 1) + 1 \\\\quad       & \\\\text{(First decomposition)}  \\\\\\\\\\n              & = ((3 + 0) + 1) + 1 \\\\quad & \\\\text{(Second decomposition)} \\\\\\\\\\n              & = (3 + 1) + 1 \\\\quad       & \\\\text{(Base case applied)}    \\\\\\\\\\n              & = 4 + 1 \\\\quad             & \\\\text{(First increment)}      \\\\\\\\\\n              & = 5 \\\\quad                 & \\\\text{(Final result)}\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\subsection{Example 2}\\n\\\\subsubsection{Question}\\nCalculate the result of \\\\(a + b\\\\):\\n\\\\subsubsection{Answer}\\n\\\\[\\n    \\\\begin{aligned}\\n        a + b & = (a + (b-1)) + 1                                       \\\\\\\\\\n              & = ((a + (b-2)) + 1) + 1                                 \\\\\\\\\\n              & \\\\;\\\\;\\\\vdots                                              \\\\\\\\\\n              & = (\\\\cdots((a + 0) + 1) + \\\\cdots + 1)                    \\\\\\\\\\n              & = a + \\\\underbrace{1 + 1 + \\\\cdots + 1}_{b \\\\text{ times}} \\\\\\\\\\n              & = a + b\\n    \\\\end{aligned}\\n\\\\]\\n\\n\\\\end{document}\\n\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "Addition Course Pattern:\n", "steps=[MathStep(id='step1', expression='a + b', operation='initial_expression', is_start=True, is_end=False), MathStep(id='step2', expression='(a + (b-1)) + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step3', expression='((a + (b-2)) + 1) + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step4', expression='\\x7f\\x7f\\x7f', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step5', expression='(a + 0) + 1 + 1 + ... + 1', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step6', expression='a + (1 + 1 + ... + 1)', operation='simplify_expression', is_start=False, is_end=False), MathStep(id='step7', expression='a + b', operation='final_result', is_start=False, is_end=True)] transitions=[MathTransition(source='step1', target='step2', rule='decomposition', explanation='Decomposed b into (b-1) and added 1.'), MathTransition(source='step2', target='step3', rule='decomposition', explanation='Decomposed (b-1) into (b-2) and added 1.'), MathTransition(source='step3', target='step4', rule='decomposition', explanation='Continued decomposing b until reaching 0.'), MathTransition(source='step4', target='step5', rule='decomposition', explanation=\"Expressed the addition in terms of 0 and added 1's.\"), MathTransition(source='step5', target='step6', rule='simplification', explanation='Simplified the expression to a + (1 + 1 + ... + 1).'), MathTransition(source='step6', target='step7', rule='final_result', explanation='Recognized that adding 1, b times results in a + b.')]\n"]}], "source": ["# Define the system prompt for our LLM\n", "SYSTEM_PROMPT = \"\"\"You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.\"\"\"\n", "\n", "# Define the prompt for extracting the addition course pattern\n", "ADDITION_COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematical course content in LaTeX format, extract a VERY DETAILED step-by-step explanatory chain that represents the calculation process. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\n", "\n", "Focus on identifying:\n", "1. Every individual calculation step, no matter how small (e.g., \"Add 2 to both sides\", \"Apply distributive property\", etc.)\n", "2. The precise mathematical operations performed at each step\n", "3. The exact sequence of operations, with clear predecessor-successor relationships\n", "4. Intermediate results at each calculation stage\n", "5. The mathematical justification for each step (e.g., \"By the associative property\", \"By substituting value from step 3\")\n", "\n", "IMPORTANT: If you see examples in the course content, do not extract them as separate graphs. The examples are only included to help you understand the calculation process better. Focus only on extracting the general calculation pattern/process.\n", "\n", "The final knowledge graph MUST:\n", "1. Have clearly marked START node(s) (the initial example statement)\n", "2. Have clearly marked END node(s) (the final result)\n", "3. Include ALL intermediate calculation steps with no gaps in reasoning\n", "4. Form a single connected component with a clear directional flow\n", "5. Use relationship types that precisely describe the mathematical operation performed (e.g., \"applies_distributive_property\", \"substitutes_value\", \"simplifies_expression\")\n", "\n", "Extract a calculation graph with steps and transitions that represent this detailed calculation process.\n", "\n", "Note:\n", "- Steps should be specified with short mathematical expressions.\n", "- Transitions should explain all the minor steps of the reasoning of the calculation.\n", "\n", "Course Content:\n", "```\n", "{course_latex}\n", "```\n", "\"\"\"\n", "\n", "# Extract the addition course pattern\n", "addition_course_pattern = extract_calculation_graph(\n", "    custom_prompt=ADDITION_COURSE_PATTERN_PROMPT.format(\n", "        course_latex=addition_course_latex\n", "    ),\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "print(\"Addition Course Pattern:\")\n", "print(addition_course_pattern)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hierarchical Pattern Extraction for Multiple Addition\n", "\n", "Now that we have extracted the pattern for addition by recursion, we'll use it to inform the extraction of the multiple addition pattern. \n", "\n", "The key insight is that multiple addition can be defined as repeated binary addition, so the multiple addition pattern should incorporate the binary addition pattern as a sub-component. This creates a hierarchical relationship between the two operations."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following mathematical course content in LaTeX format and a previously extracted addition pattern, extract a VERY DETAILED step-by-step explanatory chain for multiple addition. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\\n\\nThe previously extracted addition pattern is:\\n```\\nsteps=[MathStep(id=\\'step1\\', expression=\\'a + b\\', operation=\\'initial_expression\\', is_start=True, is_end=False), MathStep(id=\\'step2\\', expression=\\'(a + (b-1)) + 1\\', operation=\\'decompose_b\\', is_start=False, is_end=False), MathStep(id=\\'step3\\', expression=\\'((a + (b-2)) + 1) + 1\\', operation=\\'decompose_b\\', is_start=False, is_end=False), MathStep(id=\\'step4\\', expression=\\'\\\\x7f\\\\x7f\\\\x7f\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step5\\', expression=\\'(a + 0) + 1 + 1 + ... + 1\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step6\\', expression=\\'a + (1 + 1 + ... + 1)\\', operation=\\'simplify_expression\\', is_start=False, is_end=False), MathStep(id=\\'step7\\', expression=\\'a + b\\', operation=\\'final_result\\', is_start=False, is_end=True)] transitions=[MathTransition(source=\\'step1\\', target=\\'step2\\', rule=\\'decomposition\\', explanation=\\'Decomposed b into (b-1) and added 1.\\'), MathTransition(source=\\'step2\\', target=\\'step3\\', rule=\\'decomposition\\', explanation=\\'Decomposed (b-1) into (b-2) and added 1.\\'), MathTransition(source=\\'step3\\', target=\\'step4\\', rule=\\'decomposition\\', explanation=\\'Continued decomposing b until reaching 0.\\'), MathTransition(source=\\'step4\\', target=\\'step5\\', rule=\\'decomposition\\', explanation=\"Expressed the addition in terms of 0 and added 1\\'s.\"), MathTransition(source=\\'step5\\', target=\\'step6\\', rule=\\'simplification\\', explanation=\\'Simplified the expression to a + (1 + 1 + ... + 1).\\'), MathTransition(source=\\'step6\\', target=\\'step7\\', rule=\\'final_result\\', explanation=\\'Recognized that adding 1, b times results in a + b.\\')]\\n```\\n\\nIMPORTANT: Multiple addition builds upon binary addition. Your extracted knowledge graph MUST maintain this hierarchical relationship by:\\n1. Incorporating the binary addition pattern as a sub-component of the multiple addition process\\n2. Showing how multiple addition operations decompose into binary addition operations\\n3. Creating explicit connections between multiple addition steps and their corresponding binary addition steps\\n4. Preserving the hierarchical structure where multiple addition is defined in terms of binary addition\\n\\nFocus on identifying:\\n1. Every individual calculation step in the multiple addition process\\n2. How multiple addition decomposes into simpler binary addition problems\\n3. How parentheses are used to group terms for binary addition\\n4. The precise sequence of operations with clear predecessor-successor relationships\\n5. Intermediate results at each calculation stage\\n\\nThe final knowledge graph MUST:\\n1. Have clearly marked START node(s) (the initial multiple addition problem)\\n2. Have clearly marked END node(s) (the final result)\\n3. Include ALL intermediate calculation steps with no gaps in reasoning\\n4. Form a single connected component with a clear directional flow\\n5. Use relationship types that precisely describe the mathematical operations\\n6. Show the hierarchical relationship between multiple addition and binary addition\\n\\nExtract a calculation graph with steps and transitions that represent this detailed calculation process.\\n\\nNote:\\n- Steps should be specified with short mathematical expressions.\\n- Transitions should explain all the minor steps of the reasoning, including how multiple addition steps decompose into binary addition steps.\\n\\nCourse Content:\\n```\\n\\\\documentclass{article}\\n\\\\usepackage{amsmath}\\n\\\\usepackage{geometry}\\n\\n\\\\title{Adding Multiple Numbers}\\n\\n\\n\\\\begin{document}\\n\\n\\\\maketitle\\n\\n\\\\section{Introduction}\\nAll addition ultimately reduces to pairwise operations. This course shows how we systematically break down sums with multiple numbers into sequences of binary additions using parentheses.\\n\\n\\\\section{Core Principle}\\n\\\\emph{Addition is binary:} We can only add two numbers at any given moment. For multiple numbers:\\n\\\\begin{itemize}\\n    \\\\item We need $(n-1)$ additions for $n$ numbers\\n    \\\\item Parentheses specify operation order\\n    \\\\item Different groupings yield same result (associativity)\\n\\\\end{itemize}\\n\\n\\\\section{General Case Pattern}\\n\\nFor numbers $a_1 + a_2 + \\\\cdots + a_n$:\\n\\n\\\\renewcommand{\\\\arraystretch}{1.5}\\n\\\\begin{tabular}{rl}\\n    Step 1:     & $(a_1 + a_2)$                 \\\\\\\\\\n    Step 2:     & $(\\\\text{Result}_1 + a_3)$     \\\\\\\\\\n    $\\\\vdots$    &                               \\\\\\\\\\n    Step $n-1$: & $(\\\\text{Result}_{n-2} + a_n)$ \\\\\\\\\\n\\\\end{tabular}\\n\\n\\\\section{Basic Examples}\\n\\n\\\\subsection{Example}\\n\\\\begin{align*}\\n     & 2 + 3 + 4     \\\\\\\\\\n     & = (2 + 3) + 4 \\\\\\\\\\n     & = 5 + 4       \\\\\\\\\\n     & = 9\\n\\\\end{align*}\\n\\nAlternative grouping:\\n\\\\begin{align*}\\n     & 2 + (3 + 4) \\\\\\\\\\n     & = 2 + 7     \\\\\\\\\\n     & = 9\\n\\\\end{align*}\\n\\n\\\\subsection{Example}\\n\\\\begin{align*}\\n     & 1 + 4 + 2 + 5       \\\\\\\\\\n     & = ((1 + 4) + 2) + 5 \\\\\\\\\\n     & = (5 + 2) + 5       \\\\\\\\\\n     & = 7 + 5             \\\\\\\\\\n     & = 12\\n\\\\end{align*}\\n\\nAlternative sequence:\\n\\\\begin{align*}\\n     & 1 + (4 + (2 + 5)) \\\\\\\\\\n     & = 1 + (4 + 7)     \\\\\\\\\\n     & = 1 + 11          \\\\\\\\\\n     & = 12\\n\\\\end{align*}\\n\\n\\\\subsection{Example}\\nAdd: $3 + 1 + 4 + 2 + 5$\\n\\n\\\\begin{align*}\\n     & ((((3 + 1) + 4) + 2) + 5) \\\\\\\\\\n     & = ((4 + 4) + 2) + 5       \\\\\\\\\\n     & = (8 + 2) + 5             \\\\\\\\\\n     & = 10 + 5                  \\\\\\\\\\n     & = 15\\n\\\\end{align*}\\n\\n\\\\end{document}\\n```\\n', additional_kwargs={}, response_metadata={})]\n", "Multiple Addition Course Pattern:\n", "steps=[MathStep(id='step1', expression='a_1 + a_2 + a_3 + ... + a_n', operation='initial_expression', is_start=True, is_end=False), MathStep(id='step2', expression='(a_1 + a_2) + a_3 + ... + a_n', operation='group_first_two', is_start=False, is_end=False), MathStep(id='step3', expression='(Result_1 + a_3) + a_4 + ... + a_n', operation='add_next_number', is_start=False, is_end=False), MathStep(id='step4', expression='((a_1 + a_2) + a_3) + a_4 + ... + a_n', operation='grouping', is_start=False, is_end=False), MathStep(id='step5', expression='(Result_2 + a_4) + a_5 + ... + a_n', operation='add_next_number', is_start=False, is_end=False), MathStep(id='step6', expression='(((a_1 + a_2) + a_3) + a_4) + a_5 + ... + a_n', operation='grouping', is_start=False, is_end=False), MathStep(id='step7', expression='(Result_3 + a_5) + a_6 + ... + a_n', operation='add_next_number', is_start=False, is_end=False), MathStep(id='step8', expression='((((a_1 + a_2) + a_3) + a_4) + a_5) + a_6) + ... + a_n', operation='grouping', is_start=False, is_end=False), MathStep(id='step9', expression='Result_n', operation='final_result', is_start=False, is_end=True)] transitions=[MathTransition(source='step1', target='step2', rule='grouping', explanation='Grouped the first two numbers for binary addition.'), MathTransition(source='step2', target='step3', rule='addition', explanation='Added the result of the first group to the next number.'), MathTransition(source='step3', target='step4', rule='grouping', explanation='Continued grouping the results with the next number.'), MathTransition(source='step4', target='step5', rule='addition', explanation='Added the result of the previous group to the next number.'), MathTransition(source='step5', target='step6', rule='grouping', explanation='Grouped the results with the next number.'), MathTransition(source='step6', target='step7', rule='addition', explanation='Added the result of the previous group to the next number.'), MathTransition(source='step7', target='step8', rule='grouping', explanation='Grouped the results with the next number.'), MathTransition(source='step8', target='step9', rule='final_result', explanation='Final result after all additions.')]\n"]}], "source": ["# Define the prompt for extracting the multiple addition course pattern using the addition pattern\n", "MULTIPLE_ADDITION_COURSE_PATTERN_PROMPT = \"\"\"\n", "Given the following mathematical course content in LaTeX format and a previously extracted addition pattern, extract a VERY DETAILED step-by-step explanatory chain for multiple addition. Create a knowledge graph with fine-grained steps that shows exactly how calculations proceed from start to finish.\n", "\n", "The previously extracted addition pattern is:\n", "```\n", "{addition_pattern}\n", "```\n", "\n", "IMPORTANT: Multiple addition builds upon binary addition. Your extracted knowledge graph MUST maintain this hierarchical relationship by:\n", "1. Incorporating the binary addition pattern as a sub-component of the multiple addition process\n", "2. Showing how multiple addition operations decompose into binary addition operations\n", "3. Creating explicit connections between multiple addition steps and their corresponding binary addition steps\n", "4. Preserving the hierarchical structure where multiple addition is defined in terms of binary addition\n", "\n", "Focus on identifying:\n", "1. Every individual calculation step in the multiple addition process\n", "2. How multiple addition decomposes into simpler binary addition problems\n", "3. How parentheses are used to group terms for binary addition\n", "4. The precise sequence of operations with clear predecessor-successor relationships\n", "5. Intermediate results at each calculation stage\n", "\n", "The final knowledge graph MUST:\n", "1. Have clearly marked START node(s) (the initial multiple addition problem)\n", "2. Have clearly marked END node(s) (the final result)\n", "3. Include ALL intermediate calculation steps with no gaps in reasoning\n", "4. Form a single connected component with a clear directional flow\n", "5. Use relationship types that precisely describe the mathematical operations\n", "6. Show the hierarchical relationship between multiple addition and binary addition\n", "\n", "Extract a calculation graph with steps and transitions that represent this detailed calculation process.\n", "\n", "Note:\n", "- Steps should be specified with short mathematical expressions.\n", "- Transitions should explain all the minor steps of the reasoning, including how multiple addition steps decompose into binary addition steps.\n", "\n", "Course Content:\n", "```\n", "{course_latex}\n", "```\n", "\"\"\"\n", "\n", "# Extract the multiple addition course pattern using the addition pattern\n", "multiple_addition_course_pattern = extract_calculation_graph(\n", "    custom_prompt=MULTIPLE_ADDITION_COURSE_PATTERN_PROMPT.format(\n", "        addition_pattern=addition_course_pattern,\n", "        course_latex=multiple_addition_course_latex\n", "    ),\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "print(\"Multiple Addition Course Pattern:\")\n", "print(multiple_addition_course_pattern)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Storing Patterns in Neo4j\n", "\n", "Now we'll store both patterns in Neo4j to visualize the hierarchical relationship between binary addition and multiple addition. This will allow us to see how multiple addition operations build upon binary addition operations."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Patterns stored in Neo4j database.\n"]}], "source": ["# Store the patterns in Neo4j\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"neo4j\", \"password\"))\n", "neo4j.clean_database()\n", "\n", "# Function to sanitize LaTeX commands in relationship names\n", "def sanitize_latex(text):\n", "    # Replace LaTeX commands with plain text alternatives\n", "    sanitized = text.replace(\"\\\\underbrace\", \"\")\n", "    sanitized = sanitized.replace(\"\\\\text\", \"\")\n", "    sanitized = sanitized.replace(\"\\\\cdots\", \"...\")\n", "    sanitized = sanitized.replace(\"\\\\times\", \"×\")\n", "    # Remove LaTeX subscripts and superscripts\n", "    sanitized = re.sub(r\"_{.*?}\", \"\", sanitized)\n", "    sanitized = re.sub(r\"\\^{.*?}\", \"\", sanitized)\n", "    return sanitized\n", "\n", "# Sanitize expressions in multiple addition pattern\n", "for step in multiple_addition_course_pattern.steps:\n", "    step.expression = sanitize_latex(step.expression)\n", "\n", "# Store the addition pattern\n", "neo4j.store_calculation_graph(addition_course_pattern, \"addition_course\")\n", "\n", "# Store the multiple addition pattern\n", "neo4j.store_calculation_graph(multiple_addition_course_pattern, \"multiple_addition_course\")\n", "\n", "print(\"Patterns stored in Neo4j database.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Testing the Hierarchical Pattern\n", "\n", "Now let's test our hierarchical pattern by applying it to a specific multiple addition problem. We'll see how the pattern breaks down the problem into a sequence of binary additions."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[SystemMessage(content='You are an expert in mathematical calculation analysis, specializing in extracting structured knowledge graph from mathematical calculation texts. Your task is to identify quantom/detailed progress/procedural steps in a mathematical calculation process and represent them as a fine-grained knowledge graph with explicit step-by-step reasoning.', additional_kwargs={}, response_metadata={}), HumanMessage(content='\\nGiven the following multiple addition problem and the hierarchical patterns for binary addition and multiple addition, \\ncreate a detailed step-by-step solution that follows these patterns.\\n\\nBinary Addition Pattern:\\n```\\nsteps=[MathStep(id=\\'step1\\', expression=\\'a + b\\', operation=\\'initial_expression\\', is_start=True, is_end=False), MathStep(id=\\'step2\\', expression=\\'(a + (b-1)) + 1\\', operation=\\'decompose_b\\', is_start=False, is_end=False), MathStep(id=\\'step3\\', expression=\\'((a + (b-2)) + 1) + 1\\', operation=\\'decompose_b\\', is_start=False, is_end=False), MathStep(id=\\'step4\\', expression=\\'\\\\x7f\\\\x7f\\\\x7f\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step5\\', expression=\\'(a + 0) + 1 + 1 + ... + 1\\', operation=\\'continue_decomposition\\', is_start=False, is_end=False), MathStep(id=\\'step6\\', expression=\\'a + (1 + 1 + ... + 1)\\', operation=\\'simplify_expression\\', is_start=False, is_end=False), MathStep(id=\\'step7\\', expression=\\'a + b\\', operation=\\'final_result\\', is_start=False, is_end=True)] transitions=[MathTransition(source=\\'step1\\', target=\\'step2\\', rule=\\'decomposition\\', explanation=\\'Decomposed b into (b-1) and added 1.\\'), MathTransition(source=\\'step2\\', target=\\'step3\\', rule=\\'decomposition\\', explanation=\\'Decomposed (b-1) into (b-2) and added 1.\\'), MathTransition(source=\\'step3\\', target=\\'step4\\', rule=\\'decomposition\\', explanation=\\'Continued decomposing b until reaching 0.\\'), MathTransition(source=\\'step4\\', target=\\'step5\\', rule=\\'decomposition\\', explanation=\"Expressed the addition in terms of 0 and added 1\\'s.\"), MathTransition(source=\\'step5\\', target=\\'step6\\', rule=\\'simplification\\', explanation=\\'Simplified the expression to a + (1 + 1 + ... + 1).\\'), MathTransition(source=\\'step6\\', target=\\'step7\\', rule=\\'final_result\\', explanation=\\'Recognized that adding 1, b times results in a + b.\\')]\\n```\\n\\nMultiple Addition Pattern:\\n```\\nsteps=[MathStep(id=\\'step1\\', expression=\\'a_1 + a_2 + a_3 + ... + a_n\\', operation=\\'initial_expression\\', is_start=True, is_end=False), MathStep(id=\\'step2\\', expression=\\'(a_1 + a_2) + a_3 + ... + a_n\\', operation=\\'group_first_two\\', is_start=False, is_end=False), MathStep(id=\\'step3\\', expression=\\'(Result_1 + a_3) + a_4 + ... + a_n\\', operation=\\'add_next_number\\', is_start=False, is_end=False), MathStep(id=\\'step4\\', expression=\\'((a_1 + a_2) + a_3) + a_4 + ... + a_n\\', operation=\\'grouping\\', is_start=False, is_end=False), MathStep(id=\\'step5\\', expression=\\'(Result_2 + a_4) + a_5 + ... + a_n\\', operation=\\'add_next_number\\', is_start=False, is_end=False), MathStep(id=\\'step6\\', expression=\\'(((a_1 + a_2) + a_3) + a_4) + a_5 + ... + a_n\\', operation=\\'grouping\\', is_start=False, is_end=False), MathStep(id=\\'step7\\', expression=\\'(Result_3 + a_5) + a_6 + ... + a_n\\', operation=\\'add_next_number\\', is_start=False, is_end=False), MathStep(id=\\'step8\\', expression=\\'((((a_1 + a_2) + a_3) + a_4) + a_5) + a_6) + ... + a_n\\', operation=\\'grouping\\', is_start=False, is_end=False), MathStep(id=\\'step9\\', expression=\\'Result_n\\', operation=\\'final_result\\', is_start=False, is_end=True)] transitions=[MathTransition(source=\\'step1\\', target=\\'step2\\', rule=\\'grouping\\', explanation=\\'Grouped the first two numbers for binary addition.\\'), MathTransition(source=\\'step2\\', target=\\'step3\\', rule=\\'addition\\', explanation=\\'Added the result of the first group to the next number.\\'), MathTransition(source=\\'step3\\', target=\\'step4\\', rule=\\'grouping\\', explanation=\\'Continued grouping the results with the next number.\\'), MathTransition(source=\\'step4\\', target=\\'step5\\', rule=\\'addition\\', explanation=\\'Added the result of the previous group to the next number.\\'), MathTransition(source=\\'step5\\', target=\\'step6\\', rule=\\'grouping\\', explanation=\\'Grouped the results with the next number.\\'), MathTransition(source=\\'step6\\', target=\\'step7\\', rule=\\'addition\\', explanation=\\'Added the result of the previous group to the next number.\\'), MathTransition(source=\\'step7\\', target=\\'step8\\', rule=\\'grouping\\', explanation=\\'Grouped the results with the next number.\\'), MathTransition(source=\\'step8\\', target=\\'step9\\', rule=\\'final_result\\', explanation=\\'Final result after all additions.\\')]\\n```\\n\\nProblem: 2 + 3 + 4 + 5\\n\\nProvide a detailed solution that:\\n1. First applies the multiple addition pattern to break down the problem into binary additions\\n2. Then applies the binary addition pattern for each binary addition step\\n3. Shows all intermediate steps and calculations\\n4. Maintains the hierarchical relationship between multiple addition and binary addition\\n\\nExtract a calculation graph with steps and transitions that represent this solution.\\n', additional_kwargs={}, response_metadata={})]\n", "Solution for test problem:\n", "steps=[MathStep(id='step1', expression='2 + 3 + 4 + 5', operation='initial_expression', is_start=True, is_end=False), MathStep(id='step2', expression='(2 + 3) + 4 + 5', operation='group_first_two', is_start=False, is_end=False), MathStep(id='step3', expression='(Result_1 + 4) + 5', operation='add_next_number', is_start=False, is_end=False), MathStep(id='step4', expression='((2 + 3) + 4) + 5', operation='grouping', is_start=False, is_end=False), MathStep(id='step5', expression='(Result_2 + 5)', operation='add_next_number', is_start=False, is_end=False), MathStep(id='step6', expression='Result_3', operation='final_result', is_start=False, is_end=True), MathStep(id='step7', expression='2 + 3', operation='initial_expression', is_start=True, is_end=False), MathStep(id='step8', expression='(2 + (3-1)) + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step9', expression='((2 + (3-2)) + 1) + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step10', expression='((2 + (3-3)) + 1) + 1 + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step11', expression='(2 + 0) + 1 + 1 + 1', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step12', expression='2 + (1 + 1 + 1)', operation='simplify_expression', is_start=False, is_end=False), MathStep(id='step13', expression='5', operation='final_result', is_start=False, is_end=True), MathStep(id='step14', expression='Result_1 + 4', operation='initial_expression', is_start=True, is_end=False), MathStep(id='step15', expression='(Result_1 + (4-1)) + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step16', expression='((Result_1 + (4-2)) + 1) + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step17', expression='((Result_1 + (4-3)) + 1) + 1 + 1', operation='decompose_b', is_start=False, is_end=False), MathStep(id='step18', expression='(Result_1 + 1) + 1 + 1', operation='continue_decomposition', is_start=False, is_end=False), MathStep(id='step19', expression='Result_1 + (1 + 1 + 1)', operation='simplify_expression', is_start=False, is_end=False), MathStep(id='step20', expression='Result_2 + 5', operation='final_result', is_start=False, is_end=True)] transitions=[MathTransition(source='step1', target='step2', rule='grouping', explanation='Grouped the first two numbers for addition.'), MathTransition(source='step2', target='step3', rule='addition', explanation='Added the result of the first group to the next number.'), MathTransition(source='step3', target='step4', rule='grouping', explanation='Continued grouping the results with the next number.'), MathTransition(source='step4', target='step5', rule='addition', explanation='Added the result of the previous group to the next number.'), MathTransition(source='step5', target='step6', rule='final_result', explanation='Final result after all additions.'), MathTransition(source='step7', target='step8', rule='decomposition', explanation='Decomposed 3 into (3-1) and added 1.'), MathTransition(source='step8', target='step9', rule='decomposition', explanation='Decomposed (3-1) into (3-2) and added 1.'), MathTransition(source='step9', target='step10', rule='decomposition', explanation='Decomposed (3-2) into (3-3) and added 1.'), MathTransition(source='step10', target='step11', rule='decomposition', explanation=\"Expressed the addition in terms of 0 and added 1's.\"), MathTransition(source='step11', target='step12', rule='simplification', explanation='Simplified the expression to 2 + (1 + 1 + 1).'), MathTransition(source='step12', target='step13', rule='final_result', explanation='Final result of 2 + 3 is 5.'), MathTransition(source='step14', target='step15', rule='decomposition', explanation='Decomposed 4 into (4-1) and added 1.'), MathTransition(source='step15', target='step16', rule='decomposition', explanation='Decomposed (4-1) into (4-2) and added 1.'), MathTransition(source='step16', target='step17', rule='decomposition', explanation='Decomposed (4-2) into (4-3) and added 1.'), MathTransition(source='step17', target='step18', rule='decomposition', explanation=\"Expressed the addition in terms of 1 and added 1's.\"), MathTransition(source='step18', target='step19', rule='simplification', explanation='Simplified the expression to Result_1 + (1 + 1 + 1).'), MathTransition(source='step19', target='step20', rule='final_result', explanation='Final result after adding 4 to Result_1.')]\n", "Test solution stored in Neo4j database.\n"]}], "source": ["# Example: Load a test problem and apply the hierarchical pattern\n", "test_problem = \"2 + 3 + 4 + 5\"\n", "\n", "# Define a prompt to apply the hierarchical pattern to the test problem\n", "TEST_PROBLEM_PROMPT = \"\"\"\n", "Given the following multiple addition problem and the hierarchical patterns for binary addition and multiple addition, \n", "create a detailed step-by-step solution that follows these patterns.\n", "\n", "Binary Addition Pattern:\n", "```\n", "{addition_pattern}\n", "```\n", "\n", "Multiple Addition Pattern:\n", "```\n", "{multiple_addition_pattern}\n", "```\n", "\n", "Problem: {problem}\n", "\n", "Provide a detailed solution that:\n", "1. First applies the multiple addition pattern to break down the problem into binary additions\n", "2. Then applies the binary addition pattern for each binary addition step\n", "3. Shows all intermediate steps and calculations\n", "4. Maintains the hierarchical relationship between multiple addition and binary addition\n", "\n", "Extract a calculation graph with steps and transitions that represent this solution.\n", "\"\"\"\n", "\n", "# Apply the hierarchical pattern to the test problem\n", "test_solution = extract_calculation_graph(\n", "    custom_prompt=TEST_PROBLEM_PROMPT.format(\n", "        addition_pattern=addition_course_pattern,\n", "        multiple_addition_pattern=multiple_addition_course_pattern,\n", "        problem=test_problem,\n", "    ),\n", "    system_message=SYSTEM_PROMPT,\n", ")\n", "\n", "print(\"Solution for test problem:\")\n", "print(test_solution)\n", "\n", "# Store the test solution in Neo4j\n", "neo4j.store_calculation_graph(test_solution, \"test_solution\")\n", "print(\"Test solution stored in Neo4j database.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization Queries\n", "\n", "Here are some Cypher queries you can run in the Neo4j browser to visualize the hierarchical patterns:\n", "\n", "1. View the binary addition pattern:\n", "```cypher\n", "MATCH (n)-[r]->(m) \n", "WHERE n.graph_type = \"addition_course\" AND m.graph_type = \"addition_course\"\n", "RETURN n, r, m\n", "```\n", "\n", "2. View the multiple addition pattern:\n", "```cypher\n", "MATCH (n)-[r]->(m) \n", "WHERE n.graph_type = \"multiple_addition_course\" AND m.graph_type = \"multiple_addition_course\"\n", "RETURN n, r, m\n", "```\n", "\n", "3. View the test solution:\n", "```cypher\n", "MATCH (n)-[r]->(m) \n", "WHERE n.graph_type = \"test_solution\" AND m.graph_type = \"test_solution\"\n", "RETURN n, r, m\n", "```"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}