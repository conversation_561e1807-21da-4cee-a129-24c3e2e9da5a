{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Phase 1: Extracting Triplets from Informal Proofs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add src to the Python Path in the Notebook"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Read Informal Proves\n", "Read the LaTeX file. This file contains the informal proofs of the theorems in the book."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/latex": ["## سوال\n", "\n", "اثبات کنید که:\n", "\\begin{equation}\n", "    \\left(\\frac{1}{\\cos a} + \\tan a\\right)(1 - \\sin a) = \\cos a.\n", "\\end{equation}\n", "\n", "## پا<PERSON>خ\n", "\n", "\n", "با نوشتن تابع تانژانت به‌صورت کسر داریم:\n", "\\begin{equation}\n", "    \\tan a = \\frac{\\sin a}{\\cos a} \\Rightarrow \\frac{1}{\\cos a} + \\tan a = \\frac{1 + \\sin a}{\\cos a}.\n", "\\end{equation}\n", "\n", "حال ضرب در $(1 - \\sin a)$ را انجام می‌دهیم:\n", "\\begin{equation}\n", "    \\frac{(1 + \\sin a)(1 - \\sin a)}{\\cos a}.\n", "\\end{equation}\n", "\n", "چون $1 - \\sin^2 a = \\cos^2 a$، داریم:\n", "\\begin{equation}\n", "    \\frac{\\cos^2 a}{\\cos a} = \\cos a.\n", "\\end{equation}\n", "\n", "پس اتحاد برقرار است."], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "\n", "# Load LaTeX proof\n", "proof_latex = read_proof(\"../../data/proofs/persian/problem3/proof2.tex\")\n", "\n", "# Find the start and end positions\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "\n", "# Extract the content between \\begin{document} and \\end{document}\n", "informal_proof = proof_latex[start:end].strip()\n", "\n", "\n", "# Replace any \\section{...} with ## ...\n", "informal_proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", informal_proof)\n", "\n", "# Display the LaTeX content\n", "display(Latex(informal_proof))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Extract Triplet proofs from Informal Proofs"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["Triplet(entities=[Entity(id='1', name='tan a', label='Tangent of angle a', type='Function'), Entity(id='2', name='sin a', label='Sine of angle a', type='Function'), Entity(id='3', name='cos a', label='Cosine of angle a', type='Function'), Entity(id='4', name='1 - sin^2 a', label='Identity involving sine', type='Expression'), Entity(id='5', name='cos^2 a', label='Square of cosine of angle a', type='Expression'), Entity(id='6', name='(1 + sin a)(1 - sin a)', label='Product of expressions', type='Expression'), Entity(id='7', name='(1/cos a + tan a)(1 - sin a)', label='Left-hand side of the equation', type='Expression'), Entity(id='8', name='cos a', label='Right-hand side of the equation', type='Expression')], relations=[Relation(source='1', target='2', type='explains', name='Tangent definition'), Relation(source='2', target='3', type='explains', name='Sine and cosine relationship'), Relation(source='4', target='5', type='identity', name='Pythagorean identity'), Relation(source='6', target='4', type='substitutes', name='Difference of squares'), Relation(source='7', target='8', type='proves', name='Equality of both sides')])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.phase1.extract_triplets import extract_triplets\n", "\n", "# Extract triplets\n", "triplet = extract_triplets(informal_proof)\n", "triplet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Store Triplets into Neo4J DB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Initialize Neo4JUtils\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"fanavaran\", \"fanavaran\"))\n", "\n", "# Clean the database (delete all nodes and relationships)\n", "neo4j.clean_database()\n", "\n", "# Add nodes and relationships with step tracking\n", "for entity in triplet.entities:\n", "    neo4j.create_node(entity)  # Uses the current step (default is 0)\n", "for relation in triplet.relations:\n", "    neo4j.create_relation(relation)  # Uses the current step (default is 0)\n", "\n", "# Increment the step counter for the next set of changes\n", "neo4j.increment_step()\n", "\n", "# Clean the database (delete nodes and relationships with step > 1)\n", "neo4j.clean_database(step=1)\n", "\n", "# Add or modify nodes and relationships in the next step\n", "# Example:\n", "# neo4j.create_node(new_entity)  # This will use the updated step counter (1)\n", "# neo4j.create_relation(new_relation)  # This will use the updated step counter (1)\n", "\n", "# Close the connection\n", "neo4j.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}