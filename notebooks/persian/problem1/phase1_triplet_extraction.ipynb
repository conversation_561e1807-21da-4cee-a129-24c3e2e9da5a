{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Phase 1: Extracting Triplets from Informal Proofs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add src to the Python Path in the Notebook"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "import os\n", "\n", "\n", "# Add the project root directory to the Python path\n", "sys.path.append(os.path.abspath(os.path.join(\"../../..\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Read Informal Proves\n", "Read the LaTeX file. This file contains the informal proofs of the theorems in the book."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/latex": ["## سوال\n", "\n", "نشان دهید که دو عدد گنگ $a$ و $b$ پیدا می‌شود به‌طوری که $a^{b}$ یک عدد گویا است.\n", "\n", "## پا<PERSON>خ\n", "\n", "$\\sqrt{2}^{\\sqrt{2}}$ یا یک عدد گویا است که کار تمام است (فرض کنید $a=b=\\sqrt{2}$)، یا $\\sqrt{2}^{\\sqrt{2}}$ گنگ است پس می‌توانیم بنویسیم: $a=\\sqrt{2}^{\\sqrt{2}}$ و $b=\\sqrt{2}$. پس خواهیم داشت $\\left(\\sqrt{2}^{\\sqrt{2}}\\right)^{\\sqrt{2}}=\\sqrt{2}^{2}=2$، که عددی گویا از فرم $a^{b}$ است."], "text/plain": ["<IPython.core.display.Latex object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "import re\n", "from src.utils.file_utils import read_proof\n", "\n", "# Load LaTeX proof\n", "proof_latex = read_proof(\"../../data/proofs/persian/problem1/proof1.tex\")\n", "\n", "# Find the start and end positions\n", "start = proof_latex.find(r\"\\begin{document}\") + len(r\"\\begin{document}\")\n", "end = proof_latex.find(r\"\\end{document}\")\n", "\n", "# Extract the content between \\begin{document} and \\end{document}\n", "informal_proof = proof_latex[start:end].strip()\n", "\n", "\n", "# Replace any \\section{...} with ## ...\n", "informal_proof = re.sub(r\"\\\\section\\{([^}]+)\\}\", r\"## \\1\", informal_proof)\n", "\n", "# Display the LaTeX content\n", "display(Latex(informal_proof))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Extract Triplet proofs from Informal Proofs"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["Triplet(entities=[Entity(id='1', name='a', label='a', type='variable'), Entity(id='2', name='b', label='b', type='variable'), Entity(id='3', name='sqrt(2)', label='sqrt(2)', type='constant'), Entity(id='4', name='sqrt(2)^{sqrt(2)}', label='sqrt(2)^{sqrt(2)}', type='expression'), Entity(id='5', name='2', label='2', type='constant'), Entity(id='6', name='rational number', label='rational number', type='concept')], relations=[Relation(source='4', target='6', type='grounds', name='is a rational number if a=b=sqrt(2)'), Relation(source='1', target='4', type='defines', name='a is defined as sqrt(2)^{sqrt(2)}'), Relation(source='2', target='3', type='defines', name='b is defined as sqrt(2)'), Relation(source='4', target='5', type='explains', name='sqrt(2)^{sqrt(2)}^{sqrt(2)} equals 2')])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from src.phase1.extract_triplets import extract_triplets\n", "\n", "# Extract triplets\n", "triplet = extract_triplets(informal_proof)\n", "triplet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Store Triplets into Neo4J DB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utils.neo4j_utils import Neo4JUtils\n", "\n", "# Initialize Neo4JUtils\n", "neo4j = Neo4JUtils(\"bolt://localhost:7687\", (\"fanavaran\", \"fanavaran\"))\n", "\n", "# Clean the database (delete all nodes and relationships)\n", "neo4j.clean_database()\n", "\n", "# Add nodes and relationships with step tracking\n", "for entity in triplet.entities:\n", "    neo4j.create_node(entity)  # Uses the current step (default is 0)\n", "for relation in triplet.relations:\n", "    neo4j.create_relation(relation)  # Uses the current step (default is 0)\n", "\n", "# Increment the step counter for the next set of changes\n", "neo4j.increment_step()\n", "\n", "# Clean the database (delete nodes and relationships with step > 1)\n", "neo4j.clean_database(step=1)\n", "\n", "# Add or modify nodes and relationships in the next step\n", "# Example:\n", "# neo4j.create_node(new_entity)  # This will use the updated step counter (1)\n", "# neo4j.create_relation(new_relation)  # This will use the updated step counter (1)\n", "\n", "# Close the connection\n", "neo4j.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}