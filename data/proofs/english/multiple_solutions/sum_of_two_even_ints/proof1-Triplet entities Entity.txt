Triplet(
	entities=[
		Entity(id='1', name='a', label='even integer', type='variable'),
		Entity(id='2', name='b', label='even integer', type='variable'),
		Entity(id='3', name='m', label='integer', type='variable'),
		Entity(id='4', name='n', label='integer', type='variable'),
		Entity(id='5', name='a + b', label='sum of two even integers', type='expression'),
		Entity(id='6', name='2(m + n)', label='factored sum', type='expression'),
		Entity(id='7', name='even integer', label='conclusion', type='property')
	], 
	relations=[
		Relation(source='1', target='3', type='defines', name='definition of even integer'),
		Relation(source='2', target='4', type='defines', name='definition of even integer'),
		Relation(source='1', target='5', type='contributes to', name='sum of a and b'),
		Relation(source='2', target='5', type='contributes to', name='sum of a and b'),
		Relation(source='5', target='6', type='transforms to', name='factoring out 2'),
		Relation(source='6', target='7', type='concludes', name='sum is even')
	]
)

