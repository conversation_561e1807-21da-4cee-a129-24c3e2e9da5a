\documentclass{article}
\usepackage{amsmath}
\usepackage{amssymb}
\title{Introduction to Subtraction via Recursion}
\author{}
\date{}

\begin{document}
\maketitle

\section{Definition}
Subtraction of two non-negative integers \( a \) and \( b \), denoted \( a - b \), can be defined recursively using the predecessor operation (decrementing by 1). This definition assumes \( a \geq b \), as negative results are not covered here:

\begin{itemize}
    \item \textbf{Base Case}: If \( b = 0 \), then \( a - b = a \).
    \item \textbf{Recursive Case}: If \( b > 0 \), then \( a - b = (a - 1) - (b - 1) \).
\end{itemize}

This reduces subtraction by \( b \) to recursively subtracting 1 from both \( a \) and \( b \) until \( b = 0 \).

\section{Examples}

\subsection{Example 1: \( 5 - 0 \)}
Applying the base case directly:
\[
5 - 0 = 5
\]

\subsection{Example 2: \( 5 - 3 \)}
Breaking down the recursion step-by-step:
\[
\begin{aligned}
5 - 3 &= (5 - 1) - (3 - 1) \\
&= 4 - 2 \\
&= (4 - 1) - (2 - 1) \\
&= 3 - 1 \\
&= (3 - 1) - (1 - 1) \\
&= 2 - 0 \\
&= 2
\end{aligned}
\]

\subsection{Example 3: \( 6 - 6 \)}
Demonstrating equal values:
\[
\begin{aligned}
6 - 6 &= (6 - 1) - (6 - 1) \\
&= 5 - 5 \\
&= (5 - 1) - (5 - 1) \\
&= 4 - 4 \\
&\;\;\vdots \quad \text{(repeating until base case)} \\
&= 0 - 0 \\
&= 0
\end{aligned}
\]

\subsection{Example 4: \( 4 - 2 \)}
Recursive steps:
\[
\begin{aligned}
4 - 2 &= (4 - 1) - (2 - 1) \\
&= 3 - 1 \\
&= (3 - 1) - (1 - 1) \\
&= 2 - 0 \\
&= 2
\end{aligned}
\]

\subsection{Example 5: \( 3 - 1 \)}
Simpler case:
\[
\begin{aligned}
3 - 1 &= (3 - 1) - (1 - 1) \\
&= 2 - 0 \\
&= 2
\end{aligned}
\]

\section{Important Notes}
\begin{itemize}
    \item This definition assumes \( a \geq b \). If \( a < b \), the recursion would attempt to decrement \( a \) below 0, which is undefined here.
    \item Subtraction is not commutative: \( a - b \neq b - a \) (unless \( a = b \)).
\end{itemize}

\section{Conclusion}
This recursive framework reduces subtraction to repeated decrementing of both \( a \) and \( b \), terminating when \( b = 0 \). It highlights how recursion simplifies operations by breaking them into incremental steps. However, it is restricted to non-negative results, emphasizing the importance of \( a \geq b \).

\end{document}