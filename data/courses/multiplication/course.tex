\documentclass{article}
\usepackage{amsmath}
\usepackage{amssymb}
\title{Introduction to Multiplication via Recursion}

\begin{document}
\maketitle

\section{Recursive Definition}
For non-negative integers \(a\) and \(b\):

\[
    a \times b = \begin{cases}
        0                      & \text{if } b = 0 \quad \text{(Base Case)}      \\
        a + (a \times (b - 1)) & \text{if } b > 0 \quad \text{(Recursive Case)}
    \end{cases}
\]

\section{Expanded Recursion Steps}
The recursive case reduces multiplication to repeated addition through these steps:

\begin{enumerate}
    \item \textbf{Initial Problem}: \(a \times b\) where \(b > 0\)

    \item \textbf{Recursive Decomposition}:
          \[
              a \times b = a + \underbrace{(a \times (b - 1))}_{\text{Simpler subproblem}}
          \]
          This creates:
          \begin{itemize}
              \item A simpler subproblem: \(a \times (b - 1)\)
              \item A pending operation: \(+ a\)
          \end{itemize}

    \item \textbf{Iterative Reduction}:
          Repeat until reaching the base case:
          \[
              \begin{aligned}
                   & a \times b                                                       \\
                   & \Downarrow                                                       \\
                   & a + (a \times (b-1))                                             \\
                   & \Downarrow                                                       \\
                   & a + (a + (a \times (b-2)))                                       \\
                   & \Downarrow                                                       \\
                   & \quad \vdots                                                     \\
                   & \Downarrow                                                       \\
                   & \underbrace{a + a + \cdots + a}_{b \text{ times}} + (a \times 0) \\
              \end{aligned}
          \]

    \item \textbf{Base Case Resolution}:
          When \(b - n = 0\):
          \[
              \underbrace{a + a + \cdots + a}_{b \text{ times}} + \underbrace{0}_{\text{Base case}}
          \]

    \item \textbf{Result Construction}:
          \[
              \underbrace{a + a + \cdots + a}_{b \text{ times}} = a \times b
          \]
\end{enumerate}

\section{Complete Recursion Example}
For \(3 \times 2\):

\[
    \begin{aligned}
        3 \times 2 & = 3 + (3 \times 1) \quad       & \text{(First decomposition)}  \\
                   & = 3 + (3 + (3 \times 0)) \quad & \text{(Second decomposition)} \\
                   & = 3 + (3 + 0) \quad            & \text{(Base case applied)}    \\
                   & = 3 + 3 \quad                  & \text{(Simplify)}             \\
                   & = 6 \quad                      & \text{(Final result)}
    \end{aligned}
\]

\section*{Recursion Pattern}
General form for \(a \times b\):

\[
    \begin{aligned}
        a \times b & = a + (a \times (b-1))                                             \\
                   & = a + (a + (a \times (b-2)))                                       \\
                   & \;\;\vdots                                                         \\
                   & = \underbrace{a + a + \cdots + a}_{b \text{ times}} + (a \times 0) \\
                   & = \underbrace{a + a + \cdots + a}_{b \text{ times}}                \\
                   & = a \times b
    \end{aligned}
\]

\end{document}