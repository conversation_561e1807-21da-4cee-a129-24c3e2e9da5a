\documentclass[12pt]{article}
\usepackage{amsmath, amssymb, amsthm}
\usepackage{enumitem}
\usepackage{geometry}
\geometry{margin=1in}

\title{Steps in an Informal Mathematical Proof}
\begin{document}

\maketitle

\section*{Classification of Steps in an Informal Mathematical Proof}

Informal proofs often do not follow a rigid logical format but instead unfold through a sequence of rhetorical and epistemic moves. This move is encapsulated in the term explanatory chain. Each step follows from the previous one as a mathematical result. A good proof does not include an explanatory jump. Below is a classification of common steps that appear in informal proofs. The goal of creating graphs is to represent the flow of informal reasoning. 

\begin{enumerate}[label=\textbf{\arabic*.}]
    \item \textbf{Framing the Problem (Orientation)}
    \begin{itemize}
        \item Rephrasing or interpreting the theorem informally.
        \item Introducing notation and assumptions.
        \item Describing intuitions or visualizing the problem.
    \end{itemize}

    \item \textbf{Assembling Tools}
    \begin{itemize}
        \item Citing known results, definitions, or heuristics.
        \item Stating useful lemmas or propositions.
        \item Drawing analogies with familiar problems.
    \end{itemize}

    \item \textbf{Identifying the Key Insight or Strategy}
    \begin{itemize}
        \item Highlighting the central idea or trick.
        \item Suggesting a change of perspective or transformation.
        \item Describing the planned method (e.g., contradiction, induction).
    \end{itemize}

    \item \textbf{Developing the Argument}
    \begin{itemize}
        \item Proceeding step-by-step from assumptions to conclusion.
        \item Using logical implications, definitions, and intermediate results.
        \item Including intuitive commentary or explanatory remarks.
    \end{itemize}

    \item \textbf{Intermediate Constructions or Contradictions}
    \begin{itemize}
        \item Constructing objects (e.g., numbers, sets) with desired properties.
        \item Dividing into cases, if necessary.
        \item Deriving contradictions if proof by contradiction is employed.
    \end{itemize}

    \item \textbf{Classification of the Steps}
    \begin{itemize}
        \item A major mathematical fact is called a theorem. 
        \item A minor mathematical fact is called a lemma.
        \item An easy conclusion of a theorem is a corollary. 
        \item A true mathematical proposition is called a fact. 
    \end{itemize}
    
    \item \textbf{Concluding the Proof}
    \begin{itemize}
        \item Summarizing how the result was obtained.
        \item Reiterating the initial goal and confirming its achievement.
        \item Optionally reflecting on the method or result.
    \end{itemize}

    \item \textbf{Optional: Explanatory or Pedagogical Enhancements}
    \begin{itemize}
        \item Motivational asides and remarks on intuition.
        \item Use of diagrams or informal sketches.
        \item Analogical reasoning or connections to related problems.
    \end{itemize}

\end{enumerate}

\end{document}