\documentclass{article}
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{amssymb}  % Add this line to define \mathbb{N}

\title{Introduction to Mathematical Induction with Examples}
\author{Math Course}
\date{\today}

\newtheorem{theorem}{Theorem}

\begin{document}

\maketitle

\section{What is Mathematical Induction?}
Mathematical induction is a powerful proof technique used in mathematics to prove statements that are asserted to be true for all natural numbers. It is especially useful for proving propositions about:
\begin{itemize}
    \item Summations and series
    \item Divisibility properties
    \item Inequalities
    \item Combinatorial identities
\end{itemize}

\section{The Principle of Mathematical Induction}
\begin{theorem}[Principle of Mathematical Induction]
    To prove that a proposition $P(n)$ is true for all natural numbers $n \geq n_0$, it suffices to:
    \begin{enumerate}
        \item \textbf{Base Case:} Verify $P(n_0)$ is true
        \item \textbf{Inductive Step:} Show that if $P(k)$ is true for some arbitrary $k \geq n_0$ (called the induction hypothesis), then $P(k+1)$ must also be true
    \end{enumerate}
\end{theorem}

\section{The Domino Analogy}
Mathematical induction works like falling dominos:
\begin{itemize}
    \item The base case is like knocking over the first domino
    \item The inductive step ensures each domino will knock over the next one
    \item Together, these guarantee that all dominos will fall
\end{itemize}

\section{Key Points to Remember}
\begin{itemize}
    \item Always verify both the base case and inductive step
    \item The induction hypothesis is crucial - you must assume $P(k)$ is true
    \item Mathematical induction proves statements for \textit{all} natural numbers beyond the base case
    \item Choose the appropriate base case ($n_0$) for your proposition
\end{itemize}

\end{document}