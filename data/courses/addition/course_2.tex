\documentclass{article}
\usepackage{amsmath}
\usepackage{amssymb}

\begin{document}
\section{Title}
Addition Via Recursion

\section{Abstract}
Recursion is an effective way to add numbers. Using recursion, a more complicated addition is reduced to simpler additions. Hence, through a recursive procedure, it turns into several steps of addition by $1$, where $a+1$ denotes the next number.
\section{Procedure}
\[
    \begin{aligned}
         & a + b                              \\
         & \Downarrow                         \\
         & (a + (b-1)) + 1                    \\
         & \Downarrow                         \\
         & ((a + (b-2)) + 1) + 1              \\
         & \Downarrow                         \\
         & \quad \vdots                       \\
         & \Downarrow                         \\
         & (\cdots((a + 0) + 1) + \cdots + 1) \\
    \end{aligned}
\]
\section{Examples}
\subsection{Example 1}
\subsubsection{Question}
Calculate this \(3 + 2\):
\subsubsection{Answer}
\[
    \begin{aligned}
        3 + 2 & = (3 + 1) + 1 \quad       & \text{(First decomposition)}  \\
              & = ((3 + 0) + 1) + 1 \quad & \text{(Second decomposition)} \\
              & = (3 + 1) + 1 \quad       & \text{(Base case applied)}    \\
              & = 4 + 1 \quad             & \text{(First increment)}      \\
              & = 5 \quad                 & \text{(Final result)}
    \end{aligned}
\]

\subsection{Example 2}
\subsubsection{Question}
Calculate the result of \(a + b\):
\subsubsection{Answer}
\[
    \begin{aligned}
        a + b & = (a + (b-1)) + 1                                       \\
              & = ((a + (b-2)) + 1) + 1                                 \\
              & \;\;\vdots                                              \\
              & = (\cdots((a + 0) + 1) + \cdots + 1)                    \\
              & = a + \underbrace{1 + 1 + \cdots + 1}_{b \text{ times}} \\
              & = a + b
    \end{aligned}
\]

\end{document}
