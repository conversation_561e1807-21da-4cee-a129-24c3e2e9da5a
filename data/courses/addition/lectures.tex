\documentclass[11pt]{report}
\include{macros}
\usepackage{graphicx}
\usepackage{pstricks}
\DeclareMathOperator{\Li}{Li}
\newcommand{\ul}[1]{\underline{#1}}
\newcounter{theprob}\newcounter{thesubprob}
\newenvironment{problems}
   {\begin{list}{\arabic{theprob}.}{\usecounter{theprob}}}
   {\end{list}}
\newenvironment{subprob}
   {\begin{list}{(\roman{thesubprob})}{\usecounter{thesubprob}}}
   {\end{list}}
\renewcommand{\vtwo}[2]{\left[
        \begin{matrix}#1\\#2
        \end{matrix}\right]}

\title{An Explicit Approach to Elementary Number Theory}
\author{<PERSON>}
\begin{document}
\maketitle
\tableofcontents

\chapter{Introduction}
\section{Who is Teaching this Course?}
I am {\em <PERSON>}.  Come see me during my office hours, which
are Wednesdays and Fridays, 2:00--3:00.

\hd{Quick Bio:} I received a Ph.D. from Berkeley just over a year ago,
where I worked with <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.
After graduating, I visited math institutes in Europe, Australia, and
Asia and was a postdoctoral fellow here at Harvard.  Now I am a
<PERSON> Peirce Assistant Professor.  Lucky for you, my research
specialty is number theory, with a focus on computing with ``elliptic
curves and modular forms''.

\section{Evaluation}
\begin{itemize}
  \item In-class midterm on October 17  (20\% of grade)
  \item Homework every Wednesday (40\% of grade)
  \item Take-home final (40\% of grade)
\end{itemize}


\section{What is this Course About?}
See the lecture plan.  The main ideas include:

\subsection{Factorization}
Do you remember writing whole numbers as products of primes?
For example,
$$12=2\times 2\times 3.$$
Can this sort of thing always be done?  Is it really hard or really
easy?  For example, is factoring social security numbers ``trivial''
or hopeless?  In fact, it's trivial; even my wristwatch can do it!!
(Mine might be the only wristwatch in the world that can factor social
security numbers, but that's another story.)  What about bigger
numbers?

These questions are important to your everyday life. If somebody out
there secretly knows how to factor $200$-digit numbers quickly, then
that person could easily read you credit card number and expiration date when
you send it to {\tt amazon.com}.

\subsection{Congruences and Public-key Cryptography}
Two numbers~$a$ and~$b$ are {\em congruent modulo another number~$n$} if
$a=b+nk$ for some integer~$k$.  That $a$ and $b$ are congruent
just means you can ``get from~$a$  to~$b$ on the number line''
by adding or subtracting lots of copies of~$n$.
For example, $14\con 2\pmod{12}$ since
$14=2+12\cdot 1$.
$$\Z/n\Z = \{\text{ equivalence classes of numbers modulo~$n$ }\}.$$

Your web browser's ``secret code language'' uses arithmetic in
$\Z/pq\Z$ to send messages in broad weblight to
  {\tt amazon.com}.  How can this possibly be safe!?
You will find out exactly what is going on.

\subsection{Computers}

Computers make the study of properties of whole numbers vastly more
interesting.  A computer is to a number theorist, like a telescope is
to an astronomer.   It would be a shame to teach an astronomy class
without touching a telescope; likewise, it would be shame to teach
this class without telling you how to look at the integers ``through
the lens of a computer''.


\subsection{Sums of Two Squares}
I will tell you how to decide whether or not your order number is a
sum of two squares.  For example, an odd prime number is a sum of two
squares if and only if when divided by~$4$ it leaves a remainder
of~$1$.  For example, $7$ is not a sum of two squares, but $29$ is.

\subsection{Elliptic Curves}
My experience is that elliptic curves are extraordinarily fun to
study.  Every such curve is like a whole galaxy in itself, just like
the rational numbers are.
%Elliptic curves first appeared when people hundreds of years ago
%tried to compute the arc length of ellipses using calculus.   
An
elliptic curve over~$\Q$ is a curve that can be put in the form
$$y^2 = x^3 + ax+b,$$
where the cubic has distinct roots and $a, b\in \Q$.
The amazing thing is that the set of pairs
$$E(\Q) = \{ (x,y)\in \Q\cross \Q : y^2 = x^3 + ax +b \} \union \{\infty\}$$
has a natural structure of ``group''.  In particular,
this means that given two points on $E$, there is a way
to ``add'' the two solutions together to get
another solution.

Many exciting problems in number theory can be translated
into questions about elliptic curves.
For example, Fermat's Last Theorem, which asserts that
$x^n + y^n = z^n$ has no positive integer solutions when $n>2$
was proved using elliptic curves.  Giving a method to decide
which numbers are the area of a right triangle with rational
side lengths has {\em almost}, but not quite, been solved using
elliptic curves.

  {\bf The} central question about elliptic curves is {\em The Birch and
    Swinnerton-Dyer Conjecture} which gives a simple conjectural criterion
to decide whether or not $E(\Q)$ is infinite (and more).  Proving the
BSD conjecture is one of the Clay Math Institute's million dollar
prize problems.  I'll tell you what this conjecture is.




\chapter{Prime Factorization}



\section{Prime Numbers}
We call positive whole numbers the {\em natural numbers} and denote
them by $\N$. Thus
$$\N = \{1,2,3,4,\ldots\}.$$
We call all the whole numbers, both positive and negative, the
  {\em integers}, and write
$$\Z = \{\ldots, -2, -1, 0, 1,2,\ldots\}.$$
They are denoted by $\Z$ because the German word
for the integers is ``{\bf Z}ahlen'' (and $19$th century
German number theorists rocked).


\begin{definition}
  If $a, b\in \Z$ then ``$a$ divides $b$''
  if $ac=b$ for some $c\in\Z$.
\end{definition}
To save time, we write
$$a \mid b.$$
For example, $2 \mid 6$ and $389\mid 97734562907$.
Also, everything divides $0$.

\begin{definition}
  A natural number $p>1$ is a {\em prime} if~$1$ and~$p$
  are the only divisors of~$p$ in $\N$.  I.e., if $a\mid p$ implies
  $a=1$ or $a=p$.
\end{definition}
\hd{Primes:}
$$2,3,5,7,11,\ldots,389,\ldots,2003,\ldots$$

\hd{Composites:}
$$4,6,8,9,10,12,\ldots,666=2\cdot 3^2\cdot 37, \ldots,
  2001=3\cdot 23\cdot 29,\ldots$$

Primes are ``primal''---every natural number is
built out of prime numbers.
\begin{theorem}[The Fundamental Theorem of Arithmetic]\label{thm:fundamental}
  Every positive integer can be written as a product of primes,
  and this expression is unique (up to order).
\end{theorem}

\hd{Warning:} This theorem is harder to prove than I first thought it
should be.  Why?

\hd{First,}we are lucky that there are any primes at all: if
the natural numbers are replaced by the positive rational numbers then
there are no primes; e.g., $2 = \frac{1}{2}\cdot 4$, so
$\frac{1}{2}\mid 2$.

\hd{Second,}we are fortunate to have {\em unique}
factorization in~$\Z$. In other ``rings'', such as $\Z[\sqrt{-5}]
  = \{a + b\sqrt{-5} : a, b\in\Z\}$, unique factorization can fail.
In $\Z[\sqrt{-5}]$, the number~$6$ factors in two different ways:
$$2\cdot 3 = 6 = (1+\sqrt{-5})\cdot (1-\sqrt{-5}).$$

\begin{advanced}
  If you are worried about whether or not~$2$ and~$3$ are ``prime'', read this:
  If $2 = (a+b\sqrt{-5})\cdot (c+d\sqrt{-5})$ with neither factor equal
  to $\pm 1$, then taking norms implies that
  $$4 = (a^2 + 5b^2)\cdot (c^2 + 5d^2),$$
  with neither
  factor~$1$.  Theorem~\ref{thm:fundamental} implies that
  $2=a^2 + 5b^2$, which is impossible.
  Thus~$2$ is ``prime'' in the (nonstandard!) sense that it
  has no divisors besides $\pm 1$ and $\pm 2$.
  A similar argument shows that~$3$
  has no divisors besides $\pm 1$ and $\pm 3$.
  On the other hand, as you will learn later,~$2$ should not be
  considered prime, because the {\em ideal} generated by~$2$
  in $\Z[\sqrt{-5}]$ is not prime.  We have
  $(1+\sqrt{-5})\cdot (1-\sqrt{-5}) = 6\in (2)$, but
  neither $1+\sqrt{-5}$ nor $1-\sqrt{-5}$ is in $(2)$.
  We also note that $(1+\sqrt{-5})$ does not factor.
  If $(1+\sqrt{-5}) = (a+b\sqrt{-5})\cdot (c+d\sqrt{-5})$,
  then, upon taking norms,
  $$2\cdot 3 = (a^2 + 5b^2)\cdot (c^2 + 5d^2),$$
  which is impossible.

\end{advanced}



\section{Greatest Common Divisors}
Let~$a$ and~$b$ be two integers.  The {\em greatest common divisor}
of~$a$ and~$b$ is the biggest number that divides both of them.
We denote it by ``$\gcd(a,b)$''.  Thus,
\begin{definition}
  $$\gcd(a,b)=\max\{d : d \mid a\text{ and } d\mid b\}.$$
\end{definition}
\hd{Warning:} We define $\gcd(0,0)=0$, instead of ``infinity''.\vspace{1ex}

\noindent{}Here are a few gcd's:
$$\gcd(1,2)=1,\quad \gcd(0,a)=\gcd(a,0)=a, \quad\gcd(3,27)=3,
  \quad\gcd(2261,1275)=?$$


\hd{Warning:} In Davenport's book, he denotes our~$\gcd$ by HCF and calls it
the ``highest common factor''.    I will use the notation $\gcd$ because
it is much more common.

\subsection{Euclid's Algorithm for Computing GCDs}
Can we easily compute something like $\gcd(2261,1275)$?  Yep.
Watch closely:
$$2261 = 1\cdot 1275 + 986.$$
Notice that if a number~$d$ divides both $2261$ and $1275$, then it
automatically divides $986$, and of course~$d$ divides $1275$.  Also, if a number
divides both $1275$ and $986$, then it has got to divide $2261$ as well!
So we have made progress:
$$\gcd(2261,1275) = \gcd(1275,986)$$
Let's try again:
$$1275 = 1\cdot 986 + 289,$$
so $\gcd(1275,986)=\gcd(986,289)$.
Just keep at it:
\begin{align*}
  986 & =3\cdot 289 + 119 \\
  289 & =2\cdot 119 + 51  \\
  119 & =2\cdot 51 + 17.
\end{align*}
Thus $\gcd(2261,1275)=\cdots=\gcd(51,17)$, which is $17$
because $17\mid 51$, so
$$\gcd(2261,1275)=17.$$
Cool.  Aside from tedious arithmetic, that was
quick and very mechanical.

\hd{The Algorithm:}
That was an illustration of {\bf Euclid's algorithm}.
You just ``Divide and switch.''

More formally, fix $a, b\in\N$ with $a>b$.
Using ``divide with quotient and remainder'',
write $a=bq+r$, with $0\leq r<b$.
Then, just as above,
$$\gcd(a,b) = \gcd(b,r).$$
Let $a_1=b$, $b_1=r$, and repeat until $r=0$.
Soon enough we have computed $\gcd(a,b)$.

Here's are two more examples:
\begin{example}
  Set $a=15$ and $b=6$.
  \begin{eqnarray*}
    15 &=& 6\cdot 2 + 3 \qquad\gcd(15,6)=\gcd(6,3)\\
    6 &=& 3\cdot 2 + 0 \qquad\gcd(6,3) =\gcd(3,0)=3
  \end{eqnarray*}
\end{example}
\noindent{}We can just as easily do an example that is ``$10$ times as hard'':
\begin{example}
  Set $a=150$ and $b=60$.
  \begin{eqnarray*}
    150 &=& 60\cdot 2 + 30 \qquad\gcd(150,60)=\gcd(60,30)\\
    60 &=& 30\cdot 2 + 0 \qquad\,\,\,\gcd(60,30) =\gcd(30,0)=30
  \end{eqnarray*}
\end{example}

With Euclid's algorithm in hand, we can prove that if a prime divides the
product of two numbers, then it has got to divide one of them.  This
result is the {\em key} to proving that prime factorization
is unique.

\begin{theorem}[Euclid]\label{thm:euclid}
  Let~$p$ be a prime and $a, b\in \N$.
  If $p\mid ab$ then $p\mid a$ or $p\mid b$.
\end{theorem}
\begin{proof}
  If $p\mid a$ we are done.  If $p\nmid a$ then $\gcd(p,a)=1$, since
  only~$1$ and~$p$ divide~$p$.  Stepping through the Euclidean algorithm
  from above, we see that $\gcd(pb,ab) = b.$  At each step, we simply
  multiply the equation through by~$b$.  Since $p\mid pb$ and,
  by hypothesis, $p\mid ab$, it follows that $p\mid \gcd(pb,ab) = b$.
\end{proof}






\section{Numbers Do Factor}
Let $n=1275$, and recall from above that $17\mid 1275$, so~$n$
is definitely composite, $n=17 \cdot 75$.  Next, $75$ is $5\cdot 15=5\cdot 5\cdot 3$.
So, finally, $1275 = 3\cdot 5\cdot 5\cdot 17$.

Now suppose~$n$ is any positive number.
Then, just as above,~$n$ can be written as a product of primes:
\begin{itemize}
  \item If $n$ is prime, we are done.
  \item If $n$ is composite, then $n=ab$ with $a,b<n$. By induction,
        $a,b$ are products of primes, so~$n$ is also a product of primes.
\end{itemize}

What if we had done something differently when breaking $1275$ apart
as a product of primes?  Could the primes that show up be different?
Why not just try?  We have $1275 = 5\cdot 255$.  Now $255=5\cdot 51$ and
$51=17\cdot 3$, so everything turned out the same.  Will it always?

Incidently, there's an open problem nearby:

\hd{Unsolved Question:} Is there an algorithm which can factor any
given integer~$n$ so quickly that its ``running time'' is bounded by
a polynomial function of the number of decimal digits of~$n$.
\vspace{1ex}

I think most people would guess ``no'', but nobody has yet proved that it
can't be done (and told everyone...).   If there were such an
algorithm, then the cryptosystem that I use to send my girlfriend
private emails would probably be easily broken.

\subsection{A \$10,000 Challenge}
If you factor the following 174-digit number, affectionality known as
``RSA-576'', then the RSA company will give you TEN THOUSAND DOLLARS!!!
\begin{quote}
  18819881292060796383869723946165043980716356337941738270076335\\
  64229888597152346654853190606065047430453173880113033967161996\\
  92321205734031879550656996221305168759307650257059
\end{quote}
This number is called RSA-576, since it has 576 {\em binary} digits.
See
\begin{center}
  \sf http://www.rsasecurity.com/rsalabs/challenges/factoring/index.html
\end{center}
for more details.

\section{The Fundamental Theorem of Arithmetic}
We can now prove Theorem~\ref{thm:fundamental}.  The idea is simple.
Suppose we have two factorization. Use Theorem~\ref{thm:euclid} to
cancel primes from each, one prime at a time.  At the end of the game,
we discover that the factorizations have to consist of exactly the
same primes.  The technical details, with all the $p$'s and $q$'s are
given below:

\begin{proof}
  We have
  $$n = p_1\cdot p_2 \cdots p_d,$$
  with each $p_i$ prime.
  Suppose that
  $$n = q_1\cdot q_2 \cdots q_m$$
  is another expression of~$n$ as a product of primes.
  Since
  $$p_1 \mid n = q_1\cdot (q_2 \cdots q_m),$$
  Euclid's theorem implies that $p_1 = q_1$ or
  $p_1 \mid q_2\cdots q_m$.  By induction, we see that $p_1 = q_i$ for some~$i$.

  Now cancel $p_1$ and $q_i$, and repeat the above argument.  Eventually,
  we find that, up to order, the two factorizations are the same.
\end{proof}



\chapter{Introduction to Computing and PARI}



\section{Introduction}
\begin{quote}
  ``The object of numerical computation is theoretical advance.''
  \hfill -- {\em Bryan Birch describing A.\thinspace{}O.\thinspace{}L. Atkin.}
\end{quote}

Much progress in number theory has been driven by attempts to prove
conjectures.  It's reasonably easy to play around with integers, see
a pattern, and make a conjecture.  Frequently proving the conjecture is
  {\em extremely difficult}.  In this direction, computers help us to
\begin{itemize}
  \item find more conjectures
  \item disprove conjectures
  \item increase our confidence in a conjecture
\end{itemize}
They also frequently help to solve a specific problem.
For example, the following problem would be hopelessly tedious
by hand.  Here's an example of such a problem:
\begin{quote}
  Find all integer $n<100$ that are the area of a right triangle with
  integer side lengths.\footnote{We will discuss the ``The Congruent
    Number Problem'' in more depth later in this course.}
\end{quote}
This problem can be solved by a combination of very deep theorems, a
few big computer computations, and a little luck.

\section{Some Assertions About Primes}
A computer can quickly ``convince'' you that many assertions
about prime numbers are true.  Here are three.

\begin{itemize}
  \item {\em The polynomial $x^2+1$ takes on infinitely many prime values.}\\
        Let
        $$f(n) = \{x : x < n : x \text{ and }x^2+1\text{ is prime }\}.$$
        With a computer, we quickly find that
        $$f(10^2) = 19, \quad f(10^3)=112, \quad f(10^4)=841,
          \quad f(10^5) = 6656.$$
        Surely $f(n)$ is unbounded!
        The PARI code to compute $f(n)$ is very simple:
        \begin{verbatim}
? f(n) = s=0; for(x=1,n,if(isprime(x^2+1),s++)); s
? f(100)
%1 = 19
? f(1000)
%2 = 112
? f(10000)
%3 = 841
? f(100000)
%4 = 6656
\end{verbatim}

  \item {\em Every even integer $n>2$ is a sum of two primes.}\\
        With a computer we find that this seems true
        \begin{center}
          \begin{tabular}{c|cc}
            $n$ & $p$ & $q$ \\\hline
            4   & 2   & 2   \\
            6   & 3   & 3   \\
            8   & 3   & 5   \\
            10  & 3   & 7   \\
            12  & 5   & 7   \\\hline
          \end{tabular}
        \end{center}
        ... and much further.  In practice, it's easy to write
        an even number as a sum of two primes.  Why should there
        be any weird even numbers out there for which this can't be done?
        PARI code to find $p$ and $q$:
        \begin{verbatim}
? gb(n) = forprime(p=2,n,if(isprime(n-p),return([p,n-p])));
? gb(4)
%7 = [2, 2]
? gb(6)
%8 = [3, 3]
? gb(100)
%9 = [3, 97]
? gb(1000)
%10 = [3, 997]
? gb(570)          \\ takes no time at all!
%11 = [7, 563]
\end{verbatim}

  \item {\em There are infinitely many primes~$p$ such that $p+2$
        is also prime.}\\
        Let $t(n)=\#\{p : p \le n\text{ and } p+2 \text{ is prime }\}$.
        Using a computer we quickly find that
        $$t(10^2)=8, \quad t(10^3)=35,\quad t(10^4)=205, \quad t(10^5)=1024.$$
        The PARI code to compute $t(n)$ is very simple:
        \begin{verbatim}
? t(n) = s=0; forprime(p=2,n,if(isprime(p+2),s++)); s
? t(10^2)
%12 = 8
? t(10^3)
%13 = 35
? t(10^4)
%14 = 205
? t(10^5)
%15 = 1224
\end{verbatim}
        Surely $t(n)$ keeps getting bigger!!
\end{itemize}

As it turns out, these three assertions are {\em all} OLD famous
extremely difficult \underline{unsolved} problems!  Anyone who proves
one of them will be very famous.

%Assertion 1 is\edit{problem one in Guy? -- look up}

Assertion 2 is called ``The Goldbach Conjecture'';
Goldbach reformulated it in a letter to Euler in 1742.  It's
featured in the following recent novel:
\begin{center}
  \includegraphics{petros.eps}
\end{center}

The publisher of that novel offers a MILLION dollar prize
for the solution to the Goldbach conjecture:
\begin{verbatim}
  http://www.faber.co.uk/faber/million_dollar.asp?PGE=&ORD=faber&TAG=&CID=
\end{verbatim}
The Goldbach conjecture is true for all $n<4\cdot 10^{14}$, see
\begin{verbatim}
  http://www.informatik.uni-giessen.de/staff/richstein/ca/Goldbach.html
\end{verbatim}


Assertion 3 is the ``Twin Primes Conjecture''. According to
\begin{verbatim}
  http://perso.wanadoo.fr/yves.gallot/primes/chrrcds.html#twin
\end{verbatim}
on May 17, 2001, David Underbakke and Phil Carmody discovered
a 32220 digits twin primes record with a set of different
programs: $318032361\cdot 2^{107001}\pm 1$.  This is the current
``world record''.

With a computer, even if you can't solve one of these ``Grand
Challenge'' problems, at least you can perhaps work very hard and
prove it for more cases than anybody before you, especially since
computers keep getting more powerful.  This can be very fun,
especially as you search for a more efficient algorithm to extend the
computations.


\section{Some Tools for Computing}
\hd{Calculator:} A TI-89 can deal with integers with 1000s of
digits, factor, and do most basic number theory.  I am not aware
if anyone has programmed basic ''elliptic curve'' computations into
this calculator, but it could be done.

\hd{Mathematica and Maple:} Both are commercial, but they are very
powerful, can draw pretty pictures, and there are elliptic curve
packages available for each ({\tt apecs} for Maple, and something
by Silverman for Mathematica).

\hd{PARI:} Free, open source, excellent for our course, simple,
runs on Macs, MS Windows, Linux, etc.

\hd{MAGMA:} Huge, non-free but nonprofit,
what I usually use for my research.  I can legally give you a Linux
executable if you are registered for 124.

\hd{My Wristwatch:} Perhaps the only wristwatch in the world
that can factor your social security number?  {\tt :-)}

\section{Getting Started with PARI}
\subsection{Documentation}
The documentation for PARI is available at
\begin{verbatim}
        http://modular.fas.harvard.edu/docs/
\end{verbatim}
Some PARI documentation:
\begin{enumerate}
  \item {\bf Installation Guide:} Help for setting up PARI on a UNIX computer.
  \item {\bf Tutorial:} 42-page tutorial that starts with {\tt 2 + 2}.
  \item {\bf User's Guide:} 226-page reference manual; describes every function
  \item {\bf Reference Card:} hard to print, so I printed it for you (handout)
\end{enumerate}

\subsection{A Short Tour}
\begin{verbatim}
$ gp
Appele avec : /usr/local/bin/gp -s 10000000 -p 500000 -emacs

                  GP/PARI CALCULATOR Version 2.1.1 (released)
                i686 running linux (ix86 kernel) 32-bit version
                (readline v4.2 enabled, extended help available)

                       Copyright (C) 2000 The PARI Group

PARI/GP is free software, covered by the GNU General Public License, and 
comes WITHOUT ANY WARRANTY WHATSOEVER.

Type ? for help, \q to quit.
Type ?12 for how to get moral (and possibly technical) support.

   realprecision = 28 significant digits
   seriesprecision = 16 significant terms
   format = g0.28

parisize = 10000000, primelimit = 500000
? \\ this is a comment
? x = 571438063;
? print(x)
571438063
? x^2+17
%2 = 326541459845191986
? factor(x)
%3 = 
[7 1]

[81634009 1]
? gcd(x,56)
%5 = 7
? x^20
%6 = 13784255037665854930357784067541250773222915495828020913935
8450113971943932613097560462268162512901194466231159983662241797
60816483100648674388195744425584150472890085928660801

\end{verbatim}


\subsection{Help in PARI}
\begin{verbatim}
? ?
Help topics:
  0: list of user-defined identifiers (variable, alias, function)
  1: Standard monadic or dyadic OPERATORS
  2: CONVERSIONS and similar elementary functions
  3: TRANSCENDENTAL functions
  4: NUMBER THEORETICAL functions
  5: Functions related to ELLIPTIC CURVES
  6: Functions related to general NUMBER FIELDS
  7: POLYNOMIALS and power series
  8: Vectors, matrices, LINEAR ALGEBRA and sets
  9: SUMS, products, integrals and similar functions
 10: GRAPHIC functions
 11: PROGRAMMING under GP
 12: The PARI community

Further help (list of relevant functions): ?n (1<=n<=11).
Also:
  ? functionname (short on-line help)
  ?\             (keyboard shortcuts)
  ?.             (member functions)
Extended help looks available:
  ??             (opens the full user's manual in a dvi previewer)
  ??  tutorial   (same with the GP tutorial)
  ??  refcard    (same with the GP reference card)

  ??  keyword    (long help text about "keyword" from the user's manual)
  ??? keyword    (a propos: list of related functions).
? ?4

addprimes     bestappr      bezout        bezoutres     bigomega
binomial      chinese       content       contfrac      contfracpnqn
core          coredisc      dirdiv        direuler      dirmul
divisors      eulerphi      factor        factorback    factorcantor
factorff      factorial     factorint     factormod     ffinit
fibonacci     gcd           hilbert       isfundamental isprime
ispseudoprime issquare      issquarefree  kronecker     lcm
moebius       nextprime     numdiv        omega         precprime
prime         primes        qfbclassno    qfbcompraw    qfbhclassno
qfbnucomp     qfbnupow      qfbpowraw     qfbprimeform  qfbred
quadclassunit quaddisc      quadgen       quadhilbert   quadpoly
quadray       quadregulator quadunit      removeprimes  sigma
sqrtint       znlog         znorder       znprimroot    znstar

? ?gcd
gcd(x,y,{flag=0}): greatest common divisor of x and y. flag is optional, and 
can be 0: default, 1: use the modular gcd algorithm (x and y must be 
polynomials), 2 use the subresultant algorithm (x and y must be polynomials).

? ??gcd
\\ if set up correctly, brings up the typeset section from the manual on gcd
\end{verbatim}

We will discuss writing more complicated PARI programs on October 10.




\chapter{The Sequence of Prime Numbers}




This lecture is about the following three questions:
\begin{enumerate}
  \item Are there infinitely many primes? (yes)
  \item Are there infinitely many primes of the form $ax+b$?  (yes, if $\gcd(a,b)=1$)
  \item How many primes are there? (asymptotically $x/\log(x)$ primes less than $x$)
\end{enumerate}

\section{There are infinitely many primes}
\begin{theorem}[Euclid]
  There are infinitely many primes.
\end{theorem}
Note that this is not obvious.  There are completely reasonable rings
where it is false, such as
$$
  R = \left\{\frac{a}{b} : a, b\in\Z \text{ and }\gcd(b,30)=1\right\}
$$
There are exactly three primes in $R$, and that's it.
\begin{proof}[Proof of theorem]
  Suppose not.  Let $p_1=2, p_2=3, \ldots, p_n$ be all of the primes.
  Let
  $$
    N=2\times 3\times 5 \times \cdots \times p_n + 1
  $$
  Then $N\neq 1$ so, as proved in Lecture 2,
  $$N = q_1\times q_2 \times \cdots \times q_m$$
  with each~$q_i$ prime and $m\geq 1$.
  If $q_1\in\{2,3,5,\ldots,p_n\}$, then
  $N = q_1 a + 1$, so $q_1\nmid N$, a contradiction.
  Thus our assumption that
  $\{2,3,5,\ldots,p_n\}$
  are all of the primes is false, which proves
  that there must be infinitely many primes.
\end{proof}

If we were to try a similar proof in $R$, we run into trouble.
We would let $N=2\cdot 3\cdot 5 + 1 = 31$, which is a unit,
hence not a nontrivial product of primes.

\vspace{1ex}
\hd{Joke (Lenstra).}  ``There are infinitely many composite
numbers.  {\em Proof:} Multiply together the first~$n$ primes and don't add~$1$.''
\vspace{1ex}\\

According to
\begin{verbatim}
              http://www.utm.edu/research/primes/largest.html
\end{verbatim}
the largest known prime is
$$p = 2^{6972593}-1,$$
which is a number having over two million\footnote{It has exactly $2098960$ decimal
  digits.} decimal digits.
Euclid's theorem implies that there definitely {\em is} a bigger prime number.
However, nobody has yet found it {\em and proved that they are right}.
In fact, determining whether or not a number is prime is an extremely
interesting problem.
We will discuss this problem more later.




\section{Primes of the form $ax+b$}
Next we turn to primes of the form $ax+b$.
We assume that $\gcd(a,b)=1$, because otherwise there is no
hope that $ax+b$ is prime {\em infinitely} often.
For example, $3x+6$ is only prime for one value of~$x$.

\begin{proposition}
  There are infinitely many primes of the form $4x-1$.
\end{proposition}
Why might this be true?   Let's list numbers of the form $4x-1$ and underline
the ones that are prime:
$$\ul{3}, \ul{7}, \ul{11}, 15, \ul{19}, \ul{23}, 27, \ul{31}, 35, 39,
  \ul{43}, \ul{47}, \ldots$$
It certainly looks plausible that underlined numbers will continue
to appear.  The following PARI program can be used to further convince
you:
\begin{verbatim}
   f(n, s=0) = for(x=1, n, if(isprime(4*x-1), s++); s
\end{verbatim}

\begin{proof}
  The proof is similar to the proof of Euclid's Theorem, but, for variety,
  I will explain it in a slightly different way.

  Suppose $p_1, p_2,\ldots, p_n$ are primes of the form $4x-1$.  Consider
  the number
  $$
    N = 4p_1\times p_2 \times \cdots \times p_n - 1.$$
  Then $p_i \nmid N$ for any~$i$.  Moreover, not every prime $p\mid N$ is
  of the form $4x+1$; if they all were, then $N$ would also be of the
  form $4x+1$, which it is not.
  Thus there is a $p\mid N$ that is of the form $4x-1$.  Since $p\not= p_i$
  for any~$i$, we have found another prime of the form $4x-1$.  We can repeat
  this process indefinitely, so the set of primes of the form $4x-1$
  is infinite.
\end{proof}

\begin{example}
  Set $p_1=3$, $p_2=7$.   Then
  $$
    N = 4\times 3 \times 7 - 1 = \ul{83}
  $$
  is a prime of the form $4x-1$.
  Next
  $$
    N = 4\times 3 \times 7\times 83 - 1 = \ul{6971},
  $$
  which is a again a prime of the form $4x-1$.
  Again:
  $$
    N = 4\times 3 \times 7\times 83\times 6971 - 1 = 48601811 = 61 \times \ul{796751}.
  $$
  This time $61$ is a prime, but it is of the form $4x+1 = 4\times 15+1$.
  However, $796751$ is prime and $(796751-(-1))/4 = 199188$.
  We are unstoppable
  $$
    N = 4\times 3 \times 7\times 83\times 6971 \times 796751 - 1 = \ul{5591}\times 6926049421.
  $$
  This time the small prime, $5591$, is of the form $4x-1$ and the large
  one is of the form $4x+1$.
  Etc!
\end{example}

\begin{theorem}[Dirichlet]
  Let $a$ and $b$ be integers with $\gcd(a,b)=1$.
  Then there are infinitely many primes of the form
  $ax+b$.
\end{theorem}
The proof is out of the scope of this course.  You will probably see a proof
if you take Math 129 from Cornut next semester.



\section{How many primes are there?}
There are infinitely many primes.

Can we say something more precise?

Let's consider a similar question:\\
\begin{question}
  How many even integers are there?
\end{question}
\hd{Answer:} {\em Half} of all integers.

\begin{question}
  How many integers are there of the form $4x-1$?
\end{question}
\hd{Answer:} {\em One fourth} of all integers.

\begin{question}
  How many perfect squares are there?
\end{question}
\hd{Answer:} Zero percent of all numbers, in the sense that
the limit of the proportion of perfect squares to all numbers
converges to~$0$.
More precisely,
$$\lim_{x\ra \infty}
  \# \{n : n \leq x \text{ and $n$ is a perfect square }\} / x = 0,$$
since the numerator is roughly $\sqrt{x}$ and $\sqrt{x}/x \ra 0$.

A better question is:
\begin{question}
  How many numbers $\leq x$ are perfect squares, as a function of~$x$?
\end{question}
\hd{Answer:} Asymptotically, the answer is $\sqrt{x}$.

So a good question is:
\begin{question}
  How many numbers $\leq x$ are prime?
\end{question}
Let
$$
  \pi(x) = \#\{\text{ primes } p\leq x\}.
$$
For example,
$$\pi(6) =\#\{2,3,5\} = 3.$$

We can compute a few more values of $\pi(x)$ using PARI:
\begin{verbatim}
? pi(x, c=0) = forprime(p=2,x,c++); c; 
? for(n=1,7,print(n*100,"\t",pi(n*100)))
100	25
200	46
300	62
400	78
500	95
600	109
700	125
\end{verbatim}
Now draw a graph on the blackboard.  It will look like a straight
line...

Gauss spent some of his free time counting primes.  By the end
of his life, he had computed $\pi(x)$ for $x$ up to $3$ million.
$$\pi(3000000)=216816.$$
(I don't know if Gauss got the right answer.)
Gauss conjectured the following:

\begin{theorem}[Hadamard, Vall\'ee Poussin, 1896]
  $\pi(x)$ is asymptotic to $x/\log(x)$, in the sense that
  $$\lim_{x\ra \infty}  \frac{\pi(x)}{ x/\log(x)} = 1.$$
\end{theorem}
I will not prove this theorem in this class.
The theorem implies that $x/(\log(x)-a)$ can be used
to approximate $\pi(x)$, for any~$a$.  In fact, $a=1$
is the best choice.
\begin{verbatim}
? pi(x, c=0) = forprime(p=2,x,c++); c; 
? for(n=1,10,print(n*1000,"\t",pi(n*1000),"\t",n*1000/(log(n*1000)-1)))
1000	168	169.2690290604408165186256278
2000	303	302.9888734545463878029800994
3000	430	428.1819317975237043747385740
4000	550	548.3922097278253264133400985
5000	669	665.1418784486502172369455815
6000	783	779.2698885854778626863677374
7000	900	891.3035657223339974352567759
8000	1007	1001.602962794770080754784281
9000	1117	1110.428422963188172310675011
10000	1229	1217.976301461550279200775705
\end{verbatim}
\begin{remark}
\end{remark}

\subsection{Counting Primes Today}
People all over the world are counting primes, probably even as we speak.
See, e.g.,
\begin{verbatim}
http://www.utm.edu/research/primes/howmany.shtml

http://numbers.computation.free.fr/Constants/Primes/Pix/pixproject.html
\end{verbatim}

A huge computation:
$$
  \pi(10^{22}) = 201467286689315906290
$$
(I don't know for sure if this is right...)

\subsection{The Riemann Hypothesis}
The function
$$
  \Li(x) = \int_{2}^{x} \frac{1}{\log(x)} dx.
$$
is also a good approximation to $\pi(x)$.

The famous {\bf Riemann Hypothesis} is equivalent to the
assertion that
$$
  \pi(x) = \Li(x) +O(\sqrt{x}\log(x)).
$$
(This is another \$1000000 prize problem.)

\begin{verbatim}
      pi(10^22)     = 201467286689315906290
      Li(10^22)     = 201467286691248261498.1505...    (using Maple)
      Log(x)/(x-1)  = 201381995844659893517.7648...    (pari)
\end{verbatim}




\chapter{Congruences}



\hd{The point of this lecture:}\\
Define the ring $\Z/n\Z$ of integers modulo~$n$.
Prove Fermat's little theorem, which asserts
that if $\gcd(x,n)=1$, then $x^{\vphi(n)} \con 1\pmod{n}$.

\section{Notation}
\begin{definition}[Congruence]
  Let $a,b\in\Z$ and $n\in\N$.  Then
  $$
    a\con b\pmod{n}
  $$
  if $n\mid a-b$.
\end{definition}
That is, there is $c\in\Z$ such that
$$
  nc = a-b.
$$
One way I think about it: $a$ is congruent to $b$ modulo $n$, if
we can get from $b$ to $a$ by adding multiples of~$n$.

Congruence modulo~$n$ is an {\em equivalence relation}.
Let
$$
  \Z/n\Z = \{\text{ the set of equivalence classes }\}
$$
The set $\Z/n\Z$ is a {\em ring}, the ``ring of integers
modulo $n$''.  It is the quotient of the ring $\Z$ by
the ideal generated by~$n$.

\begin{example}
  $$
    \Z/3\Z = \{ \{\ldots, -3, 0, 3, \ldots\},
    \{\ldots, -2, 1, 4, \ldots\},
    \{\ldots, -1, 2, 5, \ldots\}\}
    = \{[0], [1], [2]\}
  $$
  where we let $[a]$ denote the equivalence class of~$a$.
\end{example}


\section{Arithmetic Modulo~$N$}
Suppose $a,\,a',b\,b'\in\Z$ and
$$
  a\con a'\pmod{n}, \qquad b\con b'\pmod{n}.
$$
Then
\begin{align}
  a + b     & \con a' + b'\pmod{n}     \\
  a\times b & \con a'\times b'\pmod{n}
\end{align}
So it makes sense to define $+$ and $\times$ by
$[a]+[b]=[a+b]$ and $[a]\times[b]=[a\times b]$.

\subsection{Cancellation}
\begin{proposition}\label{prop:cancel}
  If $\gcd(c,n)=1$ and
  $$
    ac\con bc\pmod{n}
  $$
  then $a \con b\pmod{n}$.
\end{proposition}
\begin{proof}
  By definition
  $$
    n \mid ac - bc = (a-b)c.
  $$
  Since $\gcd(n,c)=1$, it follows that $n\mid a-b$, so
  $$
    a \con b\pmod{n},
  $$
  as claimed.
\end{proof}

\subsection{Rules for Divisibility}
\begin{proposition}
  A number $n\in\Z$ is divisible by $3$ if and only if
  the sum of the digits of~$n$ is divisible by~$3$.
\end{proposition}
\begin{proof}
  Write
  $$n=a+10b+100c+\cdots.$$
  Since $10\con 1\pmod{3}$,
  $$
    n = a + 10b + 100c+\cdots \con a + b + c+\cdots \pmod{3},
  $$
  from which the proposition follows.
\end{proof}

Similarly, you can find rules for divisibility by~$5$, $9$ and $11$.
What about divisibility by~$7$?


\section{Linear Congruences}
\begin{definition}[Complete Set of Residues]
  A {\em complete set of residues} modulo~$n$ is a subset $R\subset\Z$
  of size~$n$ whose reductions modulo~$n$ are distinct.  In other words,
  a complete set of residues is a choice of representive for each
  equivalence class in $\Z/n\Z$.
\end{definition}
Some examples:
$$
  R=\{0,1,2,\ldots,n-1\}
$$
is a complete set of residues modulo~$n$.
When $n=5$, a complete set of residues is
$$
  R = \{0,1,-1,2,-2\}.
$$

\begin{lemma}\label{lem:residues}
  If~$R$ is a complete set of residues modulo~$n$ and $a\in\Z$ with
  $\gcd(a,n)=1$, then $aR = \{ax : x \in R\}$
  is also a complete set of residues.
\end{lemma}
\begin{proof}
  If $ax\con ax'\pmod{n}$ with $x, x'\in R$, then
  Proposition~\ref{prop:cancel} implies that $x\con{}x'\pmod{n}$.
  Because $R$ is a complete set of residues, this implies
  that $x=x'$.  Thus the elements of
  $aR$ have distinct reductions modulo~$n$.
  It follows, since $\#aR=n$, that $aR$ is a
  complete set of residues modulo~$n$.
\end{proof}


\begin{definition}[Linear Congruence]
  A {\em linear congruence} is an equation of the form
  $$
    ax\con b\pmod{n}.
  $$
\end{definition}
\begin{proposition}
  If $\gcd(a,n)=1$, then the equation
  $$
    ax\con b\pmod{n}
  $$
  must have a solution.
\end{proposition}
\begin{proof}
  Let $R$ be a complete set of residues modulo~$n$ (for
  example, $R=\{0,1,\ldots,n-1\}$).
  Then by Lemma~\ref{lem:residues},
  $aR$ is also a complete set of residues.
  Thus there is an element $ax\in aR$ such
  that $ax\con b\pmod{n}$, which proves
  the proposition.
\end{proof}
The point in the proof is that left multiplication by $a$ defines
a map $\Z/n\Z\hookrightarrow \Z/n\Z$, which must be surjective
because $\Z/n\Z$ is finite.

\hd{Illustration:}\\
$$
  2x\con 3\pmod{7}
$$
Set $R = \{0,1,2,3,4,5,6\}$.
Then
$$
  2R = \{0,2,4,6,8\con 1, 10\con 3, 12\con 5\},
$$
so $2\cdot 5\con 3\pmod{7}$.

\hd{Warning:}\\
Note that the equation $ax\con b\pmod{n}$
might have a solution even if $\gcd(a,n)\neq 1$.  To construct
such examples, let~$a$ be any divisor of~$n$,~$x$
any number, and set $b=ax$.
For example, $2x\con 6\pmod{8}$ has a solution!


\section{Fermat's Little Theorem}

\begin{definition}[Order]
  Let $n\in\N$ and $x\in\Z$ with $\gcd(x,n)=1$.
  The {\em order} of $x$ modulo~$n$ is the smallest $m\in\N$
  such that
  $$
    x^m \con 1\pmod{n}.
  $$
\end{definition}
We must show that this definition makes sense.  To do so, we verify
that such an~$m$ exists.  Consider $x, x^2, x^3, \ldots$ modulo~$n$.
There are only finitely many residue classes modulo~$n$, so we must
eventually find two integers $i, j$ with $i<j$ such that
$$
  x^i\con x^j\pmod{n}.
$$
Since $\gcd(x,n)=1$, Proposition~\ref{prop:cancel} implies that
we can cancel~$x$'s and conclude that
$$
  x^{j-i}\con 1\pmod{n}.
$$


\begin{definition}[Euler Phi function]
  Let
  $$
    \vphi(n) = \#\{a \in \N : a \leq n \text{ and } \gcd(a,n)=1\}.
  $$
\end{definition}
For example,
\begin{align*}
  \vphi(1)  & = \#\{1\} = 1,        \\
  \vphi(5)  & = \#\{1,2,3,4\} = 4,  \\
  \vphi(12) & = \#\{1,5,7,11\} = 4. \\
\end{align*}
If~$p$ is any prime number then
$$
  \vphi(p) = \#\{1,2,\ldots,p-1\} = p-1.
$$

\begin{theorem}[Fermat's Little Theorem]\label{thm:fermatlittle}
  If $\gcd(x,n)=1$, then
  $$
    x^{\vphi(n)} \con 1\pmod{n}.
  $$
\end{theorem}
\begin{proof}
  Let
  $$
    P = \{ a : 1\leq a \leq n \text{ and } \gcd(a,n) = 1\}.
  $$
  In the same way that we proved Lemma~\ref{lem:residues},
  we see that the reductions modulo~$n$ of the elements of $xP$
  are exactly the same as the reductions of the elements of $P$.
  Thus
  $$
    \prod_{a\in P} (xa) = \prod_{a \in P} a \pmod{n},
  $$
  since the products are over exactly
  the same numbers modulo~$n$.
  Now cancel the $a$'s on both sides to get
  $$x^{\#P} \con 1\pmod{n},$$
  as claimed.
\end{proof}

\subsection{Group-theoretic Interpretation}
The set of invertible elements of $\Z/n\Z$ is a group
$$
  (\Z/n\Z)^*
  = \{ [a] \in \Z/n\Z : \gcd(a,n) = 1\}.
$$
This group has order~$\vphi(n)$.
Theorem~\ref{thm:fermatlittle} asserts that the
order of an element of $(\Z/n\Z)^\star$ divides the order $\vphi(n)$
of $(\Z/n\Z)^\star$.  This is a special case of the
more general theorem that if~$G$ is a finite group and $g\in G$,
then the order of~$g$ divide $\#G$.

\section{What happened?}
Take out a piece of paper and answer the following two questions:
\begin{enumerate}
  \item What is a central idea that you learned in this lecture?
  \item What part of this lecture did you find murky?
\end{enumerate}





\chapter{Congruences, Part II}


\hd{Key Ideas Today}
\begin{itemize}
  \item Wilson's theorem
  \item Chinese Remainder Theorem
  \item Multiplicativity of $\vphi$
\end{itemize}

\section{Wilson's Theorem}
\begin{theorem}[John Wilson's theorem, from the 1770s]
  An integer $p>1$ is prime if and only if
  $$(p-1)! \con -1 \pmod{p}.$$
\end{theorem}
\begin{example}
  \begin{verbatim}

? p=3
%1 = 3
? (p-1)! % 3
%2 = 2
? p=17
%3 = 17
? (p-1)!
%4 = 20922789888000
? (p-1)! % p
%5 = 16
\end{verbatim}
\end{example}

\begin{proof}
  We first {\bf assume that~$p$ is prime} and prove that
  $(p-1)! \con -1\pmod{p}$.  If $a\in\{1,2,\ldots,p-1\}$ then
  the equation
  $$
    ax\con 1\pmod{p}
  $$
  has a unique solution $a'\in\{1,2,\ldots,p-1\}$.
  If $a=a'$, then $a^2\con 1\pmod{p}$, so
  $p\mid a^2-1 = (a-1)(a+1)$, so
  $p\mid (a-1)$ or $p\mid (a+1)$, so $a\in\{1,-1\}$.
  We can thus pair off the elements of
  $\{2,3,\ldots,p-2\}$,
  each with its inverse.
  Thus
  $$
    2\cdot 3 \cdot \cdots \cdot (p-2) \con 1\pmod{p}.
  $$
  Multiplying both sides by $p-1$ proves that
  $(p-1)! \con -1\pmod{p}$.


  Next we {\bf assume that $(p-1)! \con -1\pmod{p}$} and
  prove that~$p$ must be prime.  Suppose not, so that~$p$
  is a composite number $\geq 4$.  Let $\ell$ be a prime divisor
  of~$p$.  Then $\ell<p$, so $\ell\mid (p-1)!$.  Also,
  $$
    \ell \mid p \mid ((p-1)! - 1).
  $$
  This is a contradiction, because a prime can't divide a number $a$ and
  also divide $a-1$, since it would then have to divide $a-(a-1)=1$.
\end{proof}

\begin{example}
  When $p=17$, we have
  $$
    2\cdot 3\cdot \cdots \cdot 15
    = (2\cdot 9)\cdot(3\cdot 6)\cdot(4\cdot 13)\cdot
    (5\cdot 7)\cdot(8\cdot 15)\cdot(10\cdot 12)\cdot(14\cdot 11)
    \con 1\pmod{17},$$
  where we have paired up the numbers $a, b$ for
  which $ab\con 1\pmod{17}$.
\end{example}

Let's test Wilson's Theorem in PARI:
\begin{verbatim}
? wilson(n) = Mod((n-1)!,n) == Mod(-1,n)   
? wilson(5)
%9 = 1
? wilson(10)
%10 = 0
? wilson(389)
%11 = 1
? wilson(2001)
%12 = 0
\end{verbatim}

\hd{Warning:} In practice, this is a horribly inefficient way to check
whether or not a number is prime.

\section{The Chinese Remainder Theorem}
Sun Tsu Suan-Ching (4th century AD):
\begin{quote}
  There are certain things whose number is unknown. Repeatedly divided
  by 3, the remainder is 2; by 5 the remainder is 3; and by 7 the
  remainder is 2. What will be the number?
\end{quote}
In modern notation, Sun is asking us to solve the following system of
equations:
\begin{align*}
  x & \con 2 \pmod{3} \\
  x & \con 3 \pmod{5} \\
  x & \con 2 \pmod{7}
\end{align*}
The Chinese Remainder Theorem asserts that a solution to Sun's
question exists, and the proof gives a method to find a solution.

\begin{theorem}[The Chinese Remainder Theorem]
  Let $a, b\in\Z$ and $n,m\in\N$ such that
  $\gcd(n,m)=1$.  Then there exists $x\in\Z$ such that
  \begin{align*}
    x & \con a\pmod{m} \\
    x & \con b\pmod{n}
  \end{align*}
\end{theorem}
\begin{proof}
  The equation
  $$
    tm \con b-a\pmod{n}
  $$
  has a solution $t$ since $\gcd(m,n)=1$.
  Set $x=a+tm$.  We next verify that $x$ is a solution to the two equations.
  Then
  $$
    x \con a + (b-a) \con b \pmod{n},
  $$
  and
  $$
    x = a+tm \con a\pmod{m}.
  $$
\end{proof}

Now we can solve Sun's problem:
\begin{align*}
  x & \con 2 \pmod{3}  \\
  x & \con 3 \pmod{5}  \\
  x & \con 2 \pmod{7}.
\end{align*}
First, we use the theorem to find a solution to the pair
of equations
\begin{align*}
  x & \con 2 \pmod{3}  \\
  x & \con 3 \pmod{5}.
\end{align*}
Set $a=2$, $b=3$, $m=3$, $n=5$.
Step 1 is to find a solution to $t\cdot 3 \con 3-2\pmod{5}$.
A solution is $t=2$.  Then $x=a+tm=2+2\cdot 3 = 8$.
Since any $x'$ with $x'\con x\pmod{15}$ is also a solution to
those two equations, we can solve all three equations by
finding a solution to the pair of equations
\begin{align*}
  x & \con 8 \pmod{15} \\
  x & \con 2 \pmod{7}.
\end{align*}
Again, we find a solution to $t\cdot 15 \con 2-8\pmod{7}$.
A solution is $t = 1$, so
$$x=a+tm=8+15=23.$$
Note that there are other solutions.  Any $x'\con x\pmod{3\cdot 5\cdot 7}$
is also a solution; e.g., $23+3\cdot 5\cdot 7 = 128$.

We can also solve Sun's problem in PARI:
\begin{verbatim}
? chinese(Mod(2,3),Mod(3,5))
%13 = Mod(8, 15)
? chinese(Mod(8,15),Mod(2,7))
%14 = Mod(23, 105)
\end{verbatim}


\section{Multiplicative Functions}
\begin{definition}
  A function $f:\N\ra \Z$ is
    {\em multiplicative} if, whenever $m, n\in\N$ and $\gcd(m,n)=1$, we have
  $$
    f(mn) = f(m)\cdot f(n).
  $$
\end{definition}
Recall that the {\em Euler $\vphi$-function} is
$$
  \vphi(n) = \#\{a : 1\leq a \leq n\text{ and }\gcd(a,n)=1\}.
$$
\begin{proposition}
  $\vphi$ is a multiplicative function.
\end{proposition}
\begin{proof}
  Suppose that $m,n\in\N$ and $\gcd(m,n)=1$.
  Consider the map
  \begin{align*}\{c : & \,1 \leq c \leq mn \text{ and }\gcd(c,mn)=1\}
              \xrightarrow{\quad f\quad }                                   \\
                    & \{a : 1 \leq a \leq m \text{ and }\gcd(a,m)=1\}\cross
              \{b : 1 \leq b \leq n \text{ and }\gcd(b,n)=1\}\}
  \end{align*}
  defined by
  $$
    f(c) = (c\text{ mod } m, \quad{}c \text{ mod }n).
  $$

  \hd{The map~$f$ is injective:}  If $f(c)=f(c')$, then
  $m\mid c-c'$ and $n\mid c-c'$, so, since $\gcd(n,m)=1$,
  $nm\mid c-c'$, so $c=c'$.

  \hd{The map~$f$ is surjective:} Given $a, b$ with $\gcd(a,m)=1$,
  $\gcd(b,n)=1$, the Chinese Remainder Theorem implies that there
  exists $c$ with $c\con a\pmod{m}$ and $c\con b\pmod{n}$.  We
  may assume that $1\leq c\leq nm$, ans since $\gcd(a,m)=1$ and
  $\gcd(b,n)=1$, we must have $\gcd(c,nm)=1$. Thus $f(c)=(a,b)$.

  Because $f$ is a bijection, the set on the left has the same
  size as the product set on the right.  Thus
  $$
    \vphi(mn) = \vphi(m)\cdot \vphi(n).
  $$
\end{proof}


\begin{example}
  The proposition makes it easier to compute $\vphi(n)$.
  For example,
  $$
    \vphi(12) = \vphi(2^2)\cdot \vphi(3) = 2\cdot 2 = 4.
  $$
  Also, for $n\geq 1$, we have
  $$
    \vphi(p^n) = p^n - \frac{p^n}{p},
  $$
  since $\vphi(p^n)$ is the number of numbers less than $p^n$
  minus the number of those that are divisible by $p$.
  Thus, e.g.,
  $$
    \vphi(389\cdot 11^2) = 388 \cdot (11^2 - 11) = 388\cdot 110 = 42680.
  $$
  The $\vphi$ function is also available in PARI:
  \begin{verbatim}
  ? eulerphi(389*11^2)
  %15 = 42680
\end{verbatim}
\end{example}

\begin{question}
  Is computing $\vphi(\text{1000 digit number})$ really easy or really hard?
\end{question}





\chapter{Congruences, Part III}



\hd{Key Ideas}
\begin{enumerate}
  \item How to solve $ax\con 1\pmod{n}$ efficiently.
  \item How to compute $a^m\pmod{n}$ efficiently.
  \item A probabilistic primality test.
\end{enumerate}

\section{How to Solve $ax\con 1\pmod{n}$}
Let $a, n\in\N$ with $\gcd(a,n)=1$.  Then
we know that $ax\con 1\pmod{n}$ has a solution.  How can we find~$x$?

\subsection{More About GCDs}
\begin{proposition}
  Suppose $a,b\in\Z$ and $\gcd(a,b)=d$. Then
  there exists $x,y\in\Z$ such that
  $$
    ax + by = d.
  $$
\end{proposition}
I won't give a formal proof of this proposition, though there
are many in the literature.  Instead I will show you how to
find~$x$ and~$y$ in practice, because that's what you will
need to do in order to solve equations like
$ax\con 1\pmod{n}$.
\begin{example}
  Let $a=5$ and $b=7$.
  The steps of the Euclidean $\gcd$ algorithm are:
  \begin{align*}
    \ul{7} & =1\cdot \ul{5} + \underline{2} & \text{so } \ul{2}     & = \ul{7} - \ul{5}\hfill                                    \\
    \ul{5} & =2\cdot \ul{2} + \ul{1}        & \text{so }     \ul{1} & = \ul{5} - 2\cdot \ul{2} = 3\cdot\ul{5}-2\cdot\ul{7}\hfill
  \end{align*}
  On the right, we have written each partial remainder as a
  linear combination of~$a$ and~$b$.  In the last step, we write
  $\gcd(a,b)$ as a linear combination of~$a$ and~$b$, as desired.
\end{example}

That example wasn't too complicated, next we try a much longer example.
\begin{example}
  Let $a=130$ and $b=61$.  We have
  \begin{align*}
    \ul{130} & = 2\cdot \ul{61} + \ul{8} & \text{so } \ul{8} & = \ul{130}-2\cdot\ul{61}         \\
    \ul{61}  & = 7\cdot \ul{8} + \ul{5}  & \text{so } \ul{5} & = -7\cdot\ul{130}+15\cdot\ul{61} \\
    \ul{8}   & = 1\cdot \ul{5} + \ul{3}  & \text{so } \ul{3} & = 8\cdot\ul{130}-17\cdot\ul{61}  \\
    \ul{5}   & = 1\cdot \ul{3} + \ul{2}  & \text{so } \ul{2} & =-15\cdot\ul{130}+32\cdot\ul{61} \\
    \ul{3}   & = 1\cdot \ul{2} + \ul{1}  & \text{so } \ul{1} & = 23\cdot\ul{130}-49\cdot\ul{61}
  \end{align*}
  Thus $x=130$ and $y=-49$.
\end{example}

\begin{remark}
  For our present purposes it will always be sufficient to find one
  solution to $ax+by=d$.  In fact, there are always
  infinitely many solutions.  If $x, y$ is a solution to
  $$
    ax + by = d,
  $$
  then for any $\alpha\in\Z$,
  $$
    a\left(x+\alpha\cdot\frac{b}{d}\right) + b\left(y - \alpha\cdot\frac{a}{d}\right) = d,
  $$
  is also a solution, and all
  solutions are of the above form for some~$\alpha$.
\end{remark}

It is also possible to compute~$x$ and~$y$ using PARI.
\begin{verbatim}
? ?bezout
bezout(x,y): gives a 3-dimensional row vector [u,v,d] such that 
             d=gcd(x,y) and u*x+v*y=d.
? bezout(130,61)
%1 = [23, -49, 1]
\end{verbatim}


\subsection{To solve $ax\con 1\pmod{n}$}
Suppose $\gcd(a,n)=1$.  To solve
$$
  ax\con 1\pmod{n},
$$
find $x$ and $y$ such that
$ax+ny = 1$.  Then
$$
  ax \con ax + ny \con 1 \pmod{n}.
$$

\begin{example}
  Solve $17x \con 1\pmod{61}$.
  First, we use the Euclidean algorithm to find $x, y$ such that
  $17x+61y=1$:
  \begin{align*}
    \ul{61} & =3\cdot\ul{17}+\ul{10} & \text{so }\ul{10} & =\ul{61} - 3\cdot\ul{17}       \\
    \ul{17} & =1\cdot\ul{10}+\ul{7}  & \text{so }\ul{7}  & =-\ul{61} + 4\cdot\ul{17}      \\
    \ul{10} & =1\cdot \ul{7}+\ul{3}  & \text{so }\ul{3}  & =2\cdot\ul{61} - 7\cdot\ul{17} \\
    \ul{3}  & =2\cdot\ul{3} +\ul{1}  & \text{so }\ul{1}  & =-5\cdot\ul{61}+18\cdot\ul{17}
  \end{align*}
  Thus $x=18$ is a solution to $17x \con 1\pmod{61}$.
\end{example}

\section{How to Compute $a^m\pmod{n}$ Efficiently}
As we will see on Friday, a quick method to compute
$a^m\pmod{n}$ is absolutely {\em essential} to
public-key cryptography.

\hd{Naive Algorithm:} Compute $a\cdot a \cdot \cdots \cdot a\pmod{n}$ by
repeatedly multiplying by $a$ and reducing modulo~$m$.  This is {\em BAD}
because it takes $m-1$ multiplications.

\hd{Clever Algorithm:} The following observation is the key idea which
makes the clever algorithm work.  Write $m=\sum_{i=1}^r \eps_i 2^i$ with
each $\eps_i\in\{0,1\}$, i.e., write~$m$ in base~$2$ (binary).
Then
$$
  a^m  = \prod_{\eps_i = 1} a^{2^i}\pmod{n}.
$$
It is straightforward to write a number~$m$ in binary,
as follows:
{\sf If $m$ is odd, then $\eps_0=1$, otherwise $\eps_0=0$.
Replace $m$ by $\text{floor}({\frac{m}{2}})$.  If the new $m$ is
odd then $\eps_1=1$, otherwise $\eps_1=0$.  Keep repeating
until $m=0$.}
\begin{example}\mbox{}\\
  \hd{Problem:} {\em Compute the last $2$ digits of $6^{91}$.} \\
  \hd{Solution:} We compute $6^{91}\pmod{100}$.
  \begin{center}
    \begin{tabular}{|cccc|}\hline
      \quad$i$\quad\quad & \quad $m$\quad\quad
                         & \quad $\eps_i$\quad \quad & \quad $6^{2^i}$ \text{mod} 100      \\\hline
      0                  & 91                        & 1                              & 6  \\\hline
      1                  & 45                        & 1                              & 36 \\\hline
      2                  & 22                        & 0                              & 96 \\\hline
      3                  & 11                        & 1                              & 16 \\\hline
      4                  & 5                         & 1                              & 56 \\\hline
      5                  & 2                         & 0                              & 36 \\\hline
      6                  & 1                         & 1                              & 96 \\\hline
    \end{tabular}
  \end{center}
  As a check, note that $91 = 1011011_2 = 2^6+2^4+2^3+2+2^0$.
  Finally, we have
  $$6^{91} = 6^{2^6}\cdot 6^{2^4} \cdot 6^{2^3}\cdot 6^2 \cdot 6
    \con 96 \cdot 56 \cdot 16 \cdot 36 \cdot 6
    \con 56\pmod{100}.$$

  \hd{Summary of above table:}  The first column,
  labeled $i$,
  is just to keep track of $i$.  The second column, labeled $m$,
  is got by dividing
  the entry above it by~$2$ and taking the integer part of the result.
  The third column, labeled $\eps_i$,
  simply records whether or not the second column is
  odd.  The forth column is computed by squaring, modulo 100, the entry above
  it.
\end{example}

Some examples in PARI to convince you that powering isn't
too difficult:
\begin{verbatim}
? Mod(17,389)^5000
%13 = Mod(330, 389)
? Mod(2903,49084098)^498494
%14 = Mod(13189243, 49084098)
\end{verbatim}
These both take no noticeable time.

\section{A Probabilistic Primality Test}
Recall,
\begin{theorem}
  A natural number~$p$ is prime if and only if
  for {\em every} $a\not\con 0\pmod{p}$,
  $$
    a^{p-1}\con 1\pmod{p}.
  $$
\end{theorem}

Thus if $p\in\N$ and, e.g., $2^{p-1}\not\con 1\pmod{p}$, then
we have proved that~$p$ is {\em not} prime.  If, however,
$a^{p-1}\con 1\pmod{p}$ for a couple of~$a$, then it is ``highly likely''
that~$p$ is prime.  I will not analyze this probability here, but
we might later in this course.

\begin{example}
  Let $p=323$.  Is~$p$ prime?
  Let's compute
  $2^{322}$ modulo~$323$.  Making a table as above, we have
  \begin{center}
    \begin{tabular}{|cccc|}\hline
      \quad$i$\quad\quad & \quad $m$\quad\quad
                         & \quad $\eps_i$\quad \quad & \quad $2^{2^i}$ \text{mod} 323       \\\hline
      0                  & 322                       & 0                              & 2   \\\hline
      1                  & 161                       & 1                              & 4   \\\hline
      2                  & 80                        & 0                              & 16  \\\hline
      3                  & 40                        & 0                              & 256 \\\hline
      4                  & 20                        & 0                              & 290 \\\hline
      5                  & 10                        & 0                              & 120 \\\hline
      6                  & 5                         & 1                              & 188 \\\hline
      7                  & 2                         & 0                              & 137 \\\hline
      8                  & 1                         & 1                              & 35  \\\hline
    \end{tabular}
  \end{center}
  Thus
  $$2^{322} \con 4\cdot 188\cdot 35 \con 157\pmod{323},$$
  so $323$ is not prime.  In fact, $323 = 17\cdot 19$.
\end{example}

It's possible to prove that a large number is composite, but yet
be unable to (easily) find a factorization!
For example if
$$n = 95468093486093450983409583409850934850938459083,$$
then $2^{n-1}\not\con 1\pmod{n}$, so $n$ is composite.
This is something one could verify in a reasonable amount
of time by hand.  (Though finding a factorization by hand
would be very difficult!)

\subsection{Finding large numbers that are probably prime}
\begin{verbatim}
? probprime(n, a=2) = Mod(a,n)^(n-1) == Mod(1,n)
? x = 0948609348698406983409580934859034509834095809348509834905809345
%36 = 948609348698406983409580934859034509834095809348509834905809345
? for(i=0,100,if(probprime(x+2*i,2),print(i)))
27
? p = x + 2*27
%37 = 948609348698406983409580934859034509834095809348509834905809399
? probprime(p,3)
%39 = 1
\end{verbatim}








\chapter{Public-key Crypto I: Diffie-Hellman Key Exchange}

\hd{Key Ideas}
\begin{itemize}
  \item Public-key cryptography
  \item The Diffie-Hellman key exchange
\end{itemize}

\section{Public-key Cryptography}
\includegraphics[width=3em]{nikita.eps}
Nikita must communicate vital information to Michael, who is a thousand
kilometers away.  Their communications are being monitored by
The Collective, which must not discover the message.
If Nikita and Michael could somehow agree on a secret encoding
key, they could encrypt their message.  Fortunately, Nikita knows
about an algorithm developed by Diffie and Hellman in 1976.

\section{The Diffie-Hellman Key Exchange Protocol}
Nikita and Michael agree on a prime number~$p$ and
an integer~$g$ that has order $p-1$ modulo~$p$.
(So $g^{p-1}\con 1\pmod{p}$, but $g^{n} \not\con 1\pmod{p}$
for any positive $n<p-1$.)  Nikita chooses a random number
$n<p$, and Michael chooses a random number $m<p$.
Nikita sends $g^n\pmod{p}$ to Michael, and Michael
sends $g^m\pmod{p}$ to Nikita.
Nikita can now compute the secret key:
$$
  s = g^{mn} = (g^m)^n \pmod{p}.
$$
Likewise, Michael computes the secret key:
$$
  s = g^{mn} = (g^n)^m \pmod{p}.
$$
Now Nikita uses the secret key~$s$ to send Michael
an encrypted version of her critical message.  Michael,
who also knows~$s$, is able to decode the message.

Meanwhile, hackers in The Collective see both $g^n\pmod{p}$ and
$g^m\pmod{p}$, but they aren't able to use this information to deduce
either $m$, $n$, or $g^{mn}\pmod{p}$ quickly enough to stop Michael
from thwarting their plans.   Yeah!

The Diffie-Hellman key exchange is the first public-key cryptosystem
every published (1976).  The system was discovered by GCHQ (British
intelligence) a few years before Diffie and Hellman found it, but they
couldn't tell anyone about their work; perhaps it was discovered by
others before.  That this system was discovered independently more
than once shouldn't surprise you, given how simple it is!

\subsection{Some Quotes}
A review of Diffie and Hellman's groundbreaking article is amusing,
because the reviewer, J.S.\ Joel, says ``They propose a couple of
techniques for implementing the system, but the reviewer was
unconvinced.''
\begin{verbatim}
Diffie, Whitfield; Hellman, Martin E. 
 New directions in cryptography. 
 IEEE Trans. Information Theory IT-22 (1976), no. 6, 644--654. 
\end{verbatim}
\begin{quote}
  The authors discuss some of the recent results in communications
  theory that have arisen out of the need for security in the key
  distribution channels. They concentrate on the use of ciphers to
  restrict the extraction of information from a communication over an
  insecure [channel]. As is well known, the transmission and
  distribution is then likely to become a problem, in efficiency if not
  in security. The authors suggest various possible approaches to avoid
  these further problems that arise. The first they call a ``public key
  distribution system'', which has the feature that an unauthorized
  ``eavesdropper'' will find it computationally infeasible to decipher
  the message since the enciphering and deciphering are governed by
  distinct keys. They propose a couple of techniques for implementing
  the system, but the reviewer was unconvinced.
\end{quote}

Somebody named Alan Westrope wrote in 1998 about political
implications:
\begin{quote}
  The 1976 publication of ``New Directions in Cryptography'', by
  Whitfield Diffie and Martin Hellman, was epochal in cryptographic
  history. Many regard it as the beginning of public-key cryptography,
  analogous to a first shot in what has become an ongoing battle over
  privacy, civil liberties, and the meaning of sovereignty in cyberspace.
\end{quote}

%\newpage
Here is what Diffie and Hellman look like, respectively:
\begin{center}
  \includegraphics[width=10em]{diffie.eps}
  $\qquad$
  \includegraphics[width=10em]{hellman.eps}
\end{center}

\section{Let's try it!}
To make finding~$g$ easier, let's choose a prime~$p$
such that $(p-1)/2=q$ is prime (so $p-1 = 2q$, with $q$ prime).
Since for any~$g$ with $\gcd(g,p)=1$,
$$
  g^{2q} \con 1\pmod{p},
$$
the order of~$g$ is $1$, $2$, $q$, or $2q=p-1$,
so the order of~$g$ is easy to compute.

For our first example, let $p=23$.
Then $g=5$ has order $p-1=22$.
(I found $g=5$ using the function {\tt znprimroot} in PARI.  You can
also just compute the order of $2$, $3$, etc., until you find a number
with order $p-1$.)

\hd{Nikita:} Chooses secret $n=12$; sends $g^{12} = 5^{12} \con \mathbf{18}
  \pmod{23}$.

\hd{Michael:} Chooses secret $n=5$; sends $g^5 = 5^5 \con \mathbf{20}
  \pmod{23}$.

\hd{Compute Shared Secret:}\\
Nikita:
$
  20^{12} \con \mathbf{3}\pmod{23}
$\\
Michael:
$
  18^{5} \con \mathbf{3}\pmod{23}.
$

\section{The Discrete Logarithm Problem}

Let $a, b, n$ be positive real numbers.
Recall that
$$
  \log_b(a) = n \text{ if and only if } a=b^n.
$$
Thus the $\log_b$ function solves the following problem:
Given a base~$b$ and a power~$a$ of~$b$,
find an exponent~$n$ such that
$$
  a = b^n.
$$
That is, given $b^n$ and~$b$, find~$n$.
\begin{example}
  $a = 19683$, $b=3$.
  A calculator quickly gives that
  $$
    n = \log(19683) / \log(3) = 9.
  $$
\end{example}
The discrete log problem is the analogue of this problem modulo~$p$:

\hd{Discrete Log Problem:} Given $b\pmod{p}$ and $b^n\pmod{p}$,
find~$n$.  Put another way, compute $\log_b(a)$, when
$a, b \in\Z/p\Z$.

As far as we know, this problem is {\bf VERY HARD} to solve quickly.
Nobody has admitted publicly to having proved that the discrete log
can't be solved quickly, but many very smart people have tried hard
and not succeeded.  It's easy to write a {\em slow} program to
solve the discrete log problem.  (There are better methods but we
won't discuss them in this class.)
\begin{verbatim}
? dislog(x,g, s) = s=g; for(n=1,znorder(g),if(x==s, return(n), s=s*g)); 0;
? dislog(18,Mod(5,23))
%6 = 12
? dislog(20,Mod(5,23))
%7 = 5
\end{verbatim}
So the example above was far too simple.  Let's try a slightly larger
prime:
\begin{verbatim}
? p=nextprime(9584)
%8 = 9587
? isprime((p-1)\2)
%9 = 1
? znorder(Mod(2,p))
%10 = 9586
? g=Mod(2,p)
%11 = Mod(2, 9587)
? a = g^389
%15 = Mod(7320, 9587)
? dislog(a,g)
%16 = 389
\end{verbatim}
This is still very easy to ``crack''.  Let's try an even bigger one.
\begin{verbatim}
? p = 9048610007
%1 = 9048610007
?  g = Mod(5,p)
%2 = Mod(5, 9048610007)
? a = g^948603
%3 = Mod(3668993056, 9048610007)
? dislog(a,g)              \\ this take a while
%4 = 948603
? znlog(a,g)        \\ builtin super-optimized version takes about 1/2 second
%31 = 948603
\end{verbatim}
Computing the discrete log gets slow quickly, the larger we make the~$p$.
Doubling the number of digits of the modulus makes the discrete log much
much harder.

\subsection{The State of the Art}
\begin{verbatim}
Discrete logarithms in GF(2^n)
From: Reynald LERCIER <<EMAIL>>
 To: <EMAIL>
 Date: Tue, 25 Sep 2001 13:37:18 -0400
 
We are pleased to announce a new record for the discrete logarithm
problem. We were able to compute discrete logarithms in
GF(2^521). This was done in one month on a unique 525MHz
quadri-processors Digital Alpha Server 8400 computer. The approach
that we followed is a careful implementation of the general Function
Field Sieve as described from a theoretical point of view by Adleman
[Ad94].

As far as we know, the largest such computation previously done was
performed in GF(2^401) [GoMc92] using an algorithm due to Coppersmith
[Co84]. 

[...]
So, as a conclusion, time that we need for computing discrete
logarithms in GF(2^521) on a 525 MHz quadri-processor alpha server
8400 computer is approximatively 12 hours for each, once the sieving
step (21 days) and the linear algebra step (10 days) is performed.

Antoine JOUX    (DCSSI, Issy les Moulineaux, France, <EMAIL>),
Reynald LERCIER (CELAR, Rennes, France, <EMAIL>).
\end{verbatim}



\section{Realistic Example}

\begin{verbatim}
? p=nextprime(93450983094850938450983409583)
%17 = 93450983094850938450983409611
? isprime((p-1)\2)
%18 = 0
? nextgoodprime(p) = while(!isprime((p-1)\2), p=nextprime(p+1)); p
? nextgoodprime(p)
%19 = 93450983094850938450983409623
? g=2
%21 = 2
? znorder(Mod(g,p))
%22 = 93450983094850938450983409610
? ?random
random({N=2^31}): random integer between 0 and N-1.
? nikita = random(p)
%23 = 18319922375531859171613379181
? michael = random(p)
%24 = 82335836243866695680141440300
? nikita_say = Mod(g,p)^nikita
%26 = Mod(17037287637415625385373411504, 93450983094850938450983409611)
? michael_say=Mod(g,p)^michael
%27 = Mod(2201425894324369970772940547, 93450983094850938450983409611)
? secret = nikita_say^michael
%28 = Mod(25591938014843312529239952955, 93450983094850938450983409611)
? secret = michael_say^nikita
%29 = Mod(25591938014843312529239952955, 93450983094850938450983409611)
\end{verbatim}







\chapter{The RSA Public-Key Cryptosystem, I}


\hd{Key Ideas:}
\begin{itemize}
  \item Creating an RSA public key
  \item Encrypting and decrypting messages
  \item Breaking RSA and factoring
\end{itemize}

\tableofcontents
\section{How RSA works}
\subsection{One-way Functions}
The {\em fundamental idea} behind RSA is to try to construct
a ``one-way function'', i.e., an ``encryption'' function
$$
  E : X \ra X
$$
such that it is easy for Nikita, say, to compute $E^{-1}$, but very
hard for anybody else to compute $E^{-1}$.

\subsection{How Nikita Makes an RSA Public Key}
Here is how Nikita makes a one-way function~$E$:
\begin{enumerate}
  \item Nikita picks two large primes~$p$ and~$q$, and lets $n=pq$.
  \item It is easy for Nikita to then compute
        $$
          \vphi(n) = \vphi(p)\cdot \vphi(q) = (p-1)\cdot (q-1).
        $$
  \item Nikita next chooses a ``random'' integer~$e$ with
        $$
          1<e<\vphi(n)\text{ and }
          \gcd(e,\vphi(n))=1.
        $$

  \item Finally, Nikita uses the algorithm from Lecture 7 to find a
        solution~$d$ to the equation
        $$
          ex \con 1\pmod{\vphi(n)}.
        $$
\end{enumerate}

\hd{The Encoding Function:}\\
Nikita defines a function $E:\Z/n\Z \ra \Z/n\Z$
$$
  E(x) = x^e.
$$
(Recall that $\Z/n\Z = \{0,1,\ldots,n-1\}$ with addition and multiplication
modulo~$n$.)
Then anybody can compute~$E$ fairly quickly using the repeated-squaring
algorithm from Lecture~7.

Nikita's {\bf public key} is the pair of integers $(n,e)$, which is
just enough information for people to easily compute~$E$.
Nikita knows a number~$d$ such that $ed\con 1\pmod{\vphi(n)}$,
so, as we will see below, she can quickly compute $E^{-1}$.

Now Michael or even The Collective can send Nikita a message whenever
they want, even if Nikita is asleep.  They look up how to compute~$E$
and compute $E(\text{their message})$.

\subsection{Sending Nikita an Encrypted Message}
Encode your message as a sequence
of numbers modulo~$n$ (see Section~\ref{sec:encode}):
$$
  m_1, \ldots, m_r \in \Z/n\Z.
$$
Send
$$
  E(m_1), \ldots, E(m_r)
$$
to Nikita.   (Recall that $E(m) = m^e$.)

\subsection{How Nikita Decrypts a Message}
When Nikita receives an $E(m_i)$, she finds~$m_i$ as follows:
$$
  m_i = E^{-1}(E(m_i))  = E(m_i)^d = (m_i^{e})^{d}=m_i.
$$
The following proposition proves that the last equality holds.
\begin{proposition}
  Let~$n$ be a square-free integer and let $d,e\in\N$ such that
  $p-1\mid de-1$ for each prime $p\mid n$.  Then
  $a^{de} \con a\pmod{n}$ for all $a\in\Z$.
\end{proposition}
\begin{proof}
  Since $n\mid a^{de}-a$ if and only if $p\mid a^{de}-a$ for each prime
  divisor of~$p$, it suffices to prove that $a^{de}\con a\pmod{p}$ for
  each prime divisor $p$ of~$n$.  If $\gcd(a,p)\neq 0$, then
  $a\con 0\pmod{p}$, so $a^{de} \con a\pmod{p}$.
  If $\gcd(a,p)=1$, then Fermat's Little
  Theorem asserts that $a^{p-1}\con 1\pmod{p}$.
  Since $p-1\mid de-1$, we have $a^{de-1} \con 1\pmod{p}$
  as well.  Multiplying both sides
  by~$a$ shows that $a^{de}\con a\pmod{p}$.
\end{proof}

\section{Encoding a Phrase in a Number}\label{sec:encode}
Think of a sequence of letters and spaces as a number in base~$27$.
Let a single-space correspond to $0$, the letter $A$ to 1, $B$ to 2,
..., $Z$ to 26.  Thus, e.g., ``HARVARD'' denotes a number written in
base $27$.  The corresponding number written in decimal is $1808939906$:
$$
  \text{HARVARD}\quad\leftrightarrow\quad
  8 + 27\cdot 1 + 27^2\cdot 18 + 27^3\cdot22 + 27^4\cdot1+27^5\cdot18 + 27^6\cdot 4
  = 1808939906
$$
To recover the digits of the number, repeatedly divide by $27$:
$$
  \begin{array}{lcrcrr}
    1808939906 & = & 66997774\cdot 27 & + & 8 & \text{H} \\
    66997774   & = & 2481399\cdot 27  & + & 1 & \text{A} \\
  \end{array}
$$
and so on.

\subsection{How Many Letters Can a Number ``Hold''?}
If $27^k<n$, then $k$ letters can be encoded in a number $<n$.
Put another way,
$$
  k < \log(n) / \log(27) = \log_{27}(n).
$$

\section{Examples}
\subsection{A Small Example}
So the arithmetic is easy to follow, we use small primes~$p$
and~$q$ and encrypt the single letter ``X''.

\begin{enumerate}
  \item Choose $p$ and $q$: Let $p=17$, $q=19$, so $n=pq = 323$.
  \item Compute $\vphi(n)$:
        $$\vphi(n) = \vphi(p\cdot q)=\vphi(p)\cdot\vphi(q)
          = (p-1)(q-1) = pq-p-q+1 = 323-17-19+1 = 288.$$
  \item Randomly choose an $e\in\Z/323\Z$: We choose $e=95$.
  \item Solve
        $$
          95x \con 1\pmod{288}.
        $$
        Using the GCD algorithm, we find that $d=191$ solves
        the equation.
\end{enumerate}

The public key is $(323,95)$.  So $E:\Z/323\Z\ra \Z/323\Z$ is
defined by
$$
  E(x) = x^{95}.
$$

Next, we encrypt the letter ``X''.  It is encoded as the number
$24$, since X is the $24$th letter of the alphabet.
We have
$$
  E(24) = 24^{95} = 294 \in \Z/323\Z.
$$

To decrypt, we compute $E^{-1}$:
$$
  E^{-1}(294) = 294^{191} = 24 \in \Z/323\Z.
$$

\subsection{A Bigger Example in PARI}
\begin{verbatim}
? p=nextprime(random(10^30))
%3 = 738873402423833494183027176953
? q=nextprime(random(10^25))
%4 = 3787776806865662882378273
? n=p*q
%5 = 2798687536910915970127263606347911460948554197853542169
? e=random(n)
%6 = 1483959194866204179348536010284716655442139024915720699
? phin=(p-1)*(q-1)
%7 = 2798687536910915970127262867470721260308194351943986944
? while(gcd(e,phin)!=1,e=e+1)
? e
%8 = 1483959194866204179348536010284716655442139024915720699
? d = lift(Mod(e,phin)^(-1));
%9 = 2113367928496305469541348387088632973457802358781610803
? (e*d)%phin
%10 = 1
? log(n)/log(27)
%11 = 38.03851667699197952338510248
\end{verbatim}
We can encode single blocks of up to 38 letters.  Let's encode
``HARVARD'':
\begin{verbatim}
? m=8+27*1+27^2*18+27^3*22+27^4*1+27^5*18+27^6*4
%12 = 1808939906
? E(x)=lift(Mod(x,n)^e)
? D(x)=lift(Mod(x,n)^d)
? secret_message = E(m)
%14 = 625425724974078486559370130768554070421628674916144724
? D(secret_message)
%15 = 1808939906
\end{verbatim}

The following complete PARI program automates the whole
process, though it is a little clumsy.
Call this file {\tt rsa.gp}.  It uses \{ and \} so that
functions can be extended over more than one line.
\begin{verbatim}
/* rsa.gp ------------------------------------------------ */
{alphabet=[" ","A","B","C","D","E","F","G","H","I","J","K","L","M",
           "N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];
}
{letter_to_number(l, 
     n)=
     for(n=1,27,if(alphabet[n]==l,return(n-1))); 
     error("invalid input.")
}
{number_to_message(n,    
     s="")= 
     while(n>0, s = concat(s,alphabet[n%27+1]); n = n \ 27); 
     return(s)
}
{message_to_number(w,     
     i,n=0)=
     for(i=1,length(w), n = n + 27^(i-1)*letter_to_number(w[i]));
     return(n);
}
{make_rsa_key(len,  
     p,q,n,e,d)=
     p = nextprime(random(10^(len\2+1)));
     q = nextprime(random(10^(len\2+3)));
     n = p*q; phin = (p-1)*(q-1);
     e = random(phin);
     while(gcd(e,phin)!=1,e=e+1);
     d = lift(Mod(e,phin)^(-1));
     return([n,e,d]);
}
encrypt(message, n, e) = lift(Mod(message_to_number(message),n)^e);
decrypt(secret, n, d)  = number_to_message(lift(Mod(secret,n)^d));
/* rsa.gp ------------------------------------------------ */
\end{verbatim}

Here is an example that uses the above little program.
\begin{verbatim}
? \r rsa
? setrand(1)   \\ default random number seed is 1!
? rsa=make_rsa_key(20)   \\ returns [n, e, d]
%2 = [89050154117716728145939, 33735260657253161660951, 
      49244741969289756040079]
? n = rsa[1]; e = rsa[2]; d = rsa[3]; 
? public_key = [n,e]
%3 = [89050154117716728145939, 33735260657253161660951]
? msg = ["H", "A", "R", "V", "A", "R", "D"];   \\ clumsy!!!
? secret = encrypt(msg,n,e)
%36 = 75524965161901413275866
? decrypt(secret, n, d)
%37 = "HARVARD"
\end{verbatim}

\section{A Connection Between Breaking RSA and Factoring Integers}
Nikita's public key is $(n,e)$.
If we compute the factorization of $n=pq$, then we can compute
$\vphi(n)$ and hence deduce her secret decoder number~$d$.

\noindent{\em It is no easier to $\vphi(n)$ than to factor~$n$:}\\
Suppose $n=pq$.  Given $\vphi(n)$, it is very easy to
compute $p$ and $q$.  We have
$$
  \vphi(n) = (p-1)(q-1) = pq-(p+q)+1,
$$
so we know $pq=n$ and $p+q = n+1 - \vphi(n)$.
Thus we know the polynomial
$$
  x^2 - (p+q)x + pq = (x-p)(x-q)
$$
whose roots are~$p$ and~$q$.


There is also a more complicated ``probabilistic algorithm'' to
find~$p$ and~$q$ given the secret decoding number~$d$.  I might
describe it in the next lecture.

%\hd{A Moral:} {\em The RSA system relies primarily on the difficulty
%of factoring integers.  Diffie-Hellman relies primarily on the
%difficulty of computing discrete logarithms.}





\chapter{Attacking RSA}



Nikita's public key is $(n,e)$.  If we compute the factorization of
$n=pq$, then we can compute $\vphi(n)$ and hence deduce her secret
decoding number~$d$.  Thus attempting to factor~$n$ is a way to try to
break an RSA public-key cryptosystem. In this lecture we consider
several approaches to ``cracking'' RSA, and relate them to the
difficulty of factoring~$n$.

\section{Factoring $n$ Given $\vphi(n)$}\label{sec:phin}

\hd{If you know $\vphi(n)$ then it is easy to factor~$n$:}\\
Suppose $n=pq$.  Given $\vphi(n)$, it is very easy to
compute $p$ and $q$.  We have
$$
  \vphi(n) = (p-1)(q-1) = pq-(p+q)+1,
$$
so we know both $pq=n$ and $p+q = n+1 - \vphi(n)$.
Thus we know the polynomial
$$
  x^2 - (p+q)x + pq = (x-p)(x-q)
$$
whose roots are~$p$ and~$q$.
These roots can be found using the quadratic formula.
\begin{example}\mbox{}\vspace{-5ex}\\
  \begin{verbatim}
? n=nextprime(random(10^10))*nextprime(random(10^10));
? phin=eulerphi(n);
? f = x^2 - (n+1-phin)*x + n
%6 = x^2 - 12422732288*x + 31615577110997599711
? polroots(f)
%7 = [3572144239, 8850588049]
? n
%8 = 31615577110997599711
? 3572144239*8850588049
%9 = 31615577110997599711
\end{verbatim}
\end{example}



\section{When~$p$ and~$q$ Are Close}
Suppose that~$p$ and~$q$ are ``close'' to each other.  Then
it is easy to factor~$n$ using a factorization
method of Fermat.

Suppose $n=pq$ with $p>q$, say.  Then
$$n = \left(\frac{p+q}{2}\right)^2 -
  \left(\frac{p-q}{2}\right)^2.$$
Since $p$ and $q$ are ``close'',
$$
  s = \frac{p-q}{2}
$$
is small,
$$
  t = \frac{p+q}{2}
$$
is only slightly larger than $\sqrt{n}$,
and $t^2-n=s^2$ is a perfect square.
So we just try
$$
  t = \text{ceil}(\sqrt{n}), \quad t=\text{ceil}(\sqrt{n})+1,
  \quad t=\text{ceil}(\sqrt{n})+2, \ldots
$$
until $t^2-n$ is a perfect square $s^2$.
Then
$$
  p = t+s,\qquad q=t-s.
$$
\begin{example}
  Suppose $n=23360947609$.
  Then
  $$\sqrt{n} = 152842.88\ldots.$$

  If $t=152843$, then $\sqrt{t^2-n} = 187.18\ldots$.

  If $t=152844$, then $\sqrt{t^2-n} = 583.71\ldots$.

  If $t=152845$, then $\sqrt{t^2-n} = 804\in\Z$.

  Thus $s=804$.  We find that $p=t+s=153649$ and $q=t-s=152041$.
\end{example}

\comment{\begin{verbatim}
? n=23360947609
%1 = 23360947609
? sqrt(n)
%2 = 152842.8853725288712694157797
? x=%2
%3 = 152842.8853725288712694157797
? floor(x+1)
%4 = 152843
? t=floor(x+1)
%5 = 152843
? t^2-n
%6 = 35040
? sqrt(t^2-n)
%7 = 187.1897433087614445431082470
? t++
%8 = 152844
? sqrt(t^2-n)
%9 = 583.7182539547654063924081356
? t++
%10 = 152845
? sqrt(t^2-n)
%11 = 804.0000000000000000000000000
? s=804
%12 = 804
? p=t+s
%13 = 153649
? q=t-s
%14 = 152041
? p*q
%15 = 23360947609
? n
%16 = 23360947609
? factor(n)
%17 =
[152041 1]
[153649 1]
\end{verbatim}
\end{example}
}

Here is a bigger example in PARI:
\begin{verbatim}
? q=nextprime(random(10^50))
%20 = 78177096444230804504075122792410749354743712880803
? p=nextprime(q+1)  \\ a nearby prime
%21 = 78177096444230804504075122792410749354743712880899
? n=p*q
%22 = 6111658408450564697085634201845976850509908580949986889525704...
      ...259650342157399279163289651693722481897
? t=floor(sqrt(n))+1
  ***   precision loss in truncation
? \p150        \\ set precision of floating-point computations.
   realprecision = 154 significant digits (150 digits displayed)
? t=floor(sqrt(n))+1
%29 = 78177096444230804504075122792410749354743712880851
? sqrt(t^2-n)
%30 = 48.000000000000000000000000000000000000000000000000000000....
? s=48
%31 = 48
? t + s     \\ p
%33 = 78177096444230804504075122792410749354743712880899
? t - s     \\ q
%35 = 78177096444230804504075122792410749354743712880803
\end{verbatim}

\section{Factoring~$n$ Given~$d$}
Suppose that we crack an RSA cryptosystem by
finding a~$d$ such that
$$
  a^{ed} \con a\pmod{n}
$$
for all~$a$.  Then we've found an~$m$ ($=ed-1$) such that
$a^m\con 1\pmod{n}$ for all~$a$ with $\gcd(a,n)=1$.
Knowing~$a$ does not lead to a factorization of~$n$
in as direct a manner as knowing $\vphi(n)$ does (see
Section~\ref{sec:phin}).  However, there is a probabilistic
procedure that, given an~$m$ such that $a^m\con 1\pmod{n}$,
will with high probability find a factorization of~$n$.

\hd{Probabilistic procedure to factor~$n$:}
\begin{enumerate}
  \item $m$ is even since $(-1)^m\con 1\pmod{n}$.
  \item If $a^{m/2}\con 1\pmod{n}$ for all~$a$ coprime to $n$,
        replace~$m$ by $m/2$.  In practice, it is not possible to determine
        whether or not this condition holds, because it would require doing a
        computation for too many~$a$.  Instead, we try a few random~$a$; if
        $a^{m/2}\con 1\pmod{n}$ for the~$a$ we check, then we divide~$m$
        by~$2$.  (If there exists even a single~$a$ such that $a^{m/2}\not\con
          1\pmod{n}$, then at least half the~$a$ have this property.)

        Keep repeating this step until we find an~$a$ such that
        $a^{m/2}\not\con 1\pmod{n}$.

  \item There is a 50\% chance that a randomly chosen~$a$
        will have the property that
        $$a^{m/2}\con+1\pmod{p},\qquad
          a^{m/2}\con -1\pmod{q}$$
        or
        $$a^{m/2}\con -1\pmod{p},\qquad
          a^{m/2}\con +1\pmod{q}.$$
        If the first case occurs, then
        $$p\mid a^{m/2}-1, \qquad \text{ but }
          q\nmid a^{m/2}-1,$$
        so
        $$
          \gcd(a^{m/2}-1,pq) = p,
        $$
        and we have factored~$n$.
        Just keep trying~$a$'s until one of the cases occurs.
\end{enumerate}


\begin{verbatim}
? \r rsa   \\ load the file rsa.gp, available at Lecture 9 web page.
? rsa = make_rsa_key(10)
%34 = [32295194023343, 29468811804857, 11127763319273]
? n = rsa[1]; e = rsa[2]; d = rsa[3];
? m = e*d-1
%38 = 327921963064646896263108960
? for(a=2,20, if(Mod(a,n)^m!=1,print(a)))   \\ prints nothing...
? m = m/2
%39 = 163960981532323448131554480
? for(a=2,20, if(Mod(a,n)^m!=1,print(a)))
? m = m/2
%40 = 81980490766161724065777240
? for(a=2,20, if(Mod(a,n)^m!=1,print(a)))
? m = m/2
%41 = 40990245383080862032888620
? for(a=2,20, if(Mod(a,n)^m!=1,print(a)))
? m = m/2
%42 = 20495122691540431016444310
? for(a=2,20,if(Mod(a,n)^m!=1,print(a)))
2
5
6
... etc.
? gcd(2^m,n)
  ***   power overflow in pow_monome.
? x = lift(Mod(2,n)^m)-1
%43 = 4015382800098
? gcd(x,n)
%46 = 737531
? p = gcd(x,n)
%53 = 737531
? q = n/p
? p*q
%54 = 32295194023343
? n
%55 = 32295194023343
\end{verbatim}

\section{RSA Challenge $n$}
The easiest challenge at
\begin{verbatim}
  http://www.rsasecurity.com/rsalabs/challenges/factoring/numbers.html
\end{verbatim}
is the 576-bit number
\begin{verbatim}
Name:         RSA-576
Prize:        $10000
Digits:       174
Digit Sum:    785
188198812920607963838697239461650439807163563379417382700763356422988859
715234665485319060606504743045317388011303396716199692321205734031879550
656996221305168759307650257059
\end{verbatim}







\chapter{Primitive Roots}



\hd{Key Idea:} {\em There is an element of $(\Z/p\Z)$ of order $p-1$.}

\section{Polynomials over $\Z/p\Z$}
\begin{proposition}\label{prop:atmost}
  Let $f\in(\Z/p\Z)[x]$ be a nonzero polynomial
  over the ring $\Z/p\Z$.  Then there are at most
  $\deg(f)$ elements $\alpha\in\Z/p\Z$ such that
  $f(\alpha)=0$.
\end{proposition}
\begin{proof}
  We proceed by induction on $\deg(f)$.  The cases
  $\deg(f)=0,1$ are clear.  Write
  $f = a_n x^n + \cdots a_1 x + a_0$.  If
  $f(\alpha)=0$ then
  \begin{align*}
    f(x) & = f(x) - f(\alpha)                                            \\
         & = a_n(x^n-\alpha^n) + \cdots a_1(x-\alpha) + a_0(1-1)         \\
         & = (x-\alpha)(a_n(x^{n-1}+\cdots + \alpha^{n-1}) + \cdots a_1) \\
         & = (x-\alpha)g(x),
  \end{align*}
  for some polynomial $g(x)\in(\Z/p\Z)[x]$.
  Next suppose that $f(\beta)=0$ with $\beta\neq \alpha$.  Then
  $(\beta-\alpha) g(\beta) = 0$, so, since $\beta-\alpha\neq 0$
  (hence $\gcd(\beta-\alpha, p)=1$, we have $g(\beta)=0$.
  By our inductive hypothesis,~$g$ has at most $n-1$ roots, so
  there are at most $n-1$ possibilities for~$\beta$.
  It follows that~$f$ has at most~$n$ roots.
\end{proof}

\comment{
\begin{example}
  Find the roots\footnote{This
    example was part of the Harvard graduate school qualifying exam
    this year.}
  of
  $$
    f = x^4 - x^3 + x^2 +x +1 \in(\Z/3\Z)[x].
  $$
  Solution: $f(0) = 1$, $f(1)=0$, $f(2)=0$, so the roots are
  $\{1,2\}$. Also,
  $$
    f = (x+1)(x+2)(x^2+2x+2).
  $$
  Incidentally,
  PARI can factor polynomials over finite fields very quickly:
  \begin{verbatim}
? factormod(x^4 - x^3 + x^2 +x +1,3)
%1 =
[Mod(1, 3)*x + Mod(1, 3) 1]
[Mod(1, 3)*x + Mod(2, 3) 1]
[Mod(1, 3)*x^2 + Mod(2, 3)*x + Mod(2, 3) 1]
\end{verbatim}
\end{example}
}

\begin{proposition}\label{prop:dsols}
  Let $p$ be a prime number and let $d$ be a divisor
  of $p-1$.  Then $f(x) = x^d-1\in(\Z/p\Z)[x]$ has
  exactly~$d$ solutions.
\end{proposition}
\begin{proof}
  Let $e$ be such that $de=p-1$.
  We have
  \begin{align*}
    x^{p-1} - 1 & = (x^d)^e - 1                                       \\
                & = (x^d - 1)((x^d)^{e-1} + (x^d)^{e-2} + \cdots + 1) \\
                & = (x^d - 1)g(x),
  \end{align*}
  where $\deg(g(x)) = p-1-d$.
  Recall that Fermat's little theorem implies that $x^{p-1}-1$ has
  exactly $p-1$ roots in $\Z/p\Z$.  By Proposition~\ref{prop:atmost},
  $f(x)$ has {\em at most}
  $p-1-d$ roots and $x^d-1$ has at most~$d$ roots, so
  $g(x)$ has exactly $p-1$ roots and $x^d - 1$ has
  exactly~$d$ roots, as claimed.
\end{proof}

\hd{WARNING:} The analogue of this theorem is false
for some $f\in(\Z/n\Z)[x]$ with~$n$ composite.
For example, if $n=n_1\cdot n_2$ with $n_1, n_2\neq 1$, then
$f=nx$ has at least {\em two} distinct zeros, namely~$0$
and $n_2\neq 0$.

\section{The Structure of $(\Z/p\Z)^* = \{1,2,\ldots, p-1\}$}
In this section, we prove that the group
$(\Z/p\Z)^*$ is cylic.

\begin{definition}
  A {\em primitive root} modulo~$p$ is an element of
  $(\Z/p\Z)^*$ of order $p-1$.
\end{definition}

\hd{Question:} For which primes~$p$ is there a primitive
root? (Ans. Every prime.)

\begin{lemma}\label{lemma}
  Suppose $a,b\in(\Z/n\Z)^*$ have orders~$r$ and~$s$, respectively,
  and that $\gcd(r,s)=1$.  Then $ab$ has order $rs$.
\end{lemma}
This is a general fact about commuting elements of a group.
\begin{proof}
  Since
  $
    (ab)^{rs} = a^{rs}b^{rs}=1,
  $ the order of $ab$ is a divisor $r_1 s_1$ of $rs$, where
  $r_1\mid r$ and $s_1\mid s$.
  Thus
  $$
    a^{r_1 s_1}b^{r_1 s_1} = (ab)^{r_1 s_1} = 1.
  $$
  Raise both sides to the power $r_2$, where $r_1 r_2 = r$.
  Then
  $$
    a^{r_1 r_2 s_1} b^{r_1 r_2 s_1} = 1,
  $$
  so, since $a^{r_1 r_2 s_1} = (a^{r_1 r_2})^{s_1} = 1$,
  $$
    b^{r_1 r_2 s_1} = 1.
  $$
  This implies that $s\mid r_1 r_2 s_1$,
  and, since $\gcd(s,r_1 r_2)=1$, it follows that $s=s_1$.
  A similar argument shows that $r=r_1$, so the order of $ab$ is $rs$.
\end{proof}




\begin{theorem}\label{theorem}
  For every prime~$p$ there is a primitive
  root mod~$p$.  In other words, the
  group $(\Z/p\Z)^*$ is a cyclic group
  of order $p-1$.
\end{theorem}
\begin{proof}
  Write
  $$
    p-1 = q_1^{n_1}q_2^{n_2}\cdots q_r^{n_r}
  $$
  as a product of distinct primes $q_i$.

  By Proposition~\ref{prop:dsols},
  the polynomial $x^{q_i^{n_i}}-1$ has exactly
  $q_i^{n_i}$ roots, and the polynomial
  $x^{q_i^{n_i-1}}-1$ has exactly
  $q_i^{n_i-1}$ roots.  Thus there is an $a_i\in\Z/p\Z$ such
  that $a_i^{q_i^{n_i}}=1$ but $a_i^{q_i^{n_i-1}}\neq 1$.
  This $a_i$ has order $q_i^{n_i}$.
  For each $i=1,\ldots, r$, choose such an~$a_i$.
  By repeated application of Lemma~\ref{lemma}, we see that
  $$
    a = a_1 a_2 \cdots a_r
  $$
  has order
  $q_1^{n_1}\cdots q_r^{n_r} = p-1$,
  so~$a$ is a primitive root.
\end{proof}


\begin{remark}
  There are $\vphi(p-1)$ primitive roots modulo~$p$, since there are
  $q_i^{n_i} - q_i^{n_i-1}$ ways to choose $a_i$.  To see this, we check
  that two distinct choices of sequence $a_1,\ldots, a_r$ define two
  different primitive roots.  Suppose that
  $$
    a_1 a_2 \cdots a_r = a'_1 a'_2 \cdots a'_r,
  $$
  with $a_i$, $a'_i$ of order $q_i^{n_i}$, for $i=1,\ldots,r$.
  Upon raising both sides of this equality to the power
  $s = q_2^{n_2}\cdots q_r^{n_r}$, we see that
  $a_1^s = a_1'^s$.  Since $\gcd(s,q_1^{n_1})=1$,
  there exists~$t$ such that $st\con1\pmod{q_1^{n_1}}$.
  It follows that
  $$
    a_1 = (a_1^s)^t = (a_1'^s)^t = a'_1.
  $$
  Upon canceling $a_1$ from both sides, we see that $a_2 \cdots a_r =
    a'_2 \cdots a'_r$; by repeating the above argument, we see that $a_i =
    a'_i$ for all~$i$.  Thus, different choices of the $a_i$ must lead to
  different primitive roots; in other words, if the primitive roots are the
  same, then the $a_i$ were the same.

  For example, there are $\vphi(16)=2^4-2^4=8$ primitive roots
  mod $17$:
  \begin{verbatim}
? for(n=1,16,if(znorder(Mod(n,17))==16,print1(n," ")))
3 5 6 7 10 11 12 14
\end{verbatim}
\end{remark}

\begin{example}
  In this example, we illustrate the proof of Theorem~\ref{theorem}
  when $p=13$.   We have
  $$p-1 = 12 = 2^2\cdot 3.$$
  The polynomial $x^4 - 1$ has roots $\{1,5,8,12\}$ and
  $x^2-1$ has roots $\{1,12\}$, so we take $a_1=5$.
  The polynomial $x^3-1$ has roots $\{1,3,9\}$, so
  set $a_2=3$.  Finally, $a=5\cdot 3=15=2$.  Note
  that the successive powers of $2$ are
  $$2,\, 4,\, 8,\,  3,\,  6,\,  12,\,  11,\,  9,\,  5,\,  10,\,  7,\,  1,$$
  so~$2$ really does have order~$12$.
\end{example}

\begin{example}
  The result is false if, e.g.,~$p$ is replaced by a big power of~$2$.
  The elements of $(\Z/8\Z)^*$ all have order dividing~$2$, but
  $\vphi(8)=4$.
\end{example}


\begin{theorem}\label{theorem:cyclic}
  Let~$p^n$ be a power of an odd prime.  Then there is an element of
  $(\Z/p^n\Z)^*$ of order $\vphi(p^n)$.  Thus $(\Z/p^n\Z)^*$ is cyclic.
\end{theorem}
I will not prove Theorem~\ref{theorem:cyclic} in class.  I will
probably put a problem on your next homework set that will guide you
to a proof.


\section{Artin's Conjecture}
\begin{conjecture}[Emil Artin]
  If $a\in\Z$ is not $-1$ or a perfect square, then
  the number $N(x,a)$ of primes $p\leq x$ such that~$a$
  is a primitive root modulo~$p$ is asymptotic to $C(a)\pi(x)$,
  where $C(a)$ is a constant that depends only on~$a$.
  In particular, there are infinitely many primes~$p$ such
  that $a$ is a primitive root modulo~$p$.
\end{conjecture}
Nobody has proved this conjecture for even a single choice of~$a$.
There are partial results, e.g., that there are infinitely
many $p$ such that the order of $a$ is divisible by the largest
prime factor of $p-1$. (See, e.g., Moree, Pieter, {\em
    A note on {A}rtin's conjecture}.)

%The web page 
%\begin{verbatim}
%http://www.ieeta.pt/~tos/p-roots.html
%\end{verbatim}
%contains some computational results about Artin's conjecture.




%\newcommand{\kr}[2]{\left(\frac{#1}{#2}\right)}
\chapter{Quadratic Reciprocity I}



\hd{Key Ideas:}
\begin{itemize}
  \item {\em Euler's Criterion:} When is $a$ a square modulo~$p$?
  \item Quadratic reciprocity
  \item Lemma of Gauss
\end{itemize}

\section{Euler's Criterion}


\begin{proposition}[Euler's Criterion]\label{prop:euler}
  Let~$p$ be an odd prime and~$a$ an integer not divisible by~$p$.
  Then $x^2\con a\pmod{p}$ has a solution if and only if
  $$
    a^{(p-1)/2}\con 1\pmod{p}.
  $$
\end{proposition}
\begin{proof}
  By the theorem from Lecture~11, there is an integer~$g$ that
  has order~$p-1$ modulo~$p$.  Every integer coprime to~$p$ is
  congruent to a power of~$g$.
  First suppose that~$a$ is congruent to a perfect square modulo~$p$,
  so
  $$
    a\con (g^r)^2 \con g^{2r}\pmod{p}
  $$
  for some~$r$.  Then
  $$
    a^{(p-1)/2} \con g^{2r\cdot\frac{p-1}{2}} \con
    g^{r(p-1)}\con 1\pmod{p}.
  $$
  Conversely, suppose that
  $a^{(p-1)/2}\con 1\pmod{p}$.  We have
  $a\con g^r \pmod{p}$ for some integer~$r$.
  Thus $g^{r(p-1)/2}\con 1\pmod{p}$, so
  $$
    p-1 \mid r(p-1)/2
  $$
  which implies that~$r$ is even.
  Thus $a\con (g^{r/2})^2\pmod{p}$, so~$a$
  is congruent to a square modulo~$p$.
\end{proof}

\begin{corollary}
  If $x^2\con a\pmod{p}$ has no solutions if and only if $a^{(p-1)/2}\con
    -1\pmod{p}$.
\end{corollary}
\begin{proof}
  This follows from Proposition~\ref{prop:euler} and that
  the polynomial $x^2-1$ has no roots besides $+1$ and $-1$.
\end{proof}



\begin{example}
  Suppose $p=11$.
  By squaring each element of $(\Z/11\Z)^*$, we see
  exactly which numbers are squares modulo~$11$:
  $$
    1^2 = 1,\, 2^2 = 4,\, 3^2 = 9,\, 4^2 = 5,\, 5^2 = 3,\, 6^2 = 3,\,
    7^2 = 5,\, 8^2 = 9,\, 9^2 = 4,\, 10^2 = 1.
  $$
  Thus the squares are $\{1,3,4,5,9\}$.
  Next, we compute $a^{(p-1)/2}=a^{5}$ for each $a\in(\Z/11\Z)^*$.
  $$
    1^{5} = 1,\, 2^{5} = -1,\, 3^{5} = 1,\, 4^{5} = 1,\, 5^{5} = 1,\,
    6^{5} = -1,\, 7^{5} = -1,\, 8^{5} = -1,\, 9^{5} = 1,\, 10^{5} = -1.
  $$
  The~$a$ with $a^5=1$ are $\{1, 3, 4, 5, 9\}$, which is exactly the
  same as the set of squares, just as Proposition~\ref{prop:euler}
  predicts.

\end{example}

\begin{example}
  Determine whether or not~$3$ is a square
  modulo $p=726377359$.  \newline{\bf Answer:}
  We compute $3^{(p-1)/2}$ modulo~$p$
  using PARI:
  \begin{verbatim}
? Mod(3,p)^((p-1)/2)
%5 = Mod(726377358, 726377359)   \\ class of -1 modulo 726377359.
\end{verbatim}
  Thus~$3$ is not a square modulo~$p$.  This computation wasn't too
  difficult, but it would have been very tedious to carry about by hand.
  The law of quadratic reciprocity, which we will state in the next section,
  is a vastly more powerful way to answer such questions.
  For example, you could easily answer the above question
  by hand using quadratic reciprocity.
\end{example}


\begin{remark}\label{rem:homo}
  Proposition~\ref{prop:euler} can be reformulated in more group-theoretic
  language as follows.  The map
  $$
    (\Z/p\Z)^* \ra \{\pm 1\}
  $$
  that sends~$a$ to $a^{(p-1)/2}\pmod{p}$
  is a homomorphism of groups, whose kernel is
  the subgroup of squares of elements of $(\Z/p\Z)^*$.
\end{remark}


\begin{definition}
  An element $a\in\Z$ with $p\nmid a$ is called a {\em quadratic
      residue} modulo~$p$ if~$a$ is a square modulo~$p$.
\end{definition}


\section{The Quadratic Reciprocity Law}
Let~$p$ be an odd prime and let~$a$ be an integer with $p\nmid a$.
Set
$$
  \kr{a}{p} =
  \begin{cases}
    +1 & \text{if $a$ is a quadratic residue, and} \\
    -1 & \text{otherwise}.
  \end{cases}
$$
Proposition~\ref{prop:euler} implies that
$$
  \kr{a}{p} \con a^{(p-1)/2}\pmod{p}.
$$
Also, notice that
$$
  \kr{a}{p}\cdot \kr{b}{p} = \kr{ab}{p},
$$
because
$\kr{\cdot }{p}$ is a homomorphism (see Remark~\ref{rem:homo}).

The symbol $\kr{a}{p}$ only depends on the residue class
of~$a$ modulo~$p$.  Thus tabulating the value of
$\kr{a}{5}$ for hundreds of~$a$ would be silly.
  {\em Would it be equally silly to make a table of $\kr{5}{p}$
    for hundreds of primes~$p$?}  Let's begin making such a table
and see whether or not there is an obvious pattern.
(To compute $\kr{a}{p}$ in PARI, use the command {\tt kronecker(a,b)}.)
\begin{center}
  \begin{tabular}{|crc|}\hline
    $p$ & $\kr{5}{p}$ & $p$ mod 5 \\\hline
    7   & $-1 $       & 2         \\
    11  & $ 1 $       & 1         \\
    13  & $ -1$       & 3         \\
    17  & $ -1$       & 2         \\
    19  & $ 1 $       & 4         \\
    23  & $ -1$       & 3         \\
    29  & $ 1 $       & 4         \\
    31  & $ 1 $       & 1         \\
    37  & $ -1$       & 2         \\
    41  & $ 1 $       & 1         \\
    43  & $ -1$       & 3         \\
    47  & $ -1$       & 2         \\\hline
  \end{tabular}
\end{center}
The evidence suggests that $\kr{5}{p}$ depends only on the
congruence class of~$p$; more precisely, $\kr{5}{p}=1$ if and only
if $p\con 1,4\pmod{5}$, i.e., $p$ is a square modulo~$5$.
However, when I think directly about the equation
$$
  5^{(p-1)/2} \pmod{p},
$$
I see no way that knowing that $p\con 1,4 \pmod{5}$ helps us
to evaluate that strange expression!  And yet, the numerical
evidence is so {\em compelling}!  Argh!


Based on such computations, various mathematicians found a conjectural
explanation for this mystery in the 18th century.  Finally, on April
8, 1796, at your age (age 19), Gauss proved their conjecture.

\begin{theorem}[The Law of Quadratic Reciprocity]\label{thm:recip}
  Suppose that $p$ and $q$ are odd primes.  Then
  $$\kr{p}{q} = (-1)^{\frac{p-1}{2}\cdot \frac{q-1}{2}}\kr{q}{p}.$$
\end{theorem}
We will prove this theorem in the next lecture.

In the case considered above, this theorem implies that
$$
  \kr{5}{p} = (-1)^{2\cdot \frac{p-1}{2}} \kr{p}{5}
  =\kr{p}{5}
  = \begin{cases} +1 & \text{ if }p\con 1, 4\pmod{5}  \\
              -1 & \text{ if }p\con 2, 3\pmod{5}.
  \end{cases}
$$
Thus the quadratic reciprocity law ``explains'' why knowing~$p$ modulo~$5$
helps in computing $5^{\frac{p-1}{2}}\pmod{p}$.

Here is a list of almost 200 proofs of Theorem~\ref{thm:recip}:
\begin{verbatim}
    http://www.rzuser.uni-heidelberg.de/~hb3/rchrono.html
\end{verbatim}

\section{A Lemma of Gauss}
The proof we will give of Theorem~\ref{thm:recip} was first discovered
by Gauss, though not when he was~$19$.  This proof is given in many
elementary number theory texts (including Davenport).  It depends on
the following lemma of Gauss:

\begin{lemma}
  Let~$p$ be an odd prime and let~$a$ be an integer $\not\con 0\pmod{p}$.
  Form the numbers
  $$
    a,\, 2a,\, 3a,\, \ldots,\, \frac{p-1}{2} a
  $$
  and reduce them modulo~$p$ to lie in the interval
  $(-\frac{p}{2},\,\, \frac{p}{2})$.
  Let $\nu$ be the number of negative numbers in the resulting set.
  Then
  $$
    \kr{a}{p} = (-1)^{\nu}.
  $$
\end{lemma}
\begin{proof}
  In defining $\nu$, we expressed each number in
  $$
    S = \left\{a, 2a, \ldots, \frac{p-1}{2} a\right\}
  $$
  as congruent to a number in the set
  $$
    \left\{ 1, -1, 2, -2, \ldots, \frac{p-1}{2}, -\frac{p-1}{2}\right\}.
  $$
  No number
  $1, 2, \ldots \frac{p-1}{2}$
  appears more than once, with either choice of sign, because if it
  did then either two elements of~$S$ are congruent modulo~$p$ or
  $0$ is the sum of two elements of~$S$, and both events are impossible.
  Thus the resulting set must be of the form
  $$T = \left\{\eps_1\cdot 1, \eps_2 \cdot 2, \ldots,
    \eps_{(p-1)/2}\cdot \frac{p-1}{2} \right\},$$
  where each $\eps_i$ is either $+1$ or $-1$.  Multiplying together
  the elements of~$S$ and of~$T$, we see that
  $$
    (1a) \cdot (2a)\cdot(3a)\cdot \cdots \cdot  \left(\frac{p-1}{2} a\right)
    \con
    (\eps_1\cdot 1)\cdot (\eps_2 \cdot 2) \cdots
    \left(\eps_{(p-1)/2}  \cdot \frac{p-1}{2}\right)\pmod{p},
  $$
  so
  $$
    a^{(p-1)/2} \con \eps_1\cdot \eps_2\cdot \cdots \cdot \eps_{(p-1)/2}\pmod{p}.$$
  The lemma then follows from Proposition~\ref{prop:euler}.
\end{proof}






\chapter{Quadratic Reciprocity II}



\begin{center}
  {\sc\large In-class midterm this Wednesday, October 17!}\vspace{-2ex}
\end{center}
{\sl Monday's lecture will be a review lecture; Grigor's review session is on
Monday at 4pm; I will have an extra office hour in SC 515, Tuesday,
2:35--3:30.}

\section{Recall Gauss's Lemma}
We proved the following lemma in the previous lecture.
\begin{lemma}
  Let~$p$ be an odd prime and~$a$ an integer with $p\nmid a$.
  Form the numbers
  $
    a,\, 2a,\, 3a,\, \ldots,\, \frac{p-1}{2} a
  $
  and reduce them modulo~$p$ to lie in the interval
  $(-\frac{p}{2},\,\, \frac{p}{2})$.
  Let $\nu$ be the number of negative numbers in the resulting set.
  Then
  $
    \kr{a}{p} = (-1)^{\nu}.
  $
\end{lemma}


\section{Euler's Conjecture}
\begin{lemma}\label{lem:even}
  Let $a, b\in\Q$.  Then for any $n\in\Z$,
  $$\#\left((a,b)\intersect \Z\right)
    \con \#\left((a,b+2n)\intersect \Z\right)
    \con \#\left((a+2n,b)\intersect \Z\right)
    \pmod{2}.$$
\end{lemma}
\begin{proof}
  If $n>0$, then
  $$(a,b+2n) = (a,b) \union [b,b+2n),$$
  where the union is disjoint.  Let $[x]$ denote the least integer
  $\geq x$.  There are $2n$ integers,
  $$[b], [b]+1, \ldots, [b]+2n-1,$$
  in the interval $[b,b+2n)$, so
  the assertion of the lemma is true in this case.
  We also have
  $$(a,b-2n) = (a,b)\backslash [b-2n,b)$$
  and $[b-2n,b)$ also contains exactly $2n$ integers,
  so the lemma is also true when~$n$ is negative.
  The statement about $\#\left((a+2n,b)\intersect \Z\right)$
  is proved in a similar manner.
\end{proof}


The following proposition was first conjectured by Euler, based
on extensive numerical evidence.   Once we've proved this proposition,
it will be easy to deduce the quadratic reciprocity law.
\begin{proposition}[Euler's Conjecture]\label{prop}
  Let~$p$ be an odd prime and~$a\in\N$ a natural number with $p\nmid a$.
  \begin{enumerate}
    \item The symbol $\kr{a}{p}$ depends only on~$p$ modulo~$4a$.
    \item If~$q$ is a prime with $q\con - p \pmod{4a}$,
          then $\kr{a}{p} = \kr{a}{q}$.
  \end{enumerate}
\end{proposition}
\begin{proof}
  To apply Gauss's lemma, we have to compute the parity of the intersection
  of
  $$S = \left\{a, 2a, 3a, \ldots \frac{p-1}{2}a\right\}$$
  and
  $$I = \left(\frac{1}{2}p,p\right) \union \left(\frac{3}{2}p, 2p\right)
    \union \cdots \union
    \left(\left(b-\frac{1}{2}\right)p,bp\right),$$
  where
  $b=\frac{1}{2}a$ or $\frac{1}{2}(a-1)$, whichever is an integer.
    {\small (Why? We have to check that every element of $S$ that reduces
      to something in the interval $(-\frac{p}{2},0)$ lies in~$I$.
      This is clear if $b=\frac{1}{2}a < \frac{p-1}{2}a$.  If
      $b=\frac{1}{2}(a-1)$, then $bp+\frac{p}{2} > \frac{p-1}{2}a$,
      so $((b-\frac{1}{2})p,bp)$ is the last interval that could contain
      an element of of $S$ that reduces to $(-\frac{p}{2},0)$.)
      Also note that the integer endpoints of~$I$ are not in~$S$, since
      those endpoints are divisible by~$p$, but no element of~$S$ is
      divisible by~$p$.}

  Dividing~$I$ through by~$a$, we see that
  $$\#(S\intersect I) = \#\left(\Z \intersect \frac{1}{a}I\right),$$
  where
  $$\frac{1}{a} I =
    \left(\left(\frac{p}{2a},\frac{p}{a}\right)
    \union
    \left(\frac{3p}{2a},\frac{2p}{a}\right)
    \union
    \cdots
    \union
    \left(\frac{(2b-1)p}{2a},\frac{bp}{a}\right)
    \right).$$

  Write $p=4ac+r$, and let
  $$
    J = \left(\left(\frac{r}{2a},\frac{r}{a}\right)
    \union
    \left(\frac{3r}{2a},\frac{2r}{a}\right)
    \union
    \cdots
    \union
    \left(\frac{(2b-1)r}{2a},\frac{br}{a}\right)
    \right).
  $$
  The only difference between~$I$ and~$J$ is that the endpoints of
  intervals are changed by addition of an even integer.
  By Lemma~\ref{lem:even},
  $$\nu=\#\left(\Z \intersect \frac{1}{a}I\right) \con \#(\Z\intersect J)\pmod{2}.$$
  Thus $\kr{a}{p}=(-1)^\nu$ depends only on~$r$,
  i.e., only on~$p$ modulo~$4a$.  {\large\sc WOW!}

  If $q\con -p\pmod{4a}$, then the only change in the above computation
  is that~$r$ is replaced by~$4a-r$.  This changes $\frac{1}{a}I$ into
  $$K =
    \left(\left(2-\frac{r}{2a},4-\frac{r}{a}\right)
    \union
    \left(6-\frac{3r}{2a},8-\frac{2r}{a}\right)
    \union
    \cdots
    \union
    \left(4b-2-\frac{(2b-1)r}{2a},4b-\frac{br}{a}\right)
    \right).$$
  Thus $K$ is the same as $-\frac{1}{a}I$, except even integers
  have been added to the endpoints.  By Lemma~\ref{lem:even},
  $$\#(K\intersect \Z)\con \#\left(\left(\frac{1}{a}I\right)\intersect\Z\right)\pmod{2},$$
  so $\kr{a}{p} = \kr{a}{q}$, which completes the proof.
\end{proof}

The following more careful analysis in the special case when $a=2$
helps illustrate the proof of the above lemma, and is frequently
useful in computations.
\begin{proposition}\label{prop:p2}
  Let~$p$ be an odd prime.  Then
  $$
    \kr{2}{p} = \begin{cases} \hfill1 & \text{ if } p\con \pm 1\pmod{8} \\
              -1      & \text{ if } p\con \pm 3\pmod{8}
    \end{cases}.
  $$
\end{proposition}
\begin{proof}
  When $a=2$, the set $S = \{a,2a,\ldots,2\cdot\frac{p-1}{2}\}$ is
  $$
    \{ 2, 4, 6, \ldots, p-1 \}.
  $$
  We must count the parity of the number of elements of~$S$
  that lie in the interval $I=(\frac{p}{2}, p)$.
  Writing $p=8c+r$, we have
  \begin{align*}
    \#\left(I\intersect S\right) & =\#\left(\frac{1}{2}I \intersect \Z\right)
    =\#\left(\left(\frac{p}{4},\frac{p}{2}\right)\intersect \Z\right)                                       \\
                                 & =\#\left(\left(2c+\frac{r}{4}, 4c+\frac{r}{2}\right)\intersect \Z\right)
    \con \#\left(\left(\frac{r}{4}, \frac{r}{2}\right)\intersect \Z\right)
    \pmod{2},
  \end{align*}
  where the last equality comes from Lemma~\ref{lem:even}.
  The possibilities for~$r$ are $1,3,5,7$.  When $r=1$, the cardinality
  is~$0$, when $r=3, 5$ it is $1$, and when $r=7$ it is $2$.
\end{proof}


\section{The Quadratic Reciprocity Law}
With the lemma in hand, it is straightforward to deduce the quadratic
reciprocity law.
\begin{theorem}[Gauss]\label{thm:recip}
  Suppose that~$p$ and~$q$ are distinct odd primes.  Then
  $$\kr{p}{q}\cdot \kr{q}{p} = (-1)^{\frac{p-1}{2}\cdot \frac{q-1}{2}}.$$
\end{theorem}
\begin{proof}
  First suppose that $p\con q\pmod{4}$.  By swapping~$p$ and~$q$ if
  necessary, we may assume that $p>q$, and write $p-q=4a$.
  Since $p=4a+q$,
  $$\kr{p}{q} = \kr{4a+q}{q} = \kr{4a}{q} = \kr{a}{q},$$
  and
  $$\kr{q}{p} = \kr{p-4a}{p} = \kr{-4a}{p} = \kr{-1}{p}\cdot \kr{a}{p}.$$
  Proposition~\ref{prop} implies that
  $\kr{a}{q} = \kr{a}{p}$, since $p\con q\pmod{4a}$.  Thus
  $$\kr{p}{q}\cdot\kr{q}{p} = \kr{-1}{p} = (-1)^{\frac{p-1}{2}}
    = (-1)^{\frac{p-1}{2}\cdot \frac{q-1}{2}},$$
  where the last equality is because $\frac{p-1}{2}$ is even
  if and only if $\frac{q-1}{2}$ is even.

  Next suppose that $p\not\con q\pmod{4}$, so $p\con -q\pmod{4}$.
  Write $p+q=4a$.  We have
  $$\kr{p}{q} = \kr{4a-q}{q} = \kr{a}{q},
    \quad\text{ and }\quad
    \kr{q}{p} = \kr{4a-p}{p} = \kr{a}{p}.$$
  Since $p\con -q\pmod{4a}$, Proposition~\ref{prop}
  implies that $\kr{p}{q}=\kr{q}{p}$.
  Since $(-1)^{\frac{p-1}{2}\cdot \frac{q-1}{2}}=1$, the
  proof is complete.
\end{proof}



\subsection{Examples}
\begin{example}
  Is~$6$ a square modulo~$389$?
  We have
  $$\kr{6}{389} = \kr{2\cdot 3}{389}
    =\kr{2}{389}\cdot \kr{3}{389}
    = (-1)\cdot(-1)= 1.$$
  Here, we found that $\kr{2}{389} = -1$ using Proposition~\ref{prop:p2}
  and that $389\con 3\pmod{8}$.  We found $\kr{3}{389}$ as follows:
  $$\kr{3}{389} = \kr{389}{3} = \kr{2}{3}=-1.$$ Thus $6$ is a square
  modulo~$389$.

  Annoyingly, though we know that~$6$ is a square modulo~$389$, we still
  don't know an~$x$ such that $x^2\con 6\pmod{389}$!
  \begin{verbatim}
? for(a=1,388,if(Mod(a,389)^2==6,print1(a, " ")))
28 361
\end{verbatim}
\end{example}



\begin{example}
  Is~$3$ a square modulo $p=726377359$?  We proved that the answer is ``no''
  in the previous lecture by computing $3^{p-1}\pmod{p}$.  It's easier to
  prove that the answer is no using Theorem~\ref{thm:recip}:
  $$
    \kr{3}{726377359}
    = (-1)^{1\cdot \frac{726377358}{2}}\cdot \kr{726377359}{3}
    = -\kr{1}{3} = -1.
  $$
\end{example}



\section{Some Homework Hints}
Spend time studying for the midterm in addition to doing the homework.
To point you in the right direction on the homework problems, here
are some hints.
\begin{subprob}
  \item Use the quadratic reciprocity law, just like in the above examples.
  \item Use the quadratic reciprocity law.
  \item Relate the statement for $n=3$ to the statement for $n>3$.
  \item Write down an element of $(\Z/p^2\Z)^*$ that looks like it might
  have order~$p$, and prove that it does.  Recall that if $a, b$
  have orders $n, m$, with $\gcd(n,m)=1$, then $ab$ has order $nm$.
  \item
  \item
  \item Replace $\sum\kr{a}{p}$ by $\sum \kr{ab}{p}$ and use that
  $\kr{ab}{p}=\kr{a}{p}\cdot \kr{b}{p}$.
  \item Write a little program.
\end{subprob}







\chapter{The Midterm Exam}

Today I will briefly describe some key ideas that we've covered in
this course up until now.  Make sure you understand these, so you can
do well on the midterm, which is Wednesday, October 17, and is worth
20\% of your grade.

\section{Some Basic Definitions}
\hd{Greatest common divisor:}
$$\gcd(a,b) = \max\{ d : d \mid a \text{ and } d \mid b\}$$

\hd{Congruence:}
$a\con b\pmod{n}$ means that $n\mid a-b$.
\begin{example}
  We have $7\con -19\pmod{13}$ since $13\mid 7-(-19)=26$.
\end{example}

If $a$ is an integer such that $\gcd(a,n)=1$, then the order
of~$a$ modulo~$n$ is
$$\min\left\{ i\in\N  \,:\, a^i \con 1\pmod{n}\right\}.$$
For example, the order of~$2$ modulo $15$ is~$4$.

\hd{Some Rings and Groups:}
We let $\Z/n\Z$ denote the ring of equivalence classes of integers
modulo~$n$.  We also frequently consider the group
$$(\Z/n\Z)^* = \left\{ a \,:\, 1\leq a \leq n \text{ and }\gcd(a,n)=1\right\}.$$
The order of $a$ modulo~$n$ is then the order of the image of~$a$
in the multiplicative group $(\Z/n\Z)^*$.

%A quote: ``The hardest thing in number theory is finite abelian groups.''

\section{Equations Modulo~$n$}
\subsection{Linear Equations}
The equation $ax\con b\pmod{n}$ must have a solution if $\gcd(a,n)=1$.
  {\em Warning:} It might still have a solution even if $\gcd(a,n)\neq 1$.
\begin{example}
  The equation $3x\con 2\pmod{5}$ has the solution $x=4$.\\
  The equation $3x\con 9\pmod{18}$ has a solution $x=3$ even though
  $\gcd(3,18)=3\neq 1$.
\end{example}

\subsection{Quadratic Equations}
Suppose~$a$ is an integer that is not divisible by~$p$.  The
solvability or nonsolvability of the quadratic equation $x^2\con
  a\pmod{p}$ is addressed by quadratic reciprocity.  (So far we have
not discussed how to find a solution, only whether or not one exists.)
The quadratic residue symbol is
$$
  \kr{a}{p} = \begin{cases} +1 & \text{ if $x^2\con a\pmod{p}$ has a solution } \\
              -1 & \text{ otherwise}.
  \end{cases}
$$
We have
$$\kr{a}{p} \con a^{(p-1)/2} \pmod{p}.$$
The {\bf Quadratic Reciprocity Law}, which was proved by Gauss, asserts that
if~$p$ and~$q$ are distinct odd primes then
$$
  \kr{p}{q} = (-1)^{\frac{p-1}{2}\cdot\frac{q-1}{2}}\kr{q}{p}.
$$
This is the deepest result that we've proved in the course so far.  On
the midterm, you will {\em not} be held responsible for understanding
the proof I gave last Friday.   However, you should know the statement of
the quadratic reciprocity law and have some practice applying it.

\section{Systems of Equations}
Suppose that $n$ and $m$ are coprime integers.  Then the
  {\bf Chinese Remainder Theorem} (CRT) asserts that the system of equations
\begin{align*}
  x & \con a \pmod{m}, \\
  x & \con b \pmod{n}
\end{align*}
has solutions.  (There is exactly one nonnegative solution $x<nm$.)
\begin{example}
  Because of CRT, I know that there is an~$x$ such that
  \begin{align*}
    x & \con 1\pmod{37}, \\
    x & \con 17\pmod{23}
  \end{align*}
  even though I am too lazy to find~$x$ right now.
\end{example}


\section{The Euler $\vphi$ Function}
Define a function $\vphi: \N \ra \N$ by
$$
  \vphi(n) = \#\{a : 1\leq a \leq  n \text{ and } \gcd(a,n) = 1\} = \#(\Z/n\Z)^*.
$$
Using the Chinese Remainder Theorem we proved that $\vphi$ is
a {\em multiplicative function}, i.e., if $m, n\in\N$ and $\gcd(m,n)=1$, then
$$\vphi(mn) = \vphi(m)\cdot \vphi(n).$$
Also, if $p$ is a prime
then $\ds \vphi(p^n) = p^n - \frac{p^n}{p} = p^n-p^{n-1}$.
\begin{example}
  $$\vphi(2^3\cdot 5^2) = \vphi(2^3)\cdot \vphi(5^2)
    = (2^3 - 2^2)\cdot (5^2-5) = 4\cdot 20 = 80.$$
\end{example}

\section{Public-key Cryptography}
\subsection{The Diffie-Hellman Key Exchange}
\begin{enumerate}
  \item Nikita chooses a prime~$p$ and a number~$g$
        that is a primitive root modulo~$p$.  She tells Michael both~$p$ and~$g$.
  \item Nikita secretely chooses a random number~$n$ and
        sends Michael $g^n\pmod{p}$.
  \item Michael secretely chooses a random number~$m$ and
        sends Nikita $g^m\pmod{p}$.
  \item The {\em secret key} is $s=g^{nm}\pmod{p}$.  Both
        Michael and Nikita can easily compute~$s$, but The Collective can't
        because of the difficulty of the ``discrete logarithm problem''.
\end{enumerate}


\subsection{The RSA Cryptosystem}
\begin{enumerate}
  \item Nikita creates her public key as follows:
        \begin{enumerate}
          \item She chooses two distinct large primes~$p$ and~$q$, then
                computes both $n=pq$ and $\vphi(n) = (p-1)(q-1)$.
          \item She picks a random natural number $e<\vphi(n)$ such
                that $\gcd(e,\vphi(n))=1$.
          \item She computes a number $d$ such that $ed\con 1\pmod{\vphi(n)}$.
          \item Her public key is $(n,e)$.   (And her private decoding key is $d$.)
        \end{enumerate}
  \item To send Nikita a message, Michael encodes it (or a piece of it)
        as a number $m\pmod{n}$.  He then sends $m^e\pmod{n}$ to Nikita.
  \item Nikita recovers $m$ from $m^e\pmod{n}$ by using that
        $$m \con (m^e)^d\pmod{n}.$$
\end{enumerate}

\section{Important Algorithms}
\subsection{Euclid's Algorithm}
Given integers $a$ and $b$, a slight extension of Euclid's gcd
algorithm enables us to find integers $x$ and $y$ such that
$$
  ax + by = \gcd(a,b).
$$
\begin{example}
  $a=12$, $b=101$.
  \begin{align*}
    \ul{101} & =8\cdot \ul{12} + \ul{5} & \ul{5} & =\ul{101}-8\cdot \ul{12}                                      \\
    \ul{12}  & =2\cdot\ul{5} + \ul{2}   & \ul{2} & = -2\cdot \ul{101} + 17\cdot \ul{12}                          \\
    \ul{5}   & =2\cdot \ul{2}+ \ul{1}   & \ul{1} & = \ul{5} - 2\cdot \ul{2} = 5\cdot \ul{101} - 42\cdot \ul{12}.
  \end{align*}
  Thus $x=-42$, $y=5$ works, and $\gcd(a,b)=1$.

  We can use the result of this computation to solve
  $$12x\con 1\pmod{101}.$$
  Indeed, $1=(-42)\cdot 12 + 5\cdot 101$, so
  $x=-42$ is a solution.
\end{example}

\subsection{Powering Algorithm}
There is a clever trick that makes computing $a^n$ easier.
Write $n$ in binary, that is is write $n=\sum_{i=0}^r \eps_i 2^i$
with $\eps_i \in \{0,1\}$.
Then
$$a^n \,\,=\, \prod_{\text{$i$ with }\eps_i\neq 0} a^{2^i}.$$

\subsection{PARI}
The midterm will {\bf NOT} test knowledge of PARI.


\section{The Midterm Exam}
The students had 52 minutes to do all of these
problems with no external aids such as a calculator
or notes.
\begin{problems}

  \item (5 points) Prove that a positive number~$n$ is divisible by~$11$ if and only
  if the alternating sum of the digits of~$n$ is divisible by~$11$.

  \item Let $\vphi:\N\ra\N$ be the Euler~$\vphi$ function.
  \begin{subprob}
    \item (3 points) Find all natural numbers~$n$ such that $\vphi(n)=1$.
    \item (2 points) Do there exist natural numbers~$m$ and~$n$ such that
    $\vphi(mn)\neq \vphi(m)\cdot \vphi(n)$?
  \end{subprob}


  \item  (6 points) Nikita and Michael decide to agree on a secret encryption
  key using the Diffie-Hellman key exchange protocol.  You observe
  the following:
  \begin{itemize}
    \item Nikita chooses $p=13$ for the modulus and $g=2$ as generator.
    \item Nikita sends $6$ to Michael.
    \item Michael sends $11$ to Nikita.
  \end{itemize}
  What is the secret key?

  \item Consider the RSA public-key cryptosystem defined by
  $(n,e) = (77,7)$.
  \begin{subprob}
    \item (3 points) Encrypt the number~$4$ using this cryptosystem.
    \item (3 points) Find an integer~$d$ such that $ed\con 1\pmod{\vphi(n)}$.
  \end{subprob}


  %\item Find a primitive root modulo~$17$.  

  \item (5 points) How many natural numbers $x < 2^{13}$ satisfy the equation
  $$x^2\con 5\pmod{2^{13}-1}?$$
  (You may assume that $2^{13}-1$ is prime.)


  \item (10 points) Which of the following systems of equations have at least one
  solution?  Briefly justify your answers.
  \begin{center}
    \begin{tabular}{llll}
      (a)                                    &
      \begin{minipage}{10em}
        $x \con \,\,\,\,\,1 \pmod{3}$\\
        $x \con -1 \pmod{9}$
      \end{minipage}
                                             &
      (b)                                    &
      $2x\con 1\pmod{1234567891011121314151}$         \\

                                             &   &  & \\

      (c)                                    &
      \begin{minipage}{10em}
        $x^2 \con 5 \pmod{29}$\\
        $x^2  \con 3 \pmod{47}$
      \end{minipage} &
      (d)                                    &
      \begin{minipage}{10em}
        $x \con 3 \pmod{29}$\\
        $x  \con 5 \pmod{47}$
      \end{minipage}              \\
                                             &   &  & \\

      (e)                                    &
      \begin{minipage}{10em}
        $x^2 \con 3 \pmod{29}$\\
        $x^2  \con 5 \pmod{47}.$
      \end{minipage}
    \end{tabular}
  \end{center}

  \item (5 points) Find the natural number $x<97$ such that $x\con 4^{48}\pmod{97}$.
  (You may assume that $97$ is prime.)


\end{problems}
\newpage

\section{Abbreviated Solutions}
\begin{enumerate}
  \item We use that $10\con -1\pmod{11}$ and facts about modular arithmetic.
        Write $n = \sum_{i=0}^r d_i 10^i$. Then
        $n \con \sum_{i=0}^r (-1)^i d_i\pmod{11}$, so $n\con 0\pmod{11}$ if and
        only if the alternating sum of the digits $\sum (-1)^i d_i$ is congruent
        to $0$ modulo~$11$.
  \item For the first part, the  answer is $\mathbf{n=1,2}$.  On a
        previous homework we proved that $n=1,2$ are the only~$n$ such that
        $\vphi(n)$ is odd.  For the second part, the fact that $\vphi$ is
        multiplicative means that if $\gcd(m,n)=1$ then
        $\vphi(mn)=\vphi(m)\cdot\vphi(n)$.  When $\gcd(m,n)\neq 1$ this implication
        can fail; for example,
        $$
          2 = \vphi(2\cdot 2)\neq \vphi(2)\cdot
          \vphi(2)=1.
        $$
  \item Since $2^n\con 6\pmod{13}$, a table of powers of $2$ modulo~$13$
        quickly reveals that~$n$ must be~$5$ (we solve the discrete log problem
        easily in this case since $13$ is so small).  Likewise, since
        $g^m\con 11\pmod{13}$ we see that $m=7$.  The secret key is $\mathbf{s=7}$
        since $g^{nm}=2^{35} \con 2^{11} \con 7\pmod{13}.$
        (Some people who attempted this problem incorrectly thought the secret key
        should be $g^n\cdot g^m = g^{n+m}$.)
  \item
        \begin{enumerate}
          \item We must compute $4^7\pmod{77}$.  Working modulo~$77$, we have that
                $$4^7 = 64\cdot 64 \cdot 4 = 13^2\cdot 4 = 169\cdot 4 = 15\cdot 4 = 60,$$
                so $4$ encrypts as {\bf 60}.
          \item
                First, $\vphi(n) = \vphi(77)=\vphi(7)\cdot\vphi(11)=6\cdot 10=60$.
                (Some people incorrectly thought that $\vphi(n)=77$ for some reason.)
                We then use the extended Euclidean algorithm to
                find an integer~$e$ such that $7e\con 1\pmod{60}$. We find that
                $2\cdot 60 -17\cdot 7=1$, so $\mathbf{e=-17}$ is a solution.
        \end{enumerate}
  \item First we use the law of quadratic reciprocity to decide whether
        or not there is a solution.  We have
        $$
          \kr{5}{2^{13}-1} = (-1)^{2\cdot (2^{13}-2)/2}\kr{2^{13}-1}{5}
          = \kr{1}{5} = 1,
        $$
        so the equation $x^2\con 5\pmod{2^{13}-1}$ has at least one solution~$a$.
        Since the polynomial $x^2- 5$ has degree two and $2^{13}-1$ is prime,
        there are at most~$2$ solutions. Since $-a$ is also a solution and $a\neq 0$,
        there are {\bf exactly two solutions}.
  \item (a) has {\bf no solutions} because $x\con -1\pmod{9}$ implies that
        $x\con -1\pmod{3}$.  (b) has {\bf a solution} because
        $\gcd(2,1234567891011121314151)=1$.
        (c) has {\bf a solution} because $\kr{5}{29}=\kr{3}{47}=1$ so there are
        $a$ and $b$ such that $a^2\con 5\pmod{29}$ and $b^2\con 3\pmod{47}$;
        the Chinese Remainder Theorem then implies that there is an~$x$ such
        that $x\con a\pmod{29}$ and $x\con b\pmod{47}$.  (d) has {\bf a solution}
        by the Chinese Remainder Theorem, since $\gcd(29,47)=1$.
        (e) has {\bf no solution}
        since $\kr{3}{29}=-1$, so the first of the two equations
        doesn't even have a solution.
  \item Since $97$ is prime, Fermat's Little Theorem implies that
        $4^{48} = 2^{96} \con \mathbf{1}\pmod{97}.$

\end{enumerate}







\chapter{Programming in PARI, II}
\newcommand{\bs}{\mbox{$\backslash$}}



\section{Beyond One Liners}
In today's relaxing but decidely non-mathematical lecture, you will
learn a few new PARI programming commands.  Feel free to try out
variations of the examples below (especially because there is no
homework due this coming Wednesday).  Also, given that you know PARI
fairly well by now, ask me questions during today's lecture!
\subsection{Reading Files}
The {\tt \bs{}r} command allows you to read in a file.
\begin{example}
  Create a file {\tt pm.gp} that contains the following lines
  \begin{verbatim}
   {powermod(a, p, n) = 
       return (lift(Mod(a,p)^n));}
\end{verbatim}
  Now use {\tt \bs{}r} to load this little program into PARI:

  \begin{verbatim}
> ?powermod
  ***   powermod: unknown identifier.
> \rpm               \\ \rpm.gp would do the same thing
? ?powermod
powermod(a, p, n) = return(lift(Mod(a,p)^n));
? powermod(2,101,7)
%1 = 27
\end{verbatim}
  If we change {\tt pm.gp}, just type {\tt \bs{}r} to reload
  it (omitting the file name reloads the last file loaded).
  For example, suppose we change
  {\tt return (lift(Mod(a,p)\^{}n))} in {\tt pm.gp} to
  {\tt return (lift(Mod(a,p)\^{}n)-p)}.  Then
  \begin{verbatim}
? \r
? powermod(2,101,7)
%2 = -74
\end{verbatim}
\end{example}



\subsection{Arguments}
PARI functions can have several arguments.  For
example,
\begin{verbatim}
{add(a, b, c)=
   return (a + b + c);}
? add(1,2,3)
%3 = 6
\end{verbatim}
If you leave off arguments, they are set equal to $0$.
\begin{verbatim}
? add(1,2)
%4 = 3
\end{verbatim}
If you want the left-off arguments to default to something
else, include that information in the declaration of the function:
\begin{verbatim}
{add(a, b=-1, c=2)=
   return (a + b + c);}
? add(1,2)
%6 = 5
? add(1)
%7 = 2
? add(1,2,3)
%8 = 6
\end{verbatim}

\subsection{Local Variables Done Right}
Amidst the haste of a previous lecture, I mentioned that an unused
argument can be used as a poor man's local variable.   The following
example illustrates the right way to declare local variables in PARI.

\begin{example}
  The function {\tt verybad} below sums the integers $1, 2,\ldots n$
  whilst wreaking havoc on the variable {\tt i}.
  \begin{verbatim}
{verybad(n)=
   i=0;
   for(j=1,n, i=i+j);
   return(i);}
? verybad(3)
%9 = 6
? i=4;
? verybad(3);
? i                        
%13 = 6                    \\ ouch!! what have you done to my eye!
\end{verbatim}
  The function {\tt poormans} is better, but it uses a cheap hack to
  simulate a local variable.
  \begin{verbatim}
{poormans(n,  i=0)=   
   for(j=1,n, i=i+j);
   return(i);}
? i=4;
? poormans(3)
%16 = 6
? i
%17 = 4              \\ good
\end{verbatim}
  The following function is the best, because i is local and it's
  clearly declared as such.
  \begin{verbatim}
{best(n)=
   local(i);
   i=0;  for(j=1,n, i=i+j);
   return(i);}
? i=4;
? best(3)
%18 = 6
? i
%19 = 4
\end{verbatim}
\end{example}




\subsection{Making Your Program Listen}
The {\tt input} command reads a PARI expression from the keyboard.
The expression is evaluated and the result returned to your program.
This behavior is at first disconcerting if, like me, you naively
expect {\tt input} to return a string.
Here are some examples to illustrate the {\tt input} command:
\begin{verbatim}
? ?input
input(): read an expression from the input file or standard input.
? s = input();
1+1
? s                  \\ s is not the string "1+1", as you might expect
%24 = 2         
? s=input()
hi there
%25 = hithere
? type(s)            \\ PARI views s as a polynomial in the variable hithere
%26 = "t_POL"
? s=input()          
"hi there"
%27 = "hi there"
? type(s)            \\ now it's a string
%28 = "t_STR"
\end{verbatim}



\subsection{Writing to Files}
Use the {\tt write} command:

\begin{verbatim}
? ?write
write(filename,a): write the string expression a to filename.
? write("testfile", "Hello Kitty!")
\end{verbatim}
The {\tt write} command above appended the
line ``Hello Kitty!'' to the last line of {\tt testfile}.
This is useful if, e.g., you want to save key bits of work during
a session or in a function.   There is also a {\bf logging facility}
in PARI, which records most of what you type and PARI outputs to
the file {\tt pari.log}.
\begin{verbatim}
? \l
   log = 1 (on)
? 2+2
%29 = 4
? \l
   log = 0 (off)
   [logfile was "pari.log"]
\end{verbatim}

\section{Coming Attractions}
The rest of this course is about continued fractions,
quadratic forms, and elliptic curves.  The following
illustrates some relevant PARI commands which will
help us to explore these mathematical objects.

\begin{verbatim}
? ?contfrac
contfrac(x,{b},{lmax}): continued fraction expansion of x ...
? contfrac(7/9)
%30 = [0, 1, 3, 2]
? contfrac(sqrt(2))
%31 = [1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, ...]
? ?qfbclassno
qfbclassno(x,{flag=0}): class number of discriminant x using Shanks's 
method by default. If (optional) flag is set to 1, use Euler products.
? qfbclassno(-15,1)  \\ ALWAYS use flag=1, since ``the authors were too
%32 = 2              \\ lazy to implement Shanks' method completely...''
? E=ellinit([0,1,1,-2,0]);
? P=[0,0];
? elladd(E,P,P)
%36 = [3, 5]
? elladd(E,P,[3,5])
%37 = [-11/9, 28/27]
? a=-11/9;b=28/27;          \\ this is an ``amazing'' point on the curve.
? b^2+b == a^3+a^2-2*a 
%38 = 1                  
\end{verbatim}








\chapter{Continued Fractions, I}




\section{Introduction}
A {\em continued fraction} is an expression of the form
$$a_0 + \frac{1}{a_1+\frac{1}{a_2+\frac{1}{a_3+\cdots}}}$$
which may or may not go on indefinitely.  We denote\footnote{{\em Warning:}
  This notation clashes with the notation
  used in Davenport.  Our notation is standard.}
the value of this continued fraction by
$$[a_0,a_1,a_2,\ldots].$$
The $a_n$ are called the {\em partial quotients}
of the continued fraction (we will see why at the end of this lecture).
Thus, e.g.,
$$[1,2] = 1+\frac{1}{2} = \frac{3}{2},$$
and
$$\frac{172}{51} = [3,2,1,2,6]=3+\frac{1}{2+\frac{1}{1+\frac{1}{2+\frac{1}{6}}}}.$$

Continued fractions have many applications, from the abstract to the concrete.
They give good rational approximations to irrational numbers, and
the have been used to understand why you can't tune a
piano perfectly.\footnote{See
{\tt http://www.research.att.com/\~{\mbox{}}njas/sequences/DUNNE/TEMPERAMENT.HTML}}
Continued fractions also suggest a sense in which~$e$ appears to
be ``less transcendental'' than~$\pi$.

There are many places to read about continued fractions, including
Chapter X of Hardy and Wright's {\em Intro. to the Theory of Numbers},
\S13.3 of Burton's {\em Elementary Number Theory}, Chapter IV of
Davenport, and Khintchine's {\em Continued Fractions}. The notes
you're reading right now draw primarily on Hardy and Wright, since
their exposition is very clear and to the point.  I found Davenport's
chapter IV uneccessarily tedious; I felt marched through a thick
jungle to see a beautiful river.

\section{Finite Continued Fractions}
\begin{definition}
  A {\em finite continued fraction} is an expression
  $$
    a_0 + \frac{1}{a_1+\frac{1}{a_2 + \cdots + \frac{1}{a_m},}}$$
  where each $a_n$ is a rational number and $a_n>0$ for all $n\geq 1$.
  If the $a_n$ are integers, we say that the continued fraction is
    {\em integral}.
\end{definition}
To get a feeling for continued fractions, observe that
\begin{align*}
  [a_0]           & = a_0,                                           \\
  [a_0, a_1]      & = a_0 + \frac{1}{a_1} = \frac{a_0 a_1 + 1}{a_1}, \\
  [a_0, a_1, a_2] & = a_0 + \frac{1}{a_1 + \frac{1}{a_2}}
  = \frac{a_0 a_1 a_2 + a_0 + a_2}{a_1 a_2 + 1}.
\end{align*}
Also,
\begin{align*}
  [a_0, a_1, \ldots ,a_{m-1}, a_m] & =
  [a_0, a_1, \ldots, a_{m-2}, a_{m-1} + \frac{1}{a_m}]                   \\
                                   & = a_0 + \frac{1}{[a_1,\ldots, a_m]} \\
                                   & = [a_0, [a_1,\ldots, a_m]].
\end{align*}
\subsection{Partial Convergents}
Fix a continued fraction $[a_0,\ldots,a_m]$.
\begin{definition}
  For $0\leq n\leq m$, the $n$th {\em convergent} of the
  continued fraction $[a_0,\ldots,a_m]$
  is $[a_0,\ldots, a_n]$.
\end{definition}
For each $n\geq -1$, define real numbers $p_n$ and $q_n$ as follows:
\begin{align*}
  p_{-1} & = 1, \quad                   & p_0 = a_0, \qquad & p_1 = a_1 p_0 + p_{-1} = a_1 a_0 + 1,
  \quad  & p_n = a_n p_{n-1} + p_{n-2},                                                             \\
  q_{-1} & = 0, \quad                   & q_0 = 1, \qquad   & q_1 = a_1 q_0 + q_{-1} = a_1,
  \quad  & q_n = a_n q_{n-1} + q_{n-2}.
\end{align*}

\begin{exercise}\footnote{Try to do this exercise, which
    is not part of the regular homework, before the next lecture.}
  Compute $p_n$ and $q_n$ for the continued fractions
  $[-3,1,1,1,1,3]$ and $[0,2,4,1,8,2]$.  Observe that the
  propositions below hold.
\end{exercise}


\begin{proposition}
  $\ds [a_0, \ldots, a_n] = \frac{p_n}{q_n}$
\end{proposition}
\begin{proof}
  We use induction.  We already verified the assertion when $n=0,1$.
  Suppose the proposition is true for all continued fractions of
  length $n-1$.  Then
  \begin{align*}
    [a_0,\ldots, a_n]
     & = [a_0,\ldots,a_{n-2}, a_{n-1} + \frac{1}{a_n}]                          \\
     & = \frac{\left( a_{n-1} + \frac{1}{a_n}\right) p_{n-2} + p_{n-3}}
    {\left( a_{n-1} + \frac{1}{a_n}\right) q_{n-2} + q_{n-3}}                   \\
     & = \frac{(a_{n-1}a_n +1)p_{n-2} + a_n p_{n-3}}
    {(a_{n-1}a_n +1)q_{n-2} + a_n q_{n-3}}                                      \\
     & = \frac{a_n(a_{n-1}p_{n-2} + p_{n-3}) + p_{n-2}}
    {a_n(a_{n-1}q_{n-2} + q_{n-3}) + q_{n-2}}                                   \\
     & = \frac{a_n p_{n-1} + p_{n-2}}{a_n q_{n-1} + q_{n-2}} = \frac{p_n}{q_n}.
  \end{align*}
\end{proof}

\begin{proposition}\label{prop:dets}
  For $n\leq m$,
  \begin{enumerate}
    \item
          the determinant of $\mtwo{p_n}{p_{n-1}}{q_n}{q_{n-1}}$ is
          $(-1)^{n-1}$; equivalently,
          $$\frac{p_n}{q_n} - \frac{p_{n-1}}{q_{n-1}} =
            (-1)^{n-1}\cdot\frac{1}{q_n q_{n-1}};$$
    \item
          the determinant of $\mtwo{p_n}{p_{n-2}}{q_n}{q_{n-2}}$ is
          $(-1)^{n}a_n$; equivalently,
          $$\frac{p_n}{q_n} - \frac{p_{n-2}}{q_{n-2}} =
            (-1)^{n}\cdot\frac{a_n}{q_n q_{n-2}}.$$
  \end{enumerate}
\end{proposition}
\begin{proof}
  For the first statement, we proceed by induction.  The case $n=0$
  holds because the determinant of $\mtwo{a_0}{1}{1}{0}$ is $-1=(-1)^{-1}$.
  Suppose the statement is true for $n-1$.  Then
  \begin{align*}
    p_{n}q_{n-1} - q_n p_{n-1} & =
    (a_n p_{n-1} + p_{n-2}) q_{n-1} - (a_n q_{n-1} + q_{n-2}) p_{n-1} \\
                               & = p_{n-2}q_{n-1} - q_{n-2} p_{m-1}   \\
                               & =
    -(p_{n-1}q_{n-2} - p_{n-2} q_{n-1})                               \\
                               & = -(-1)^{n-2} = (-1)^{n-1}.
  \end{align*}
  This completes the proof of the first statement.  For the second statement,
  \begin{align*}
    p_n q_{n-2} - p_{n-2} q_n & =
    (a_n p_{n-1} + p_{n-2})q_{n-2} - p_{n-2}(a_n q_{n-1} + q_{n-2})    \\
                              & = a_n(p_{n-1}q_{n-2} - p_{n-2}q_{n-1}) \\
                              & = (-1)^n a_n.
  \end{align*}
\end{proof}


\begin{corollary}
  The fraction $\ds \frac{p_n}{q_n}$ is in lowest terms.
\end{corollary}
\begin{proof}
  If $p\mid p_n$ and $p\mid q_n$ then $p\mid (-1)^{n-1}$.
\end{proof}

\subsection{How the Convergents Converge}
Let $[a_0,\ldots, a_m]$ be a continued fraction and for
$n\leq m$ let
$$
  c_n = [a_0, \ldots, a_n] = \frac{p_n}{q_n}
$$
denote the $n$th convergent.

\begin{proposition}
  The even convergents $c_{2n}$ increase strictly with~$n$, and the
  odd convergents $c_{2n+1}$ decrease strictly with~$n$.  Moreover,
  the odd convergents $c_{2n+1}$ are greater than all of the
  even convergents.
\end{proposition}
\begin{proof}
  For $n\geq 1$ the $a_n$ are positive, so the $q_n$ are all positive.
  By Proposition~\ref{prop:dets}, for $n\geq 2$,
  $$c_n - c_{n-2} = (-1)^n \cdot \frac{a_n}{q_n q_{n-2} },$$
  which proves the first claim.

  Next, Proposition~\ref{prop:dets} implies that for $n\geq 1$,
  $$
    c_n - c_{n-1} = (-1)^{n-1}\cdot \frac{1}{q_n q_{n-1}}
  $$
  has the sign of $(-1)^{n-1}$, so that
  $c_{2n+1} > c_{2n}$.
  Thus if there exists $r, n$ such that
  $c_{2n+1} < c_{2r}$, then $r\neq n$.
  If $r<n$, then $c_{2n+1} < c_{2r} < c_{2n}$, a contradiction.
  If $r>n$, then $c_{2r+1} < c_{2n+1} < c_{2r}$, also a contradiction.
\end{proof}

\section{Every Rational Number is Represented}
\begin{proposition}
  Every rational number is represented by
  a continued fraction.
\end{proposition}
\begin{proof}
  Let $a/b$, where $b>0$, be any rational number.  Euclid's
  algorithm gives:
  \begin{align*}
    a       & = b\cdot a_0 + r_1,           & 0<r_1<b         \\
    b       & = r_1\cdot a_1 + r_2,         & 0<r_2<r_1       \\
            & \cdots                        &                 \\
    r_{n-2} & = r_{n-1}\cdot a_{n-1} + r_n, & 0<r_n < r_{n-1} \\
    r_{n-1} & = r_n\cdot a_n + 0.
  \end{align*}
  Note that $a_i>0$ for $i>0$.
  Rewrite the equations as follows:
  \begin{align*}
    a/b         & = a_0 + r_1/b = a_0 + 1/(b/r_1),       \\
    b/r_1       & = a_1 + r_2 / r_1 = a_1 + 1/(r_1/r_2), \\
    r_1/r_2     & = a_2 + r_3 / r_2 = a_2 + 1/(r_2/r_3), \\
    \cdots                                               \\
    r_{n-1}/r_n & = a_n.
  \end{align*}
  It follows that
  $$
    \frac{a}{b} = [a_0,a_1,\ldots a_n].
  $$


\end{proof}






list of good links:

http://mathforum.org/electronic.newsletter/mf.intnews2.44.html


\chapter{Continued Fractions II: Infinite Continued Fractions}

\section{The Continued Fraction Algorithm}
Let $x\in\R$ and write
$$x = a_0 + t_0$$
with $a_0\in\Z$ and $0\leq t_0 < 1$.  If $t_0\neq 0$, write
$$\frac{1}{t_0} = a_1 + t_1$$ with $a_1\in\N$ and $0\leq t_1 < 1$.
Thus $t_0 = \frac{1}{a_1 + t_1}=[0,a_1+t_1]$, which is a (nonintegral)
continued fraction expansion of $t_0$.  Continue in this manner
so long as $t_n\neq 0$ writing
$$\frac{1}{t_n} = a_{n+1} + t_{n+1}$$
with $a_{n+1}\in\N$ and $0\leq t_{n+1}<1$.
This process, which associates to a real number~$x$ the sequence
of integers $a_0, a_1, a_2, \ldots $, is called the
  {\em continued fraction algorithm}.

\begin{example}
  Let $x=\frac{8}{3}$.  Then $x=2+\frac{2}{3}$, so
  $a_0=2$ and $t_0=\frac{2}{3}$.  Then
  $\frac{1}{t_0}=\frac{3}{2} = 1+\frac{1}{2}$, so $a_1=1$ and $t_1=\frac{1}{2}$.
  Then $\frac{1}{t_1} = 2$, so $a_2=2$, $t_2=0$, and the sequence terminates.
  Notice that
  $$ \frac{8}{3} = [2,1,2],$$
  so the continued fraction algorithm produces the
  continued fraction of $\frac{8}{3}$.
\end{example}


\begin{proposition}\label{prop1}
  For every $n$ such that $a_n$ is defined, we have
  $$x = [a_0, a_1, \ldots, a_{n}+t_n],$$
  and if $t_{n}\neq 0$ then
  $
    x = [a_0, a_1, \ldots, a_{n}, \frac{1}{t_n}].
  $
\end{proposition}

\begin{proof}
  Use induction.  The statements are both true when $n=0$.
  If the second statement is true for $n-1$, then
  $$
    x = [a_0,a_1, \ldots, a_{n-1},\frac{1}{t_{n-1}}]
    =[a_0,a_1, \ldots, a_{n-1},a_n + t_n]
    =[a_0,a_1, \ldots, a_{n-1},a_n, \frac{1}{t_n}].$$
  Similarly, the first statement is true for~$n$ if
  it is true for $n-1$.

\end{proof}



\begin{example}
  Let $x = \frac{1+\sqrt{5}}{2}.$  Then
  $$
    x = 1 + \frac{-1 + \sqrt{5}}{2},
  $$
  so $a_0 = 1$ and $t_0 = \frac{-1+\sqrt{5}}{2}$.
  We have
  $$
    \frac{1}{t_0} = \frac{2}{-1+\sqrt{5}} = \frac{-2-2\sqrt{5}}{-4}
    = \frac{1+\sqrt{5}}{2}
  $$
  so again $a_1 = 1$ and $t_1 =  \frac{-1+\sqrt{5}}{2}$.
  Likewise, $a_n = 1$ for all~$n$.
  Does the following crazy-looking equality makes sense??
  $$\frac{1+\sqrt{5}}{2} =
    1 + \frac{1}{1 + \frac{1}{1 + \frac{1}{1 + \frac{1}{1 + \frac{1}{1 + \cdots}}}}}
  $$
\end{example}

\begin{example}
  Next suppose $x = e$.  Then
  $$a_0, a_1, a_2, \ldots = 2,1,2,1,1,4,1,1,6,1,1,8,1,1,\ldots$$
  \begin{verbatim}
? contfrac(exp(1))
%1 = [2, 1, 2, 1, 1, 4, 1, 1, 6, 1, 1, 8, 1, 1, 10, 1, 1, 
       12, 1, 1, 14, 1, 1, 16, 1, 1, 18, 1, 1, 20, 2]
? \\ to get more terms, increase the real precision:
? \p60
? contfrac(exp(1),[])
%12 = [2, 1, 2, 1, 1, 4, 1, 1, 6, 1, 1, 8, 1, 1, 10, 1, 1, 
   12, 1, 1, 14, 1, 1, 16, 1, 1, 18, 1, 1, 20, 1, 1, 22, 1, 
   1, 24, 1, 1, 26, 1, 1, 28, 1, 1, 30, 1, 1, 32, 1, 1, 34, 
   1, 1, 36, 1, 1, 38, 1, 1, 40, 1, 1, 42, 2]

\end{verbatim}
  The following program uses a proposition we proved yesterday to compute the partial convergents
  of a continued fraction:
  \begin{verbatim}
{convergents(v)=
    local(pp,qq,p,q,tp,tq,answer);
    pp=1; qq=0; p=v[1]; q=1;    \\ pp is p_{n-1} and p is p_n.
    answer = vector(length(v)); \\ put answer in this vector
    answer[1] = p/q;
    for(n=2,length(v),
       tp=p; tq=q; p=v[n]*p+pp; q=v[n]*q+qq; pp=tp; qq=tq;
       answer[n] = p/q;
    );
    return(answer);
}
\end{verbatim}

  Let's try this with $\pi$:
  \begin{verbatim}
? contfrac(Pi)
%26 = [3, 7, 15, 1, 292, 1, 1, ...]
? convergents([3,7,15])
%27 = [3, 22/7, 333/106]
? convergents([3,7,15,1,292])
%28 = [3, 22/7, 333/106, 355/113, 103993/33102]
? %[5]*1.0
%29 = 3.1415926530119026040...
? % - Pi
%30 = -0.000000000577890634...
\end{verbatim}
\end{example}


\section{Infinite Continued Fractions}
\begin{theorem}
  Let $a_0, a_1, a_2, \ldots $ be a sequence of integers such that $a_n > 0$ for
  all $n\geq 1$,
  and for each $n\geq 0$, set
  $c_n = [a_0, a_1, \ldots a_n].$
  Then $\ds\lim_{n\ra \infty} c_n$ exists.
\end{theorem}
\begin{proof}
  For any $m\geq n$, the number $c_n$ is a partial convergent of $[a_0, \ldots, a_m]$.
  Recall from the previous lecture that the even convergents $c_{2n}$ form a strictly
    {\em increasing} sequence and the odd convergents $c_{2n+1}$ form a strictly
    {\em decreasing} sequence. Moreover, the even convergents are all $\leq c_1$
  and the odd convergents are all $\geq c_0$.
  Hence $\alpha_0 = \lim_{n\ra \infty} c_{2n}$ and $\alpha_1 = \lim_{n\ra \infty} c_{2n+1}$
  both exist and $\alpha_0\leq \alpha_1$.
  Finally, by a proposition from last time
  $$| c_{2n} - c_{2n-1}|
    = \frac{1}{q_{2n}\cdot q_{2n-1}} \leq
    \frac{1}{2n(2n-1)} \ra 0,
  $$
  so $\alpha_0 = \alpha_1$.
\end{proof}

We define
$$
  [a_0, a_1, \ldots ] = \lim_{n\ra \infty} c_n.
$$

\begin{example}
  We use PARI to illustrate the convergence of the theorem for $x=\pi$.
  \begin{verbatim}
? a = contfrac(Pi)
%38 = [3, 7, 15, 1, 292, 1, 1, 1, 2, 1, 3, 1, 14, 2, 1, 1, 2, 2, ...]
? c = convergents(a)
%39 = [3, 22/7, 333/106, 355/113, 103993/33102, 104348/33215, ...]
? \p9                      \\ so we can see.
   realprecision = 9 significant digits
? [c[1]*1.0, c[3]*1.0, c[5]*1.0, c[7]*1.0]  \\ odd ones converge up to pi
%43 = [3.00000000, 3.14150943, 3.14159265, 3.14159265]
? [c[2]*1.0,c[4]*1.0,c[6]*1.0,c[8]*1.0]     \\ even ones swoop down on pi.
%44 = [3.14285714, 3.14159291, 3.14159265, 3.14159265]
\end{verbatim}
\end{example}

\begin{theorem}
  Let $x\in\R$ be a real number.  Then
  $$
    x = [a_0, a_1, a_2, \ldots],
  $$
  where $a_0, a_1, a_2,\ldots$ is the sequence produced by the continued fraction algorithm.
\end{theorem}
\begin{proof}
  If the sequence is finite then some $t_n=0$ and the
  result follows by Proposition~\ref{prop1}.
  Suppose the sequence is infinite.
  By Proposition~\ref{prop1},
  $$
    x = [a_0, a_1, \ldots, a_n, \frac{1}{t_n}].
  $$
  By a proposition from the last lecture\footnote{Which we apply in a case when
    the partial quotients of the continued fraction are not integers!},
  $$
    x = \frac{\frac{1}{t_n} p_n + p_{n-1}}{\frac{1}{t_n} q_n + q_{n-1}}.
  $$
  Thus if $c_n = [a_0, a_1, \ldots, a_n]$, then
  \begin{align*}
    x - c_n & = x - \frac{p_n}{q_n}                                                             \\
            & =\frac{\frac{1}{t_n} p_n q_n + p_{n-1} q_n - \frac{1}{t_n} p_n q_n - p_n q_{n-1}}
    {q_n \left(\frac{1}{t_n} q_n + q_{n-1}\right)}.                                             \\
            & = \frac{p_{n-1} q_n - p_{n}q_{n-1}}{q_n\left(\frac{1}{t_n} q_n + q_{n-1}\right)}  \\
            & = \frac{(-1)^n}{q_n\left(\frac{1}{t_n} q_n + q_{n-1}\right)}.
  \end{align*}
  Thus
  \begin{align*}
    |x - c_n| & = \frac{1}{q_n\left(\frac{1}{t_n} q_n + q_{n-1}\right)}    \\
              & < \frac{1}{q_n(a_{n+1} q_n + q_{n-1})}                     \\
              & = \frac{1}{q_n \cdot q_{n+1}} \leq \frac{1}{n(n+1)} \ra 0.
  \end{align*}
  (In the inequality we use that $a_{n+1}$ is the integer part of $\frac{1}{t_n}$, and
  is hence $\leq \frac{1}{t_n}$.)

\end{proof}





\begin{proposition}\label{prop}
  If~$x$ is a rational number then the sequence
  $a_0, a_1, a_2, \ldots $
  terminates (at~$n$ say) and
  $$x = [a_0, a_1, a_2, \ldots, a_n].$$
\end{proposition}
\begin{proof}
  Let $[b_0,b_1,\ldots, b_m]$ be the continued fraction representation
  of~$x$ that we obtain using the Euclidean algorithm.  Then
  $$
    x = b_0 + 1/[b_1,\ldots,b_m].
  $$
  If $[b_1,\ldots,b_m]=1$ then $m=1$ and $b_1=1$, which would never
  happen using the Euclidean algorithm since $x$ is expressed in lowest
  terms.  Thus $[b_1,\ldots,b_m]>1$, so in the continued fraction
  algorithm we choose $a_0 = b_0$ and $t_0 = 1/[b_1, \ldots, b_m]$.
  Repeating this argument enough times proves the claim.
\end{proof}



\chapter{Continued Fractions III: Quadratic Irrationals}



In this lecture we prove that the continued fraction expansion of
a number is periodic if and only if the number is a quadratic irrational.

\section{Quadratic Irrationals}
\begin{definition}
  An element $\alpha\in\R$ is a {\em quadratic irrational}
  if it is irrational and satisfies a quadratic polynomial.
\end{definition}
Thus, e.g., $(1+\sqrt{5})/2$ is a quadratic irrational.
Recall that
$$
  \frac{1+\sqrt{5}}{2} = [1,1,1,\ldots].
$$
The continued fraction of $\sqrt{2}$ is $[1,2,2,2,2,2,\ldots]$, and
the continued fraction of $\sqrt{389}$ is
$$
  [19,1,2,1, 1, 1, 1, 2, 1, 38, 1, 2, 1, 1, 1, 1, 2, 1, 38,\ldots].$$
Does the $[1,2,1, 1, 1, 1, 2, 1, 38]$ pattern repeat over and over again??

\section{Periodic Continued Fractions}
\begin{definition}
  A {\em periodic continued fraction} is a continued
  fraction $[a_0, a_1, \ldots, a_n, \ldots]$ such that
  $$
    a_n = a_{n+h}
  $$
  for a fixed positive integer~$h$ and all sufficiently large~$n$.
  We call~$h$ the {\em period} of the continued fraction.
\end{definition}

\begin{example}
  Consider the periodic continued fraction $[1,2,1,2,\ldots] = [\overline{1,2}]$.
  What does it converge to?
  $$[\overline{1,2}] = 1+\frac{1}{2+\frac{1}{1+\frac{1}{2+ \frac{1}{1+\cdots}}}},$$
  so if $\alpha=[\overline{1,2}]$ then
  $$
    \alpha = 1 + \frac{1}{2+\alpha}.
  $$
  Thus $2\alpha + \alpha^2 = 2 + \alpha + 1$, so
  $$
    \alpha^2 + \alpha - 3 = 0
    \quad\text{ and }\quad \alpha = \frac{-1+\sqrt{7}}{2}.
  $$
\end{example}

\begin{theorem}
  An infinite integral continued fraction is periodic if and only if
  it represents a quadratic irrational.
\end{theorem}
\begin{proof}
  \par\noindent($\Longrightarrow$) First suppose that
  $$[a_0, a_1, \ldots, a_n, \overline{a_{n+1},\ldots, a_{n+h}}]$$
  is a periodic continued fraction.  Set
  $\alpha=[a_{n+1},a_{n+2}, \ldots]$.  Then
  $$
    \alpha = [a_{n+1},\ldots, a_{n+h}, \alpha],
  $$
  so
  $$
    \alpha = \frac{\alpha p_{n+h} + p_{n+h-1}}{\alpha q_{n+h} + q_{n+h-1}}.
  $$
  (We use that~$\alpha$ is the last partial convergent.)
  Thus $\alpha$ satisfies a quadratic equation.  Since the $a_i$ are
  all integers, the number
  \begin{align*}
    [a_0, a_1, \ldots ] & = [a_0, a_1, \ldots, a_n, \alpha]                       \\
                        & = a_0 + \frac{1}{a_1 + \frac{1}{a_2 + \cdots + \alpha}}
  \end{align*}
  can be expressed as a polynomial in~$\alpha$ with
  rational coefficients, so
  $[a_0, a_1, \ldots]$ also satisfies a quadratic polynomial.
  Finally, $\alpha\not\in\Q$ because periodic continued fractions
  have infinitely many terms.
  \vspace{2ex}

  \par\noindent($\Longleftarrow$)
  This direction was first proved by Lagrange.  The proof
  is much more exciting!
  Suppose $\alpha\in\R$ satisfies a quadratic equation
  $$a \alpha^2 + b\alpha + c = 0$$
  with $a, b, c\in\Z$.
  Let $[a_0, a_1, \ldots]$
  be the expansion of~$\alpha$.  For each~$n$, let
  $$
    r_n = [a_n, a_{n+1}, \ldots],
  $$
  so that
  $$
    \alpha = [a_0, a_1, \ldots, a_{n-1}, r_n].
  $$
  We have
  $$
    \alpha = \frac{r_n p_n + p_{n-1}}{r_n q_n + q_{n-1}}.
  $$
  Substituting this expression for~$\alpha$ into the quadratic equation
  for $\alpha$, we see that
  $$A_n r_n^2 + B_n r_n + C_n = 0,$$
  where
  \begin{align*}
    A_n & = a p_{n-1}^2 + b p_{n-1} q_{n-1} + c q_{n-1}^2,                                  \\
    B_n & = 2a p_{n-1} p_{n-2} + b(p_{n-1} q_{n-2} + p_{n-2} q_{n-1}) + 2c q_{n-1} q_{n-2}, \\
    C_n & = a p_{n-2}^2 + b p_{n-2} q_{n-2} + c p_{n-2}^2.
  \end{align*}
  Note that $A_n, B_n, C_n\in\Z$, that $C_n = A_{n-1}$, and that
  $$
    B^2 - 4A_n C_n = (b^2- 4ac)(p_{n-1}q_{n-2} - q_{n-1}p_{n-2})^2 = b^2 - 4ac.
  $$

  Recall from the proof of Theorem~2.3 of the previous lecture that
  $$
    \left| \alpha - \frac{p_{n-1}}{q_{n-1}}\right|
    < \frac{1}{q_n q_{n-1}}.
  $$
  Thus
  $$
    \left| \alpha q_{n-1} - p_{n-1}\right| < \frac{1}{q_n} < \frac{1}{q_{n+1}},
  $$
  so
  $$
    p_{n-1} = \alpha q_{n-1} + \frac{\delta}{q_{n-1}}
    \qquad\text{with }|\delta| < 1.
  $$
  Hence
  \begin{align*}
    A_n & = a\left(\alpha q_{n-1} + \frac{\delta}{q_{n-1}}\right)^2
    +b\left(\alpha q_{n-1} + \frac{\delta}{q_{n-1}}\right)q_{n-1}
    +c q_{n-1}^2                                                    \\
        & = (a\alpha^2 + b\alpha + c)q_{n-1}^2 + 2a\alpha\delta +
    a\frac{\delta^2}{q_{n-1}^2} + b\delta                           \\
        & = 2a\alpha\delta + a\frac{\delta^2}{q_{n-1}^2} + b\delta.
  \end{align*}
  Thus
  $$
    |A_n| = \left|2a\alpha\delta + a\frac{\delta^2}{q_{n-1}^2} + b\delta\right|
    < 2|a\alpha| + |a| + |b|.
  $$
  Thus there are only finitely many possibilities for the integer $A_n$.
  Also,
  $$
    |C_n| = |A_{n-1}|
    \quad\text{ and }\quad
    |B_n| = \sqrt{b^2 - 4(ac-A_n C_n)},
  $$
  so there are only finitely many triples $(A_n, B_n, C_n)$,
  and hence only finitely many possibilities for $r_n$ as~$n$
  varies.  Thus for some~$h>0$,
  $$
    r_n = r_{n+h}.
  $$
  This shows that the continued fraction for~$\alpha$ is periodic.
\end{proof}

\section{What About Higher Degree?}
\begin{definition}
  An {\em algebraic number} is a root of a polynomial $f\in\Q[x]$.
\end{definition}

\hd{Open Problem:}\footnote{As far as I know this is still
  an open problem.}
What is the continued fraction expansion of the algebraic number $\sqrt[3]{2}$?
\begin{verbatim}
? contfrac(2^(1/3))
%5 = [1, 3, 1, 5, 1, 1, 4, 1, 1, 8, 1, 14, 1, 10, 2, 1, 4, 12, 2, 3,
 2, 1, 3, 4, 1, 1, 2, 14, 3, 12, 1, 15, 3, 1, 4, 534, 1, 1, 5, 1, 1, 
 121, 1, 2, 2, 4, 10, 3, 2, 2, 41, 1, 1, 1, 3, 7, 2, 2, 9, 4, 1, 3, 7, 
 6, 1, 1, 2, 2, 9, 3, 1, 1, 69, 4, 4, 5, 12, 1, 1, 5, 15, 1, 4, 1, 1, 
 1, 1, 1, 89, 1, 22, 186, 5, 2, 4, 3, 3, 1, \ldots]
\end{verbatim}
I sure don't see a pattern, and that $534$ strips me of any confidence
that I ever will.  One could at least try to analyze the first few terms
of the continued fraction statistically (see Lang and Trotter, 1972).

\vspace{2ex}
\hd{Khintchine (1963), page 59:}
\begin{quote}
  No properties of the representing continued fractions,
  analogous to those which have just been proved, are known
  for algebraic numbers of higher degree.  [...] It is of
  interest to point out that up till the present time no
  continued fraction development of an algebraic
  number of higher degree than the second is known.
  It is not even known if such a development  has bounded
  elements.  Generally speaking the problems associated with
  the continued fraction expansion of algebraic numbers of degree
  higher than the second are extremely difficult and virtually
  unstudied.
\end{quote}

\vspace{1ex}
\hd{Richard Guy {\em Unsolved Problems in Number Theory} (1994), page 260:}
\begin{quote}
  Is there an algebraic number of degree greater than two whose simple
  continued fraction has unbounded partial quotients?
  Does {\em every} such number have unbounded partial quotients?
\end{quote}




\chapter{Continued Fractions IV: Applications}



In this lecture we will learn about two applications of continued
fractions.  The first is a solution to the computational
problem of recognizing a rational number using a computer.  The second
application is to the following ancient question: Given a positive nonsquare
integer $d$, find {\em integers}~$x$ and~$y$ such that $x^2 - dy^2 = 1$.

\section{Recognizing Rational Numbers}
Suppose that you can compute approximations to a rational number using a
computer, and desparately want to know what the rational number is.
As Henri Cohen explains in his book {\em A Course in Computational
    Algebraic Number Theory}, continued fraction are very helpful.

\begin{quote}
  Consider the following apparently simple problem.  Let $x\in\R$ be
  given by an approximation (for example a decimal or binary one).
  Decide if $x$ is a rational number or not.  Of course, this question
  as posed does not really make sense, since an approximation is usually
  itself a rational number.  In practice however the question does make
  a lot of sense in many different contexts, and we can make it
  algorithmically more precise.  For example, assume that one has an
  algorithm which allows us to compute~$x$ to as many decimal places as
  one likes (this is usually the case).  Then, if one claims that~$x$ is
  (approximately) equal to a rational number $p/q$, this means that
  $p/q$ should still be extremely close to~$x$ whatever the number of
  decimals asked for, $p$ and $q$ being fixed.  This is still not
  completely rigorous, but it comes quite close to actual practice, so
  we will be content with this notion.

  Now how does one find $p$ and $q$ if $x$ is indeed a rational number?
  The standard (and algorithmically excellent) answer is to compute the
  continued fraction expansion $[a_0, a_1, \ldots ]$ of~$x$.
  The number~$x$ is rational if and only if its continued fraction expansion
  is finite, i.e., if and only if one of the $a_i$ is {\em infinite}.
  Since~$x$ is only given with the finite precision, $x$ we be considered
  rational if~$x$ has a {\em very} large partial quotient $a_i$ in its
  continued fraction expansion.
\end{quote}

The following example illustrates Cohen's remarks:
\begin{example}
  \begin{verbatim}
? x
%13 = 9495/3847
? x*1.0
%14 = 2.4681570054587990642058747075643358461138549519105
? contfrac(x)
%15 = [2, 2, 7, 2, 1, 5, 1, 1, 1, 1, 2]
? contfrac(2.468157005458799064)
%16 = [2, 2, 7, 2, 1, 5, 1, 1, 1, 1, 1, 1, 328210621945, 2, 1, 1, 1, 1, 7]
? contfracpnqn([2, 2, 7, 2, 1, 5, 1, 1, 1, 1, 1, 1])
%17 =
[9495 5852]
[3847 2371]
? contfrac(2.4681570054587990642058747075643)
%18 = [2, 2, 7, 2, 1, 5, 1, 1, 1, 1, 1, 1, 1885009518355562936415046, 1, 4]
? \p300
? x*1.0  \\ notice that no repeat is immediately evident in the digits of x
%19 = 2.468157005458799064205874707564335846113854951910579672472056147647517..
? \\ in fact, the length of the period of the decimal expansion 
  \\ of 1/3847 is 3846 (the order of 10 modulo 3847)!!
\end{verbatim}

\end{example}

\section{Pell's Equation}
In February of 1657, Pierre Fermat issued the following challenge:
\begin{quote}
  Given a positive integer~$d$, find a positive integer~$y$ such that
  $dy^2 + 1$ is a perfect square.
\end{quote}
In other words, find a solution to $x^2-dy^2 = 1$ with $y\in\N$.


Note Fermat's emphasis on {\em integer} solutions.  It is
easy to find rational solutions to the equation $x^2-dy^2 = 1$.
Simply divide the relation
$$
  (r^2+d)^2 - d(2r)^2 = (r^2-d)^2
$$
by $(r^2 - d)^2$ to arrive at
$$
  x = \frac{r^2+d}{r^2-d}, \qquad
  y = \frac{2r}{r^2-d}.
$$
Fermat said: ``Solutions in fractions, which can be given at
once from the merest elements of arithmetic, do not satisfy me.''

The equation $x^2 - dy^2=1$ is called {\bf Pell's equation}.  This is
because Euler (in about 1759) accidently called it ``Pell's equation''
and the name stuck, though Pell (1611--1685) had nothing
to do with it.

If~$d$ is a perfect square, $d=n^2$, then
$$
  (x+ny)(x-ny) = x^2-dy^2 = 1
$$
which implies that
$x+ny = x-ny = 1$, so
$$
  x = \frac{x+ny+x-ny}{2} = \frac{1+1}{2}=1.
$$
We will thus always assume that~$d$ is not a perfect square.
You can read about Pell's equation in Section 0.6 of Kato-Kurokawa-Saito
and on pages 107--111 of Davenport.  Pell's equation is
best understood in terms of units in real quadratic fields.

\section{Units in Real Quadratic Fields}
Let $d$ be a nonsquare positive integer, and set
\begin{align*}
  \Q(\sqrt{d}) & = \{a + b\sqrt{d} : a, b\in\Q\}  \\
  \Z[\sqrt{d}] & = \{a + b\sqrt{d} : a, b\in\Z\}.
\end{align*}
Then $\Q(\sqrt{d})$ is a {\em real quadratic field} and
$\Z[\sqrt{d}]$ is a ring.
There is a homomorphism called norm:
$$
  N : \Q(\sqrt{d})^* \ra \Q^*,
  \qquad
  N\left(a+b\sqrt{d}\right) = \left(a+b\sqrt{d}\right)\left(a-b\sqrt{d}\right) = a^2 - b^2 d.
$$
\begin{definition}
  An element $x\in R$ is a {\em unit} if there exists $y\in{}R$
  such that $xy=1$.
\end{definition}

\begin{proposition}
  The units of $\Z[\sqrt{d}]$ are exactly the elements of
  norm~$\pm 1$ in $\Z[\sqrt{d}]$.
\end{proposition}
\begin{proof}
  Suppose $u\in\Z[\sqrt{d}]$ is a unit.  Then
  $$
    1 = N(1) = N(u u^{-1}) = N(u)\cdot N(u^{-1}).
  $$
  Since $N(u), N(u^{-1})\in\Z$, we have $N(u) = N(u^{-1}) = \pm 1$
\end{proof}

Thus Fermat's challenge amounts to determing the group $U^+$ of
units in $\Z[\sqrt{d}]$ of the form $a+b\sqrt{d}$ with $a, b \geq 0$.
\begin{theorem}\label{thm:pell}
  The group $U^+$ is an infinite cyclic group.
  It is generated by $p_m+ q_m\sqrt{d}$, where
  $\frac{p_m}{q_m}$ is one of the partial convergents of the
  continued fraction expansion of $\sqrt{d}$.
  (In fact, if~$m$ is the period of the continued fraction of $\sqrt{d}$ then
  $n=m-1$ when~$m$ is even and $2n-1$ when $m$ is odd.)
\end{theorem}

The theorem implies that {\em Pell's equation always has a solution}!
Warning: the smallest solution is typically shockingly large.
For example, the value of~$x$ in the smallest solution to $x^2 -
  1000099y^2 = 1$ has {\bf 1118 digits}.

The following example illustrates how to use Theorem~\ref{thm:pell} to
solve Pell's equation when $d=61$, where the simplest solution is
already quite large.
\begin{example}
  Suppose $d=61$.  Then
  $$\sqrt{d} = [7, \overline{1, 4, 3, 1, 2, 2, 1, 3, 4, 1, 14}],$$
  which has odd period $n=11$. Thus the group $U^+$ is generated by
  \begin{align*}
    x & = p_{21} = 1766319049  \\
    y & = q_{21} =  226153980.
  \end{align*}
  That is, we have
  $$U^+ = \langle u \rangle = \langle 1766319049 + 226153980\sqrt{61}\rangle,$$
  and $x = 1766319049$, $y=226153980$ gives a solution to
  $x^2 - d y^2 = 1$.  All the other solutions arise from $u^n$ for
  some~$n$.  For example,
  $$u^2 = 6239765965720528801 + 798920165762330040\sqrt{61}$$
  leads to another solution.
\end{example}

\begin{remark}
  To help with your homework, note that if the equation
  $$
    x^2 - dy^2 = n
  $$
  has at least one (nonzero) solution $(x_0, y_0)\in\Z\cross\Z$, then it
  must have infinitely many solutions.  This is because if
  $x_0^2 - dy_0^2 =n$ and~$u$ is a generator of the
  cyclic group $U^+$, then for any integer~$i$,
  $$
    N(u^i(x_0+ y_0\sqrt{d})) = N(u^i)\cdot N(x_0+y_0\sqrt{d}) = 1\cdot n = n,
  $$
  so
  $$
    x_1 + y_1\sqrt{d} = u^i(x_0 + y_0\sqrt{d})
  $$
  provides another solution to $x^2 + d y^2 = n$.
\end{remark}


\section{Some Proofs}
The rest of this lecture is devoted to proving most of
Theorem~\ref{thm:pell}.  We will prove that partial convergents to
continued fractions contribute infinitely many solutions to Pell's
equation.  We will not prove that every solution to Pell's equation is
a partial convergent, though this is true.\footnote{There is a
complete proof in Section 13.5 of Burton's {\em Elementary Number
Theory}.  It just involves more of the same sort of computations that we've
been doing with continued fractions.}

Fix a positive nonsquare integer~$d$.

\begin{definition}
  A quadratic irrational $\alpha=a+b\sqrt{d}$ is {\em reduced} if
  $\alpha>1$ and if the conjugate of~$\alpha$, denoted by~$\alpha'$,
  satisfies $-1<\alpha'<0$.
\end{definition}
For example, the number $\alpha=1+\sqrt{2}$ is reduced.

\begin{definition}
  A continued fraction is {\em purely periodic}
  if it is of the form $[\overline{a_0,a_1,\ldots,a_n}]$.
\end{definition}
The continued fraction $[\overline{2}]$ of $1+\sqrt{2}$ is
purely periodic.

\begin{lemma}\label{lem:pure}
  If~$\alpha$ is a reduced quadratic irrational, then the continued
  fraction expansion of~$\alpha$ is purely periodic.  (The converse
  is also true, and is easy to prove.)
\end{lemma}
\begin{proof}
  The proof can be found on pages 102--103 of Davenport's book.
\end{proof}

\begin{lemma}\label{lemma:2a0}
  The continued fraction expansion of $\sqrt{d}$ is of the form
  $$[a_0, \overline{a_1, \ldots, a_{n-1}, 2 a_0}].$$
\end{lemma}
\begin{proof}
  Let $a_0$ be the floor of $\sqrt{d}$.
  Then $\alpha=\sqrt{d} + a_0$ is reduced because $\alpha>1$
  and $\alpha'=-\sqrt{d}+a_0$ satisfies $-1<\alpha'<0$.
  Let $[a_0, a_1, a_2, \ldots ]$ be the continued fraction
  expansion of $\sqrt{d}$.  Then the continued
  fraction expansion of $\sqrt{d}+a_0$ is
  $[2a_0, a_1, a_2, \ldots]$.
  By Lemma~\ref{lem:pure}, the continued fraction expansion of
  $\sqrt{d}+a_0$ is purely periodic, so
  $$[2a_0, a_1, a_2, \ldots] = [\overline{2a_0, a_1, a_2, \ldots, a_{n-1}}],$$
  where~$n$ is the period.  It follows that $a_n = 2 a_0$, as claimed.
\end{proof}

The following proposition shows that there are infinitely many
solutions to Pell's equation that arise from continued fractions.
\begin{proposition}
  Let $p_k/q_k$ be the partial convergents of the continued fraction
  expansion of $\sqrt{d}$, and let~$n$ be the period of the expansion
  of $\sqrt{d}$.  Then
  $$p_{kn-1}^2 - d q_{kn-1}^2 = (-1)^{kn}$$
  for $k=1,2,3,\ldots$.
\end{proposition}
\begin{proof}\footnote{This proof is from Section~13.5 of Burton's {\em Elementary Number Theory}.}
  By Lemma~\ref{lemma:2a0},
  for $k\geq 1$, the continued fraction of $\sqrt{d}$ can be written
  in the form
  $$\sqrt{d} = [a_0, a_1, a_2, \ldots, a_{kn-1}, r_{kn}]$$
  where
  $$r_{kn} = [2a_0, \overline{a_1, a_2, \ldots, a_{n}}] = a_0 + \sqrt{d}.$$
  Because $\sqrt{d}$ is the last partial convergent of the continued fraction
  above, we have
  $$
    \sqrt{d} = \frac{r_{kn} p_{kn-1} + p_{kn-2}}{r_{kn} q_{kn-1} + q_{kn-2}}.
  $$
  Upon substituting $r_{kn} = a_0 + \sqrt{d}$ and simplifying, this reduces
  to
  $$\sqrt{d}(a_0 a_{kn-1} + q_{kn-2} - p_{kn-1})
    = a_0 p_{kn-1} + p_{kn-2} - d q_{kn-1}.$$
  Because the right-hand side is rational and $\sqrt{d}$ is irrational,
  $$a_0 a_{kn-1} + q_{kn-2} = p_{kn-1},
    \quad\text{ and }\quad
    a_0 p_{kn-1} + p_{kn-2} = d q_{kn-1}.$$
  Multiplying the first of these equations by $p_{kn-1}$
  and the second by $-q_{kn-1}$, and then adding them, gives
  $$
    p_{kn-1}^2 - d q_{kn-1}^2 = p_{kn-1}q_{kn-2} - q_{kn-1} p_{kn-2}.$$
  But
  $$
    p_{kn-1}q_{kn-2} - q_{kn-1} p_{kn-2} = (-1)^{kn-2} = (-1)^{kn},
  $$
  which proves the proposition.
\end{proof}







\chapter{Binary Quadratic Forms I: Sums of Two Squares}


Today we study the question of which integers are the sum of two squares.

\section{Sums of Two Squares}
During the next four lectures, we will study binary quadratic forms.
A simple example of a binary quadratic form that will occupy us today is
$$
  x^2 + y^2.
$$
A typical question that one asks about a quadratic form is which
integers does it represent.  ``Are there integers~$x$ and~$y$
so that $x^2 + y^2 = 389$?  So that $x^2 + y^2 = 2001$?''


\subsection{Which Numbers are the Sum of Two Squares?}
The main goal of today's lecture is to prove the following theorem.
\begin{theorem}\label{thm:sumsquare}
  A number~$n$ is a sum of two squares if and only if all prime factors
  of~$n$ of the form $4m+3$ have even exponent in the prime factorization
  of~$n$.
\end{theorem}
Before tackling a proof, we consider a few examples.
\begin{example}\mbox{}\vspace{-3ex}\\
  \begin{itemize}
    \item $5 = 1^2 + 2^2$.
    \item $7$ is not a sum of two squares.
    \item $2001$ is divisible by $3$ because $2+1$ is, but not by $9$ since $2+1$ is not, so $2001$ is {\em not} a sum of two squares.
    \item $2\cdot 3^4\cdot 5\cdot 7^2\cdot 13$ is a sum of two squares.
    \item $389$ is a sum of two squares, since $389\con 1\pmod{4}$ and $389$ is prime.
    \item $21=3\cdot 7$ is {\em not} a sum of two squares even though $21\con 1\pmod{4}$.
  \end{itemize}
\end{example}

In preparation for the proof of Theorem~\ref{thm:sumsquare}, we recall a
result that emerged when we analyzed how  partial convergents
of a continued fraction converge.
\begin{lemma}\label{lem:approx}
  If $x\in\R$ and $n\in\N$, then there is a fraction $\ds\frac{a}{b}$
  in lowest terms such that $0<b\leq n$ and
  $$\left| x - \frac{a}{b} \right| \leq \frac{1}{b(n+1)}.$$
\end{lemma}
\begin{proof}
  Let $[a_0,a_1,\ldots]$ be the continued fraction expansion of~$x$.
  As we saw in the proof of Theorem~2.3 in Lecture~18, for each~$m$
  $$
    \left| x - \frac{p_m}{q_m}\right|
    < \frac{1}{q_m \cdot q_{m+1}}.
  $$
  Since $q_{m+1}$ is always at least~$1$ bigger than $q_m$ and $q_0=1$,
  either there exists an~$m$ such that $q_m\leq n < q_{m+1}$, or the
  continued fraction expansion of~$x$ is finite and $n$ is larger
  than the denominator of the rational number~$x$.  In the first
  case,
  $$
    \left| x - \frac{p_m}{q_m}\right|
    < \frac{1}{q_m \cdot q_{m+1}}
    \leq \frac{1}{q_m \cdot (n+1)},$$
  so $\ds\frac{a}{b} = \frac{p_m}{q_m}$ satisfies the conclusion of
  the lemma.   In the second case, just let $\ds\frac{a}{b} = x$.

\end{proof}

\begin{definition}
  A representation $n=x^2 + y^2$ is {\em primitive} if
  $\gcd(x,y)=1$.%; otherwise, it is {\em imprimitive}.
\end{definition}

\begin{lemma}\label{lem:primitive}
  If~$n$ is divisible by a prime~$p$ of the form $4m+3$, then~$n$
  has no primitive representations.
\end{lemma}
\begin{proof}
  If~$n$ has a primitive representation, $n=x^2 + y^2$, then
  $$
    p \mid x^2 + y^2\quad \text{ and }\quad \gcd(x,y)=1,
  $$
  so $p\nmid x$ and $p\nmid y$.  Thus
  $x^2 + y^2 \con 0\pmod{p}$
  so, since $\Z/p\Z$ is a field we can divide by $y^2$ and see that
  $$
    (x/y)^2 \con -1\pmod{p}.
  $$
  Thus the quadratic residue symbol $\kr{-1}{p}$ equals $+1$.
  However,
  $$
    \kr{-1}{p} = (-1)^{\frac{p-1}{2}} = (-1)^\frac{4m+3-1}{2} = (-1)^{2m+1} = -1.
  $$
\end{proof}

\begin{proof}[Proof of Theorem~\ref{thm:sumsquare}]
  \noindent$\left(\Longrightarrow\right)$
  Suppose that~$p$ is of the form $4m+3$, that $p^r\mid\mid n$ (exactly
  divides) with~$r$ odd, and that $n=x^2+y^2$.  Letting $d=\gcd(x,y)$,
  we have
  $$
    x = dx', \quad y = dy', \quad n = d^2 n'
  $$
  with $\gcd(x',y')=1$ and
  $$
    (x')^2 + (y')^2 = n'.
  $$

  Because~$r$ is odd, $p\mid n'$, so Lemma~\ref{lem:primitive}
  implies that $\gcd(x',y')>1$, a contradiction.
  \vspace{0.3ex}

  \noindent$\left(\Longleftarrow\right)$
  Write $n=n_1^2 n_2$ where $n_2$ has no prime factors of the
  form $4m+3$.  It suffices to show that~$n_2$ is a sum of two
  squares.  Also note that
  $$
    (x_1^2 + y_1^2)(x_2^2+y_2^2) = (x_1x_2+y_1y_2)^2 + (x_1y_2-x_2y_1)^2,
  $$
  so a product of two numbers that are sums of two squares is also
  a sum of two squares.\footnote{This algebraic identity is secretely
    the assertion that the norm map $N:\Q(i)^* \ra \Q^*$ sending
    $x+iy$ to $(x+iy)(x-iy)=x^2+y^2$ is a homomorphism.}
  Also, the prime~$2$ is a sum of two squares.
  It thus suffices to show that if~$p$ is a prime of the form
  $4m+1$, then~$p$ is a sum of two squares.

  Since
  $$
    (-1)^{\frac{p-1}{2}} = (-1)^{\frac{4m****}{2}} = +1,
  $$
  $-1$ is a square modulo~$p$; i.e., there exists~$r$ such
  that $r^2\con -1\pmod{p}$.  Taking $n=\lfloor \sqrt{p}\rfloor$
  in Lemma~\ref{lem:approx} we see that there are integers $a, b$
  such that
  $0<b<\sqrt{p}$ and
  $$
    \left| -\frac{r}{p} - \frac{a}{b}\right| \leq\frac{1}{b(n+1)} < \frac{1}{b\sqrt{p}}.
  $$
  If we write
  $$
    c = rb + pa
  $$
  then
  $$
    |c| < \frac{pb}{b\sqrt{p}} = \frac{p}{\sqrt{p}} = \sqrt{p}
  $$
  and
  $$
    0 < b^2 + c^2 < 2p.
  $$
  But $c \con rb\pmod{p}$, so
  $$
    b^2 + c^2 \con b^2 + r^2 b^2 \con b^2(1+r^2) \con 0\pmod{p}.
  $$
  Thus $b^2 + c^2 = p$.
\end{proof}

\subsection{Computing $x$ and $y$}
Suppose~$p$ is a prime of the form $4m+1$.  There is a construction of
Legendre of~$x$ and~$y$ that is explained on pages 120--121 of
Davenport.  I'm unconvinced that it is any more efficient than the
following naive algorithm: compute $\sqrt{p-x^2}$ for $x=1,2,\ldots$
until it's an integer.  This takes at most $\sqrt{p}$ steps.  Here's
a simple PARI program which implements this algorithm.

\begin{verbatim}
{sumoftwosquares(n) =
   local(y);
   for(x=1,floor(sqrt(n)),
      y=sqrt(n-x^2); 
      if(y-floor(y)==0, return([x,floor(y)]))
   );
   error(n," is not a sum of two squares.")
}
\end{verbatim}

\section{Sums of More Squares}
Every natural number is a sum of {\bf four} squares.  See pages
124--126 of Davenport for a proof.

A natural number is a sum of {\bf three} squares if and only if it is not a
power of~$4$ times a number that is congruent to~$7$ modulo~$8$.  For
example, $7$ is not a sum of three squares.  This is more difficult to
prove.






\chapter{Binary Quadratic Forms II: Basic Notions}



\section{Introduction}
A {\em binary quadratic form} is a homogeneous polynomial
$$
  ax^2 + bxy + cy^2 \in\Z[x,y].
$$
(There is a theory of quadratic forms in $n$-variables, but we will
not study it in this course.)  Chapter VI of Davenport's book is clear
and well written.  Read it.

\hd{The Classic Problem:}  {\sf Given a binary quadratic form
  $f(x,y) = ax^2 + bxy + cy^2$,
  what is the set of integers $\{f(x,y) : x, y\in\Z\}$?}\vspace{1ex}

That is, for which integers~$n$ are there integers~$x$ and~$y$ such
that
$$
  ax^2 + bxy + cy^2 = n?
$$
We gave a clean answer to this question in the last lecture
in the case when $f(x,y) = x^2 + y^2$.  The set
of sums of two squares is the set of integers~$n$
such that any prime divisor~$p$ of~$n$ of the form $4m+3$ exactly
divides~$n$ to an even power (along with~$0$).
In your homework (Problem~5), you will give a simple answer to
the question of which numbers are of the form $x^2+2y^2$.
Is there a simple answer in general?

\section{Equivalence}
\begin{definition}
  The modular group $\SL_2(\Z)$ is the group of all $2\times 2$
  integer matrices with determinant $+1$.
\end{definition}
If $g=\abcd{p}{q}{r}{s}\in\SL_2(\Z)$ and
$f(x,y)=ax^2 + bxy+cy^2$ is a quadratic form, let
$$
  f|_g (x,y)= f(px+qy, rx+sy) =
  f\left(\mtwo{p}{q}{r}{s}\vtwo{x}{y}\right),
$$
where for simplicity we will sometimes write
$f\left(\vtwo{x}{y}\right)$ for $f(x,y)$.
\begin{proposition}
  The above formula defines a right action of the group $\SL_2(\Z)$
  on the set of binary quadratic forms, in the sense that
  $$
    f|_{gh} = (f|_g)|_h.
  $$
\end{proposition}
\begin{proof}
  $$
    f|_{gh}(x,y) = f\left(gh\vtwo{x}{y}\right)
    = f|_g\left(h\left(\vtwo{x}{y}\right)\right)
    = (f|_g)|_h(x,y).
  $$
  %       &= f\left(g\left(h\vtwo{x}{y}\right)\right) 
  %        = f\left(gh\vtwo{x}{y}\right)
  %        = f|_{gh}\left(\vtwo{x}{y}\right).
  %\end{align*}
\end{proof}

\begin{proposition}\label{prop:samerep}
  Let $g\in\SL_2(\Z)$ and let $f(x,y)$ be a binary quadratic form.
  The set of integers represented by $f(x,y)$ is exactly the same
  as the set of integers represented by $f|_g(x,y)$.
\end{proposition}
\begin{proof}
  If $f(x_0, y_0)=n$  then
  since $g^{-1}\in\SL_2(\Z)$, we have $g^{-1}\vtwo{x_0}{y_0}\in\Z^2$, so
  $$
    f|_g\left(g^{-1}\vtwo{x_0}{y_0}\right) = f (x_0, y_0) = n.
  $$
  Thus every integer represented by~$f$ is also represented by $f|_g$.
  Conversely, if $f|_g(x_0,y_0)=n$, then
  $f\left(g\vtwo{x_0}{y_0}\right) = n$,
  so~$f$ represents~$n$.
\end{proof}

Define an equivalence relation $\sim$ on the set of all binary
quadratic forms by declaring that~$f$ is equivalent to~$f'$ if
there exists $g\in\SL_2(\Z)$ such that $f|_g = f'$.

For simplicity, we will sometimes denote the quadratic form
$ax^2 + bxy+cy^2$ by $(a,b,c)$.
Then, for example, since $g=\abcd{0}{-1}{1}{\hfill 0}\in\SL_2(\Z)$,
we see that
$(a,b,c)\sim (c,-b,a)$,
since if $f(x,y) = ax^2 + bxy + cy^2$, then
$f(-y,x) = ay^2 - bxy + cx^2$.

\begin{example}
  Consider the binary quadratic form
  $$
    f(x,y) = 458x^2 + 214xy + 25y^2.
  $$
  Solving the representation problem for~$f$ might, at first glance,
  look hopeless.  We find $f(x,y)$ for a few values of~$x$
  and~$y$:
  \begin{align*}
    f(-1,-1) & =17\cdot 41       \\
    f(-1,0)  & =2\cdot 229       \\
    f(0,-1)  & =5^2              \\
    f(1,1)   & =269              \\
    f(-1,2)  & =2\cdot 5\cdot 13 \\
    f(-1,3)  & =41
  \end{align*}
  Each number is a sum of two squares!
  Letting $g=\abcd{\hfill4}{-3}{-17}{13}$, we have
  $$f|_g =
    458(4x-3y)^2 + 214(4x-3y)(-17x+13y)
    + 25(-17x+13y)^2 = \cdots = x^2 + y^2!!
  $$
  By Proposition~\ref{prop:samerep}, $f$ represents an integer~$n$
  if and only if~$n$ is a sum of two squares.
\end{example}


\section{Discriminants}
\begin{definition}
  The {\em discriminant} of $f(x,y) = ax^2 + bxy+cy^2$ is
  $b^2 - 4ac$.
\end{definition}
\begin{example}
  $\disc(x^2 + y^2) = -4$ and
  $$\disc(458,214,25) = 214^2 - 4\cdot 25\cdot 458 = -4.$$
  That the discriminants are the same is a good hint that
  $(1,0,1)$ and $(458,214,25)$ are closely related.
  Inspecting discriminants is more effective than simply computing $f(x,y)$
  for many values of~$x$ and~$y$ and staring at the result.
\end{example}
\begin{proposition}
  If $f\sim f'$, then $\disc(f) = \disc(f')$.
\end{proposition}
\begin{proof}
  By tedious but elementary algebra (see page 133 of Davenport's book),
  one sees that if $g\in\SL_2(\Z)$, then
  $$
    \disc(f|_g) = \disc(f)\cdot (\det(g))^2 = \disc(f).
  $$
  Since $f'=f|_g$ for some $g\in\SL_2(\Z)$, the proposition
  follows.
\end{proof}

\noindent{\bf WARNING:} The converse of the proposition is false!
Forms with the same discriminant need not be equivalent.  For example,
the forms $(1,0,6)$ and $(2,0,3)$ have discriminant $-24$, but are not
equivalent.  To see this, observe that $(1,0,6)$ represents~$1$, but
$2x^2 + 3y^2$ does not represent~$1$.

\begin{proposition}
  The set of all discriminants of forms is exactly the set of integers~$d$
  such that $d\con 0\text{ or }1\pmod{4}$.
\end{proposition}
\begin{proof}
  First note that $b^2-4ac$ is a square modulo~$4$, so it must equal $0$
  or $1$ modulo~$4$.  Next suppose~$d$ is an integer
  such that $d\con 0\text{ or }1\pmod{4}$.   If we set
  $$
    c = \begin{cases}
      -d/4,    & \text{if $d\con 0\pmod{4}$}  \\
      -(d-1)/4 & \text{if $d\con 1\pmod{4}$},
    \end{cases}
  $$
  then $\disc(1,0,c)=d$ in the first case and
  $\disc(1,1,c)=d$ in the second.
\end{proof}

\begin{definition}
  The form $(1,0,-d/4)$ or $(1,1,-(d-1)/4)$ of discriminant~$d$ that
  appears in the proof of the previous proposition is called the
    {\em principal form} of discriminant~$d$.
\end{definition}

\begin{center}
  \begin{tabular}{rc|cll}
    $d$   &  &  & principal form &                    \\\hline
    $-4$  &  &  & (1,0,1)        & $x^2 + y^2$        \\
    $5$   &  &  & (1,1,-1)       & $x^2 + xy-y^2$     \\
    $-7$  &  &  & (1,1,2)        & $x^2 + xy+2y^2$    \\
    $8$   &  &  & (1,0,-2)       & $x^2 - 2y^2$       \\
    $-23$ &  &  & (1,1,6)        & $x^2 + xy + 6y^2$  \\
    $389$ &  &  & (1,1,-97)      & $x^2 + xy - 97y^2$
  \end{tabular}
\end{center}



\section{Definite and Indefinite Forms}
\begin{definition}
  A quadratic form with negative discriminant
  is called {\em definite}.  A form with positive
  discriminant is called {\em indefinite}.
\end{definition}
Let $(a,b,c)$ be a quadratic form.  Multiply by $4a$ and complete
the square:
\begin{align*}
  4a(ax^2+bxy+cy^2) & = 4a^2x^2 + 4abxy + 4acy^2  \\
                    & = (2ax+by)^2 + (4ac-b^2)y^2
\end{align*}
If $\disc(a,b,c)<0$ then $4ac-b^2=-\disc(a,b,c)>0$, so
$ax^2 + bxy+cy^2$ takes only positive or only negative values,
depending on the sign of~$a$.  In this sense, $(a,b,c)$ is very
  {\sf definite} about its choice of sign.  If $\disc(a,b,c)>0$,
then $(2ax+by)^2 + (4ac-b^2)y^2$ takes both positive and negative
values, so $(a,b,c)$ does also.

We will consider only definite forms in the next two lectures.

\section{Real Life}
The following text is from the documentation for binary quadratic
forms in the {\sc Magma} computer algebra system.  A quick scan of the
buzzwords emphasized (by me) below conveys an idea of where
binary quadratic forms appear in mathematics.

  {\small
    \begin{quote}
      A binary quadratic form is an integral form $ax^2 + bxy + cy^2$ which
      is represented in {\sc Magma} by a tuple $(a, b, c)$. Binary
      quadratic forms play an central role in the {\em ideal theory of
          quadratic fields}, the classical theory of {\em complex
          multiplication}, and the theory of {\em modular forms}. Algorithms
      for binary quadratic forms provide efficient means of computing in
      the {\em ideal class group of orders} in a {\em quadratic field}. By
      using the explicit relation of definite quadratic forms with lattices
      with nontrivial endomorphism ring in the complex plane, one can apply
        {\em modular and elliptic functions} to forms, and exploit the
      analytic theory of complex multiplication.

      The structures of quadratic forms of a given discriminant~$D$ correspond
      to ordered bases of ideals in an order in a {\em quadratic number field},
      defined up to scaling by the rationals. A form is primitive if the
      coefficients $a$, $b$, and $c$ are coprime. For negative discriminants the
      primitive reduced forms in this structure are in bijection with the
        {\em class group of projective or invertible ideals}. For positive
      discriminants, the reduced orbits of forms are used for this
      purpose. Magma holds efficient algorithms for composition, enumeration
      of reduced forms, {\em class group computations}, and
        {\em discrete logarithms}. A
      significant novel feature is the treatment of nonfundamental
      discriminants, corresponding to nonmaximal orders, and the collections
      of homomorphisms between different class groups coming from the
      inclusions of these orders.

      The functionality for binary quadratic forms is rounded out with
      various functions for applying modular and elliptic functions to
      forms, and for {\em class polynomials} associated to {\em class groups} of
      definite forms.
    \end{quote}
  }





\renewcommand{\vtwo}[2]{\left[
    \begin{matrix}#1 \\#2
    \end{matrix}\right]}
\chapter{Binary Quadratic Forms III: Reduction Theory}


Recall that a binary quadratic form is a function
$f(x,y) = ax^2 + bxy+cy^2$.  Our motivating problem
is to decide which numbers are ``represented'' by~$f$;
i.e., for which integers~$n$ do there exist integers
$x, y$ such that $ax^2 + bxy + cy^2 = n$?  If $g\in\SL_2(\Z)$
then $f(x,y)$ and $f|_g(x,y) = f\left(g\vtwo{x}{y}\right)$ represent
exactly the same set of integers.  Also, $\disc(f)=\disc(f|_g)$,
where
$\disc(f) = b^2 - 4ac$,
and~$f$ is called {\em positive definite}
if $\disc(f)<0$ and $a>0$.

In today's lecture, we will learn about reduction theory, which
allows us to decide whether or not two positive definite binary
quadratic forms are equivalent under the action of $\SL_2(\Z)$.

If, in the future, you would like to pursue the theory of binary
quadratic forms in either a more algebraic or algorithmic direction, I
highly recommend that you look at Chapter~5 of Henri Cohen's book {\em
    A Course in Computational Algebraic Number Theory} (GTM 138).


\section{Reduced Forms}
\begin{definition}[Reduced]
  A positive definite quadratic form $(a,b,c)$ is {\em reduced}
  if $|b|\leq a \leq c$ and if, in addition, when one of the
  two inequalities is an equality (i.e., either $|b|=a$ or $a=c$),
  then $b\geq 0$.
\end{definition}


There is a geometric interpretation of reduced, which we will not
use this later.  Let $D=\disc(a,b,c) = b^2 - 4ac$ and set $\tau =
  \frac{-b+\sqrt{D}}{2a}$, so $\tau$ is the root of $ax^2 + bx + c$
with positive imaginary part.  The right action of $\SL_2(\Z)$ on
positive definite binary quadratic forms corresponds to the left
action of $\SL_2(\Z)$ by linear fractional transformations on the
complex upper half plane $\h = \{z \in \C : \Im(z)>0\}$.  The standard
fundamental domain for the action of $\SL_2(\Z)$ on~$\h$ is
$$\mathcal{F} = \left\{\tau \in \h : \Re(\tau)\in\left[-\frac{1}{2},\frac{1}{2}\right),
  |\tau|>1 \text{ or } |\tau|=1\text{ and }\Re(\tau)\leq 0\right\}.$$
Then $(a,b,c)$ is reduced if and only if the corresponding complex
number~$\tau$ lies in $\mathcal{F}$.  For example, if $(a,b,c)$
is reduced then $\Re(\tau) = -b/2a\in[-1/2,1/2)$ since
$|b|\leq a$ and if $|b|=a$ then $b\geq 0$.  Also
$$
  |\tau| = \sqrt{\frac{b^2 + 4ac - b^2}{4a^2}}
  = \sqrt{\frac{c}{a}} \geq 1$$
and if $|\tau|=1$ then $b\geq 0$ so $\Re(\tau) \leq 0$.

The following theorem (which is not proved in Davenport) highlights the
importance of reduced forms.
\begin{theorem}
  There is exactly one reduced form in each equivalence class of
  positive definite binary quadratic forms.
\end{theorem}
\begin{proof}
  We have to prove two things.  First, that every class contains at least
  one reduced form, and second that this reduced form is the only
  one in the class.

  We first prove that there is a reduced form in every class.
  Let $\mathcal{C}$ be an equivalence class of positive definite
  quadratic forms of discriminant~$D$.  Let~$(a,b,c)$ be
  an element of $\mathcal{C}$ such that~$a$ is minimal (amongst elements
  of $\mathcal{C}$).  Note that for any such form we
  have $c\geq a$, since $(a,b,c)$ is equivalent to
  $(c,-b,a)$ (use the matrix $\abcd{0}{-1}{1}{\hfill 0}$).
  Applying the element $\abcd{1}{k}{0}{1}\in\SL_2(\Z)$ to $(a,b,c)$
  for a suitably chosen integer~$k$ (precisely,
  $k=\lfloor(a-b)/2a\rfloor$) results in a form
  $(a',b',c')$ with $a'=a$ and $b'\in (-a',a']$.
  Since $a'=a$ is minimal, we have just as above that
  $a'\leq c'$, hence
  $(a',b',c')$ is ``just about'' reduced.
  The only possible remaining problem would occur if $a'=c'$
  and $b'<0$. In that case, changing $(a',b',c')$ to
  $(c'',b'',a'')=(c',-b',a')$
  results in an equivalent form with $b''>0$, so that
  $(c'',b'',a'')$ is reduced.

  Next suppose $(a,b,c)$ is a reduced form.  We
  will now establish that $(a,b,c)$ is the only reduced form
  in its equivalence class.  First, we check that~$a$ is minimal amongst all
  forms equivalent to~$(a,b,c)$.  Indeed, every other $a'$ has the
  form $a'=ap^2 + bpr + cr^2$ with $p,r$ coprime integers (see this
  by hitting $(a,b,c)$ by $\abcd{p}{q}{r}{s}$).  The
  identities
  $$
    a p^2 + bpr + c r^2 = a p^2 \left(1+\frac{b}{a}\frac{r}{p}\right)
    + cr^2 = a p^2 + c r^2\left( 1 + \frac{b}{c}\frac{p}{r}\right)
  $$
  then imply our claim since $|b|\leq a \leq c$
  (use the first identity if $r/p<1$ and the second otherwise).
  Thus any other reduced form $(a',b',c')$ equivalent to $(a,b,c)$
  has $a'=a$.  But the same identity implies that
  the only forms equivalent to $(a,b,c)$ with $a'=a$ are obtained by applying
  a transformation of the form $\mtwo{1}{k}{0}{1}$ (corresponding
  to $p=1$, $r=0$).  Thus $b' = b+2ak$ for some~$k$.
  Since $a=a'$ we have $b,b'\in(-a,a]$, so $k=0$.
  Finally
  $$
    c'=\frac{(b')^2-D}{4a'}=\frac{b^2-D}{4a} = c,
  $$
  so $(a',b',c')=(a,b,c)$.
\end{proof}

\section{Finding an Equivalent Reduced Form}
Here is how to find the reduced form equivalent
to a given positive definite form $(a,b,c)$.
This algorithm is useful for solving problems 8 and 9 on
the homework assignment.
Consider the following two operations, which can be used to diminish
one of~$a$ and $|b|$, without altering the other:
\begin{enumerate}
  \item
        If $c<a$, replace $(a,b,c)$ by the equivalent form
        $(c,-b,a)$.

  \item
        If $|b|>a$, replace $(a,b,c)$ by the equivalent form
        $(a,b',c')$ where $b'=b+2ka$ and~$k$ is chosen so that
        $b'\in(-a,a]$ (more precisely, $k=\lfloor \frac{a-b}{2a}\rfloor$),
        and $c'$ is found from the fact that
        $(b')^2 - 4ac'=D=\disc(a,b,c)$, so
        $c' = \frac{(b')^2 - D}{4a}$.
\end{enumerate}
Starting with $(a,b,c)$, if you iterate the appropriate operation,
eventually you will find the reduced form that is equivalent to
$(a,b,c)$.


\begin{example}
  Let $f=458x^2 + 214xy + 25y^2$.
  \begin{center}
    \begin{tabular}{|l|l|l|}\hline
      Equivalent form & What I did      & Matrix               \\\hline
      $(458,214,25)$  &                 &                      \\\hline
      $(25,-214,458)$ & (1)             & $\abcd{0}{-1}{1}{0}$ \\\hline
      $(25,-14,2)$    & (2) with $k=4$  & $\abcd{1}{4}{0}{1}$  \\\hline
      $(2,14,25)$     & (1)             & $\abcd{0}{-1}{1}{0}$ \\\hline
      $(2,2,1)$       & (2) with $k=-3$ & $\abcd{1}{-3}{0}{1}$ \\\hline
      $(1,-2,2)$      & (1)             & $\abcd{0}{-1}{1}{0}$ \\\hline
      $(1,0,1)$       & (2) with $k=1$  & $\abcd{1}{1}{0}{1}$  \\\hline
    \end{tabular}
  \end{center}

  Let
  \begin{align*}
    g & = \mtwo{0}{-1}{1}{0}\cdot \mtwo{1}{4}{0}{1}\cdot
    \mtwo{0}{-1}{1}{0}\cdot \mtwo{1}{-3}{0}{1}\cdot
    \mtwo{0}{-1}{1}{0}\cdot \mtwo{1}{1}{0}{1}            \\
      & = \mtwo{\hfill3}{\hfill4}{-13}{-17}.
  \end{align*}
  Then
  $$f|_g = x^2 + y^2!$$
\end{example}

\section{Some PARI Code}
The following PARI code checks whether or not a form is reduced,
and computes the reduced form equivalent to a given form.
You can download it from my web page if you don't want to type
it in.
\begin{verbatim}
\\ true if and only if (a,b,c) is reduced.
{isreduced(a,b,c) = 
   if(b^2-4*a*c>=0 || a<0, 
      error("reduce: (a,b,c) must be positive definite."));
   if(!(abs(b)<=a && a<=c), return(0));
   if(abs(b)==a || a==c, return(b>=0));
   return(1);
}

\\ reduces, printing out each step.  returns the reduced form
\\ and a matrix that transforms the input form to the reduced form.
{reduce(a,b,c,s) = 
   local(D, k, t, g); 
   D=b^2-4*a*c;
   if(D>=0 || a<0, error("reduce: (a,b,c) must be positive definite."));
   g=[1,0;0,1];
   while(!isreduced(a,b,c),       \\ ! means ``not''
      if(c<a, 
         b = -b; t = a; a = c; c = t; 
         g = g*[0,-1;1,0];
         print([a,b,c], "  \t(1)"),  \\ backslash t means ``tab''
      \\ else
         if (abs(b)>a || -b==a, 
            k = floor((a-b)/(2*a)); 
            b = b+2*k*a;
            c = (b^2-D)/(4*a);
            g = g*[1,k;0,1];
            print([a,b,c], "  \t(2) with k=",k)
         )
      )
   );
   return([a,b,c,g])
}

/* Here is an example:
? \r quadform
? reduce(458,214,25)
[25, -214, 458] (1)
[25, -14, 2]    (2) with k=4
[2, 14, 25]     (1)
[2, 2, 1]       (2) with k=-3
[1, -2, 2]      (1)
[1, 0, 1]       (2) with k=1
%22 = [1, 0, 1, [3, 4; -13, -17]]
*/
\end{verbatim}










\chapter{Binary Quadratic Forms IV: The Class Group}



\section{Can You Hear the Shape of a Lattice?}
After Lecture 23, Emanuele Viola asked me whether or
not the following is true: {\em ``If $f_1$ and $f_2$ are binary quadratic
forms that represent exactly the same integers, is $f_1\sim f_2$?''}
The answer is no.  For example,
$f_1=(2,1,3)=2x^2 + xy+3y^2$ and
$f_2=(2,-1,3)=2x^2 -xy+3y^2$
are inequivalent reduced positive definite binary quadratic
forms that represent exactly the same integers.   Note that
$\disc(f_1) = \disc(f_2)=-23$.
There appears to be a sense in which
all counterexamples resemble the one just given.

Questions like these are central to John H. Conway's book {\em The
    sensual (quadratic) form}, which I've never seen because the Cabot
library copy is checked out and the Birkhoff copy has gone missing.  The
following is taken from the {\sc MathSciNet} review (I changed the
text slightly so that it makes sense):
\begin{quote}
  Chapter~$2$ begins by posing Mark Kac's question of ``hearing the
  shape of a drum'', and the author relates the higher-dimensional
  analogue of this idea on tori---quotients of $\mathbf{R}\sp n$ by a
  lattice---to the question of what properties of a positive definite
  integral quadratic form are determined by the numbers the form
  represents. A property of such a form is called ``audible'' if the
  property is determined by these numbers, or equivalently, by the theta
  function of the quadratic form. As examples, he shows that the
  determinant of the form and the theta function of the dual form are
  audible. He also provides counterexamples to the higher-dimensional
  Kac question, the first of which were found by J. Milnor...
\end{quote}

\section{Class Numbers}
\begin{proposition}\label{prop:finite}
  Let $D<0$ be a discriminant.  There are only finitely
  many equivalence classes of positive definite binary
  quadratic forms of discriminant~$D$.
\end{proposition}
\begin{proof}
  Since there is exactly one reduced binary quadratic form
  in each equivalence class, it suffices to show that there are
  only finitely many reduced forms of discriminant~$D$.  Recall
  that if a form $(a,b,c)$ is reduced, then
  $|b| \leq a \leq c$.
  If $(a,b,c)$ has discriminant~$D$ then $b^2-4ac=D$.
  Since $b^2\leq a^2 \leq ac$, we have
  $D = b^2-4ac\leq -3ac$, so
  $$
    3ac \leq -D.
  $$
  There are only finitely many positive integers $a,c$ that
  satisfy this inequality.
\end{proof}

\begin{definition}
  A binary quadratic form $(a,b,c)$ is {\em primitive}
  if $\gcd(a,b,c)=1$.
\end{definition}

\begin{definition}
  The {\em class number} $h_D$ of discriminant $D<0$ is the number
  of equivalence classes of primitive positive definite binary
  quadratic forms of discriminant~$D$.
\end{definition}

I computed the following table of class number $h_D$ for $-D\leq 839$
using the built-in PARI function {\tt qfbclassno(D,1)}.
Notice that there are just a few $1$s at the beginning and then no more.
\begin{center}
  \begin{tabular}{|ll|}\hline
    $-D$ & $h_{D}$ \\\hline
    3    & {\bf 1} \\
    7    & {\bf 1} \\
    11   & {\bf 1} \\
    15   & 2       \\
    19   & {\bf 1} \\
    23   & 3       \\
    27   & {\bf 1} \\
    31   & 3       \\
    35   & 2       \\
    39   & 4       \\
    43   & {\bf 1} \\
    47   & 5       \\
    51   & 2       \\
    55   & 4       \\
    59   & 3       \\
    63   & 4       \\
    67   & {\bf 1} \\
    71   & 7       \\
    75   & 2       \\
    79   & 5       \\
    83   & 3       \\
    87   & 6       \\
    91   & 2       \\
    95   & 8       \\
    99   & 2       \\
    103  & 5       \\
    107  & 3       \\
    111  & 8       \\
    115  & 2       \\
    119  & 10      \\\hline
  \end{tabular}
  \begin{tabular}{|ll|}\hline
    $-D$ & $h_{D}$ \\\hline
    123  & 2       \\
    127  & 5       \\
    131  & 5       \\
    135  & 6       \\
    139  & 3       \\
    143  & 10      \\
    147  & 2       \\
    151  & 7       \\
    155  & 4       \\
    159  & 10      \\
    163  & {\bf 1} \\
    167  & 11      \\
    171  & 4       \\
    175  & 6       \\
    179  & 5       \\
    183  & 8       \\
    187  & 2       \\
    191  & 13      \\
    195  & 4       \\
    199  & 9       \\
    203  & 4       \\
    207  & 6       \\
    211  & 3       \\
    215  & 14      \\
    219  & 4       \\
    223  & 7       \\
    227  & 5       \\
    231  & 12      \\
    235  & 2       \\
    239  & 15      \\\hline
  \end{tabular}
  \begin{tabular}{|ll|}\hline
    $-D$ & $h_{D}$ \\\hline
    243  & 3       \\
    247  & 6       \\
    251  & 7       \\
    255  & 12      \\
    259  & 4       \\
    263  & 13      \\
    267  & 2       \\
    271  & 11      \\
    275  & 4       \\
    279  & 12      \\
    283  & 3       \\
    287  & 14      \\
    291  & 4       \\
    295  & 8       \\
    299  & 8       \\
    303  & 10      \\
    307  & 3       \\
    311  & 19      \\
    315  & 4       \\
    319  & 10      \\
    323  & 4       \\
    327  & 12      \\
    331  & 3       \\
    335  & 18      \\
    339  & 6       \\
    343  & 7       \\
    347  & 5       \\
    351  & 12      \\
    355  & 4       \\
    359  & 19      \\\hline
  \end{tabular}
  \begin{tabular}{|ll|}\hline
    $-D$ & $h_{D}$ \\\hline
    363  & 4       \\
    367  & 9       \\
    371  & 8       \\
    375  & 10      \\
    379  & 3       \\
    383  & 17      \\
    387  & 4       \\
    391  & 14      \\
    395  & 8       \\
    399  & 16      \\
    403  & 2       \\
    407  & 16      \\
    411  & 6       \\
    415  & 10      \\
    419  & 9       \\
    423  & 10      \\
    427  & 2       \\
    431  & 21      \\
    435  & 4       \\
    439  & 15      \\
    443  & 5       \\
    447  & 14      \\
    451  & 6       \\
    455  & 20      \\
    459  & 6       \\
    463  & 7       \\
    467  & 7       \\
    471  & 16      \\
    475  & 4       \\
    479  & 25      \\\hline
  \end{tabular}
  \begin{tabular}{|ll|}\hline
    $-D$ & $h_{D}$ \\\hline
    483  & 4       \\
    487  & 7       \\
    491  & 9       \\
    495  & 16      \\
    499  & 3       \\
    503  & 21      \\
    507  & 4       \\
    511  & 14      \\
    515  & 6       \\
    519  & 18      \\
    523  & 5       \\
    527  & 18      \\
    531  & 6       \\
    535  & 14      \\
    539  & 8       \\
    543  & 12      \\
    547  & 3       \\
    551  & 26      \\
    555  & 4       \\
    559  & 16      \\
    563  & 9       \\
    567  & 12      \\
    571  & 5       \\
    575  & 18      \\
    579  & 8       \\
    583  & 8       \\
    587  & 7       \\
    591  & 22      \\
    595  & 4       \\
    599  & 25      \\\hline
  \end{tabular}
  \begin{tabular}{|ll|}\hline
    $-D$ & $h_{D}$ \\\hline
    603  & 4       \\
    607  & 13      \\
    611  & 10      \\
    615  & 20      \\
    619  & 5       \\
    623  & 22      \\
    627  & 4       \\
    631  & 13      \\
    635  & 10      \\
    639  & 14      \\
    643  & 3       \\
    647  & 23      \\
    651  & 8       \\
    655  & 12      \\
    659  & 11      \\
    663  & 16      \\
    667  & 4       \\
    671  & 30      \\
    675  & 6       \\
    679  & 18      \\
    683  & 5       \\
    687  & 12      \\
    691  & 5       \\
    695  & 24      \\
    699  & 10      \\
    703  & 14      \\
    707  & 6       \\
    711  & 20      \\
    715  & 4       \\
    719  & 31      \\\hline
  \end{tabular}
  \begin{tabular}{|ll|}\hline
    $-D$ & $h_{D}$ \\\hline
    723  & 4       \\
    727  & 13      \\
    731  & 12      \\
    735  & 16      \\
    739  & 5       \\
    743  & 21      \\
    747  & 6       \\
    751  & 15      \\
    755  & 12      \\
    759  & 24      \\
    763  & 4       \\
    767  & 22      \\
    771  & 6       \\
    775  & 12      \\
    779  & 10      \\
    783  & 18      \\
    787  & 5       \\
    791  & 32      \\
    795  & 4       \\
    799  & 16      \\
    803  & 10      \\
    807  & 14      \\
    811  & 7       \\
    815  & 30      \\
    819  & 8       \\
    823  & 9       \\
    827  & 7       \\
    831  & 28      \\
    835  & 6       \\
    839  & 33      \\\hline
  \end{tabular}
\end{center}

We can compute these numbers using Proposition~\ref{prop:finite}.
The following PARI program enumerates the primitive reduced
forms of discriminant~$D$.
\begin{verbatim}
{isreduced(a,b,c) = 
   if(b^2-4*a*c>=0 || a<0, 
      error("reduce: (a,b,c) must be positive definite."));
   if(!(abs(b)<=a && a<=c), return(0));
   if(abs(b)==a || a==c, return(b>=0));
   return(1);
}
{reduce(f) = 
   local(D, k, t, a,b,c); 
   a=f[1]; b=f[2]; c=f[3]; D=b^2-4*a*c;
   if(D>=0 || a<0, error("reduce: (a,b,c) must be positive definite."));
   while(!isreduced(a,b,c),       \\ ! means ``not''
      if(c<a, 
         b = -b; t = a; a = c; c = t,
      \\ else
         if (abs(b)>a || -b==a, 
            k = floor((a-b)/(2*a)); 
            b = b+2*k*a;
            c = (b^2-D)/(4*a);
         )
      )
   );
   return([a,b,c])
}
{reducedforms(D)=
   local(bound, forms, b, r);
   if (D > 0 || D%4 == 2 || D%4==3, error("Invalid discriminant"));
   bound = floor(-D/3);
   forms = [];
   for(a = 1, bound,
      for(c = 1, bound,
         if(3*a*c<=-D && issquare(4*a*c+D),
            b = floor(sqrt(4*a*c+D));
            r = reduce([a,b,c]);
            print1([a,b,c], " ----> ", r);
            if (gcd(r[1],gcd(r[2],r[3])) == 1,
               forms = setunion(forms,[r]); print(""),
               \\ else
               print ("  \t(not primitive)")
            )
         )
      )
   );
   return(eval(forms));    \\ eval gets rid of the annoying quotes.
}
\end{verbatim}

For example, when $D=-419$ the program finds exactly $9$ reduced forms:
\begin{verbatim}
? D = -419
%21 = -419
? qfbclassno(D,1)
%22 = 9
? reducedforms(D)
[1, 1, 105] ----> [1, 1, 105]
[1, 3, 107] ----> [1, 1, 105]
[1, 5, 111] ----> [1, 1, 105]
[1, 7, 117] ----> [1, 1, 105]
[1, 9, 125] ----> [1, 1, 105]
[1, 11, 135] ----> [1, 1, 105]
[3, 1, 35] ----> [3, 1, 35]
[3, 5, 37] ----> [3, -1, 35]
[3, 7, 39] ----> [3, 1, 35]
[3, 11, 45] ----> [3, -1, 35]
[5, 1, 21] ----> [5, 1, 21]
[5, 9, 25] ----> [5, -1, 21]
[5, 11, 27] ----> [5, 1, 21]
[7, 1, 15] ----> [7, 1, 15]
[9, 7, 13] ----> [9, 7, 13]
[9, 11, 15] ----> [9, -7, 13]
[13, 7, 9] ----> [9, -7, 13]
[15, 1, 7] ----> [7, -1, 15]
[15, 11, 9] ----> [9, 7, 13]
[21, 1, 5] ----> [5, -1, 21]
[25, 9, 5] ----> [5, 1, 21]
[27, 11, 5] ----> [5, -1, 21]
[35, 1, 3] ----> [3, -1, 35]
[37, 5, 3] ----> [3, 1, 35]
[39, 7, 3] ----> [3, -1, 35]
[45, 11, 3] ----> [3, 1, 35]
[105, 1, 1] ----> [1, 1, 105]
[107, 3, 1] ----> [1, 1, 105]
[111, 5, 1] ----> [1, 1, 105]
[117, 7, 1] ----> [1, 1, 105]
[125, 9, 1] ----> [1, 1, 105]
[135, 11, 1] ----> [1, 1, 105]
%23 = [[1, 1, 105], [3, -1, 35], [3, 1, 35], [5, -1, 21], [5, 1, 21], 
       [7, -1, 15], [7, 1, 15], [9, -7, 13], [9, 7, 13]]
? length(%23)
%24 = 9
\end{verbatim}

\begin{theorem}[Heegner, Stark-Baker, Goldfeld-Gross-Zagier]
  Suppose~$D$ is a negative discriminant that is either square
  free or~$4$ times a square-free number. Then
  \begin{itemize}
    \item $h_D=1$ only for $D=-3,-4,-7,-8,-11,-19,-43,-67,-163$.
    \item $h_D=2$ only for $D=-15, -20, -24, -35, -40, -51, -52, -88, -91,$\\
          $ -115,-123, -148, -187, -232, -235, -267, -403, -427$.
    \item $h_D=3$ only for $D=-23,-31,-59,-83,-107,-139,-211,-283,-307,$\\
          $-331,-379,-499,-547,-643,-883,-907.$
    \item $h_D=4$ only for $D=-39,-55,-56,-68,\ldots,-1555.$
  \end{itemize}
\end{theorem}
To quote Henri Cohen: ``The first two statements concerning class
numbers~$1$ and~$2$ are very difficult theorems proved in 1952 by
Heegner and in 1968--1970 by Stark and Baker.  The general problem of
determing all imaginary quadratic fields with a given class number has
been solved in principle by Goldfeld-Gross-Zagier, but to my
knowledge the explicit computations have been carried to the end
only for class numbers~$3$ and~$4$ (in addition to the already known
class numbers~$1$ and~$2$).

\section{The Class Group}
There are {\em much} more sophisticated ways to compute $h_D$ than
simply listing the reduced binary quadratic forms of discriminant~$D$,
which is an $O(|D|)$ algorithm.  For example, there is an algorithm
that can compute $h_D$ for $D$ having $50$ digits in a reasonable
amount of time.  These more sophisticated algorithms use the fact
that the set of primitive positive definite binary quadratic forms
of given discriminant is a finite abelian group.

\begin{definition}
  Let $f_1=(a_1,b_1,c_1)$ and $f_2=(a_2,b_2,c_2)$ be two
  quadratic forms of the same discriminant~$D$.  Set
  $s=(b_1+b_2)/2$, $n=(b_1-b_2)/2$ and let $u,v,w$ and~$d$
  be such that
  $$
    u a_1 + v a_2 + w s = d = \gcd(a_1, a_2, s)
  $$
  (obtained by two applications of Euclid's algorithm), and let
  $d_0 = \gcd(d,c_1,c_2,n)$.  Define the composite of the equivalence
  classes of the two forms $f_1$ and $f_2$ to be
  the equivalence class of the form
  $$
    (a_3, b_3, c_3) = \left(
    d_0 \frac{a_1 a_2}{d^2},
    b_2 + \frac{2a_2}{d}(v(s-b_2)-w c_2),
    \frac{b_3^2-D}{4a_3}\right).
  $$
\end{definition}
This mysterious-looking group law is induced by ``multiplication
of ideals'' in the ``ring of integers'' of the quadratic imaginary
number field $\Q(\sqrt{D})$.
The following PARI program computes this group operation:
\begin{verbatim}
{composition(f1, f2)=
   local(a1,b1,c1,a2,b2,c2,D,s,n,bz0,bz1,u,v,w);
   a1=f1[1]; b1=f1[2]; c1=f1[3];
   a2=f2[1]; b2=f2[2]; c2=f2[3];   
   D = b1^2 - 4*a1*c1;
   if(b2^2 - 4*a2*c2 != D, error("Forms must have the same discriminant."));
   s = (b1+b2)/2;
   n = (b1-b2)/2;
   bz0 = bezout(a1,a2);
   bz1 = bezout(bz0[3],s);
   u = bz1[1]*bz0[1];
   v = bz1[1]*bz0[2];
   w = bz1[2];
   d = bz1[3];
   d0 = gcd(gcd(gcd(d,c1),c2),n);
   a3 = d0*a1*a2/d^2;
   b3 = b2+2*a2*(v*(s-b2)-w*c2)/d;
   c3 = (b3^2-D)/(4*a3);
   f3 = reduce([a3,b3,c3]);
   return(f3);
}
\end{verbatim}
Let's try the group out in the case when $D=-23$.

\begin{verbatim}
? reducedforms(-23)
[1, 1, 6] ----> [1, 1, 6]
[2, 1, 3] ----> [2, 1, 3]
[3, 1, 2] ----> [2, -1, 3]
[6, 1, 1] ----> [1, 1, 6]
%56 = [[1, 1, 6], [2, -1, 3], [2, 1, 3]]
\end{verbatim}
Thus the group has elements $(1,1,6)$, $(2,-1,3)$, and $(2,1,3)$.
Since $h_{-23}=3$,
the group must be cyclic of order~$3$.  Let's find the identity element.
\begin{verbatim}
? composition([1,1,6],[2,-1,3])
%58 = [2, -1, 3]
\end{verbatim}
Thus the identity element must be $(1,1,6)$.
The element $(2,-1,3)$ is a generator for the group:
\begin{verbatim}
? composition([2,-1,3],[2,-1,3])
%59 = [2, 1, 3]
? composition([2,-1,3],[2,1,3])
%60 = [1, 1, 6]
\end{verbatim}












\chapter{Elliptic Curves 1: Introduction}



\section{The Definition}
Finally we come to elliptic curves, which I think are the most
exciting and central {\em easily accessibly} objects in modern number
theory.   There are so many exciting things to tell you about elliptic
curves, that the course is suddenly going to move more quickly
than before.

\begin{definition}
  An {\em elliptic curve}~$E$ over a field~$K$ is a plane cubic
  curve of the form
  $$
    y^2 + a_1 xy + a_3 y = x^3 + a_2 x^2 + a_4 x + a_6,
  $$
  where
  $ a_1, a_2, a_3, a_4, a_6 \in K$
  and
  $$
    \Delta = -b_2^2 b_8 - 8b_4^3 - 27 b_6^2 + 9b_2 b_4 b_6 \neq 0,
  $$
  where
  $$b_2 = a_1^2 + 4a_2, \quad b_4 = 2a_4 + a_1 a_3,\quad
    b_6 = a_3^2 + 4a_6.
  $$
\end{definition}

Help!  Don't worry, when $2$ and $3$ are not equal to $0$ in $K$, using
completing the square and a little algebra we find a change of
coordinates that transforms the above cubic equation into the form
$$
  y^2  = x^3 + ax + b,
$$
and then $\Delta = -16(4a^3 + 27b^2)$.
We will consider only elliptic curves of the form $y^2 = x^3 + ax+b$
for a while.

Hey!  That's not an ellipse!  You're right, elliptic curves are {\em
    not ellipses}; they are curves that first arose when $19$th century
mathematicians studied integral formulas for the arc lengths of
ellipses.


In these lectures, I'll give you a glimpse into two main ways in which
elliptic curves feature in mathematics.  On the left hand, they
provide the simplest example of a class of diophantine equations that
we still can't totally solve.  On the right hand, when~$K$ is a finite
field (or, more sneakily, a finite ring), elliptic curves can be used
as a tool for both making and breaking cryptosystems.

\section{Linear and Quadratic Diophantine Equations}
Consider the following question:
\begin{quote}
  Let $F(x,y)$ be an irreducible polynomial in two variables over~$\Q$.
  Find all rational numbers $x_0,y_0$ such that
  $
    F(x_0, y_0) = 0.
  $
\end{quote}
When $F$ is linear, this problem is easy.  The equation
$$
  F(x,y) = ax + by + c = 0
$$
defines a line, and letting $y=t$, the solutions are
$$
  \left\{ \left( -\frac{b}{a} t - \frac{c}{a}, t\right)
  \, : \, t \in \Q\right\}.
$$

When~$F$ is quadratic, the solution is not completely trivial, but it
is well understood.  In this case, the equation $F=0$ has infinitely
many rational solutions if and only if it has at least one solution.
Moreover, it is easy to describe all solutions when there is one.  If
$(x_0, y_0)$ is a solution and $L$ is a non-tangent line through
$(x_0,y_0)$, then~$L$ will intersect the curve $F=0$ in exactly one
other point $(x_1, x_1)$.  Also $x_1, y_1\in\Q$ since a quadratic
polynomial over~$\Q$ with $1$ rational root has both roots rational.
Thus the rational points on $F=0$ are in bijection with the slopes of
lines through $(x_0,y_0)$.

Chapter 2 of [Kato et al.] is about how to decide whether
or not an~$F$ of degree~$2$ has a rational point.  The answer is that
$F=0$ has a rational solution if and only if $F=0$ has a solution with
$x_0,y_0\in\R$ and a solution with $x_0,y_0\in\Q_p$ for every
``$p$-adic field'' $\Q_p$.   This condition, though it might sound
foreboding, is easy to check in practice.   I encourage you to
flip through chapter 2 of loc. cit.

\section{Points on Elliptic Curves}
Next suppose that~$F$ is an irreducible cubic polynomial.
The question of whether or not $F=0$ has a rational solution is
still an {\em open problem}!  We will not consider this problem
further until we discuss the Birch and Swinnerton-Dyer conjecture.

Suppose that $F=0$ has a given rational solution.  Then one can change
coordinates so that the question of finding the rational solutions to
$F=0$ is equivalent to the problem of finding all rational points on
the elliptic curve
$$
  y^2 = x^3 + ax + b.
$$

Recall that when~$F$ has degree~$2$ we can use a given rational
point~$P$ on the graph of $F=0$ to find all other rational points by
intersecting a line through~$P$ with the graph of $F=0$.
The graph of $y^2=x^3+ax+b$ looks like
\begin{center}
  [egg and curvy line] or [curvier line]
\end{center}
Notice that if~$P$ is a point on the graph of the curve, then a line
through~$P$ (usually) intersects the graph in exactly {\em two} other
points.  In general, these two other points usually do not have
rational coordinates.  However, if~$P$ and~$Q$ are rational points on
the graph of $y^2=x^3+ax+b$ and~$L$ is the line through~$P$ and~$Q$,
then the third point of intersection with the graph will have rational
coordinates.  Explicitly, if $P=(x_1,y_1)$ and $Q=(x_2,y_2)$ then the
third point of intersection has coordinates\footnote{It is traditional
  in a course like ours for me to derive these formulas.  I'm not going
  to, because it's simple algebra and once you see the geometric picture
  it is easy to carry out.  You should do this as an exercise, or read
  the derivation in [Kato et al.] or [Davenport].}
$$
  x_3 = \left(\frac{y_2 - y_1}{x_2-x_1}\right)^2 - x_1 - x_2, \qquad
  y_3 = \frac{y_2-y_1}{x_2-x_1} x_3 - \frac{y_2 x_1 - y_1 x_2}{x_2-x_1}.
$$
Thus, given two points on $E$, we can find another.  Also, given a single
point, we can draw the tangent line to $E$ through that point and obtain
a third point.

\subsection{To Infinity!}
At first glance, the above construction doesn't work if $x_1=x_2$.
  [draw picture].  Fortunately, there is a natural sense in which the
graph of~$E$ is missing one point, and when $x_1=x_2$ this one
missing point is the third point of intersection.

The graph of~$E$ that we drew above is a graph in the plan $\R^2$.
The plane is a subset of the projective plane $\P^2$, which I will
define in just a moment. The closure of the graph of $y^2 = x^3 +ax+b$
in $\P^2$ has exactly one extra point, which has rational coordinates,
and which we denote by~$\infty$.
Formally, $\P^2$ can be viewed as the set of triples $(a,b,c)$
with $a,b,c$ not all~$0$ modulo the equivalence relation
$$(a,b,c)\sim (\lambda a ,\lambda b , \lambda c)$$
for any nonzero $\lambda$.
Denote by $(a:b:c)$ the equivalence class of $(a,b,c)$.
The closure of the graph of $y^2 = x^3 + ax+b$ is the
graph of
$
  y^2 z = x^3 + axz^2 + bz^3
$
and the extra point $\infty$ is $(0:1:0)$.

\hd{Venerable Problem:} Find an algorithm that, given
an elliptic curve~$E$ over~$\Q$, outputs a complete description
of the set of rational points $(x_0, y_0)$ on~$E$.

This problem is difficult.  In fact, so far it has stumped everyone!
There is a conjectural algorithm, but nobody has succeeded in proving
that it is really an algorithm, in the sense that it terminates for
any input curve~$E$.  Several of your profs at Harvard, including
Barry Mazur, myself, and Christophe Cornut (who will teach Math 129
next semester) have spent, or will probably spend, a huge chunk of
their life thinking about this problem. (Am I being overly pessimistic?)

How could one possible ``describe'' the set of rational points on~$E$
in the first place?  In 1923, Louis Mordell proved an amazing theorem,
which implies that there is a reasonable way to describe the rational
points on~$E$.  To state his theorem, we introduce the ``group law''
on~$E$.

\section{The Group Law}
Consider the set
$
  E(\Q) = \{\infty\} \union
  \{ (x_0, y_0) : y_0^2 = x_0^3 + ax_0 + b\}.
$
There is a natural way to endow the set $E(\Q)$ with a {\em group}
structure.  Here's how it works.  First, the element $\infty\in E(\Q)$
is the $0$ element of the group.  Next, suppose $P$ and $Q$ are elements of
$E(\Q)$.  Just like we did earlier, draw the line through $P$ and $Q$
and let $R=(x_3,y_3)$ be the third point of intersection.  Define
$
  P + Q = (x_3, -y_3).
$
There are various special cases to consider, such as when $P=Q$ or
the third point of intersection is $\infty$, but I will let your read about
them in [Kato et al.].  It is clear that this binary operation on $E(\Q)$
satisfies $P+Q = Q+P$.  Also, the inverse of $P=(x_1,y_1)$ is
$-P=(x_1,-y_1)$.  The only other axiom to check in order to
verify that $+$ gives $E(\Q)$ an abelian group structure is the associative
law.  This is simple but {\em very tedious} to check using only elementary
methods\footnote{The right way to prove that the associate law holds is to
  develop the theory of algebraic curves and define the group law in
  terms of divisors; this is way outside the scope of this course.}.
Fortunately, we can coerce the computer algebra system MAGMA
into verifying the associative law for us:
\begin{verbatim}
// The field K = Q(a,b,x0,x1,x2) 
K<a,b,x0,x1,x2> := FieldOfFractions(PolynomialRing(Rationals(),5));
// The polynomial ring R = K[y0,y1,y2]
R<y0,y1,y2> := PolynomialRing(K,3);
// A maximal ideal of R.
I := ideal<R | y0^2 - (x0^3+a*x0+b), y1^2 - (x1^3+a*x1+b), y2^2-(x2^3+a*x2+b)>;
// The field L contains three distinct "generic" points on E.
L  := quo<R|I>;
E  := EllipticCurve([L| a,b]);  // The elliptic curve y^2 = x^3 + a*x + b.
P0 := E![L|x0,y0]; P1 := E![L|x1,y1]; P2 := E![L|x2,y2];
lhs := (P0 + P1) + P2;  rhs := P0 + (P1 + P2);
lhs eq rhs;
true     // yeah!
\end{verbatim}


\section{Mordell's Theorem}
\begin{theorem}[Mordell]
  The group $E(\Q)$ is finitely generated.
\end{theorem}
This means that there are points $P_1,\ldots, P_r \in E(\Q)$
such that every element of $E(\Q)$ is of the form
$
  n_1 P_1 + \cdots + n_r P_r
$
for some $n_1, \ldots n_r\in\Z$.  I won't prove Mordell's theorem in
this course.  You can find an elementary proof of most of it in \S1.3
of [Kato et al.].\footnote{Matt Baker is teaching a graduate course
  (255r) this semester, and he is just about to present a proof of
  Weil's generalization of Mordell's theorem.}

\begin{example}
  Consider the elliptic curve $E$ given by $y^2 = x^3 + x +1$.
  Then $E(\Q)\ncisom \Z$ with generator $(0,1)$.  We have
  $2(0,1)=(-1/4, -9/8)$,
  $3(0,1)=(72,611)$,
  and $4(0,1)=\left(-\frac{287}{1296}, \frac{40879}{46656}\right)$.
\end{example}






\chapter{The Elliptic Curve Group Law}




\section{Some Graphs}
Recall that an elliptic curve over a field~$K$ (in which $2$ and $3$
are invertible) can be defined by  an equation
$$y^2 = x^3 + ax +b$$
with $a, b\in K$.   Here are some examples over~$\Q$.

  {\large$$y^2 = x^3 + 1,\qquad E(\Q)\ncisom \Z/6\Z\vspace{1ex}$$}
\begin{center}
  \psset{unit=.5in}
  \pspicture(-2,-5)(5,5)
  \psgrid[gridcolor=gray]

  \pscircle*[linecolor=red](2,3){0.1}\rput(2.5,3){$(2,3)$}
  \pscircle*[linecolor=red](0,1){0.1}\rput(0.5,1){$(0,1)$}

  \psline[linewidth=0.03]{->}(-2,0)(5,0)\rput(5.2,0){$x$}
  \psline[linewidth=0.03]{->}(0,-5)(0,5)\rput(0,5.25){$y$}

  \pscurve[linecolor=blue]
  (2.8500,4.9141)
  (2.8000,4.7908)
  (2.7500,4.6687)
  (2.7000,4.5478)
  (2.6500,4.4282)
  (2.6000,4.3099)
  (2.5500,4.1930)
  (2.5000,4.0773)
  (2.4500,3.9631)
  (2.4000,3.8502)
  (2.3500,3.7387)
  (2.3000,3.6286)
  (2.2500,3.5200)
  (2.2000,3.4129)
  (2.1500,3.3073)
  (2.1000,3.2032)
  (2.0500,3.1008)
  (2.0000,3.0000)
  (1.9500,2.9008)
  (1.9000,2.8033)
  (1.8500,2.7077)
  (1.8000,2.6138)
  (1.7500,2.5217)
  (1.7000,2.4316)
  (1.6500,2.3435)
  (1.6000,2.2574)
  (1.5500,2.1734)
  (1.5000,2.0916)
  (1.4500,2.0121)
  (1.4000,1.9349)
  (1.3500,1.8602)
  (1.3000,1.7880)
  (1.2500,1.7184)
  (1.2000,1.6516)
  (1.1500,1.5877)
  (1.1000,1.5267)
  (1.0500,1.4688)
  (1.0000,1.4142)
  (0.95000,1.3628)
  (0.90000,1.3149)
  (0.85000,1.2704)
  (0.80000,1.2296)
  (0.75000,1.1924)
  (0.70000,1.1588)
  (0.65000,1.1289)
  (0.60000,1.1027)
  (0.55000,1.0799)
  (0.50000,1.0606)
  (0.45000,1.0445)
  (0.40000,1.0315)
  (0.35000,1.0212)
  (0.30000,1.0134)
  (0.25000,1.0077)
  (0.20000,1.0039)
  (0.15000,1.0016)
  (0.10000,1.0005)
  (0.050000,1.0000)
  (0,1.0000)
  (-0.050000,0.99993)
  (-0.10000,0.99950)
  (-0.15000,0.99831)
  (-0.20000,0.99599)
  (-0.25000,0.99215)
  (-0.30000,0.98640)
  (-0.35000,0.97832)
  (-0.40000,0.96747)
  (-0.45000,0.95335)
  (-0.50000,0.93541)
  (-0.55000,0.91303)
  (-0.60000,0.88543)
  (-0.65000,0.85169)
  (-0.70000,0.81055)
  (-0.75000,0.76034)
  (-0.80000,0.69857)
  (-0.85000,0.62118)
  (-0.90000,0.52057)
  (-0.95000,0.37765)
  (-1.0000,0)
  (-1.0000,0)
  (-0.95000,-0.37765)
  (-0.90000,-0.52057)
  (-0.85000,-0.62119)
  (-0.80000,-0.69857)
  (-0.75000,-0.76034)
  (-0.70000,-0.81055)
  (-0.65000,-0.85169)
  (-0.60000,-0.88544)
  (-0.55000,-0.91303)
  (-0.50000,-0.93541)
  (-0.45000,-0.95335)
  (-0.40000,-0.96747)
  (-0.35000,-0.97833)
  (-0.30000,-0.98641)
  (-0.25000,-0.99216)
  (-0.20000,-0.99599)
  (-0.15000,-0.99831)
  (-0.10000,-0.99950)
  (-0.050000,-0.99994)
  (0,-1.0000)
  (0.050000,-1.0000)
  (0.10000,-1.0005)
  (0.15000,-1.0016)
  (0.20000,-1.0039)
  (0.25000,-1.0077)
  (0.30000,-1.0134)
  (0.35000,-1.0212)
  (0.40000,-1.0315)
  (0.45000,-1.0445)
  (0.50000,-1.0606)
  (0.55000,-1.0799)
  (0.60000,-1.1027)
  (0.65000,-1.1289)
  (0.70000,-1.1588)
  (0.75000,-1.1924)
  (0.80000,-1.2296)
  (0.85000,-1.2704)
  (0.90000,-1.3149)
  (0.95000,-1.3628)
  (1.0000,-1.4142)
  (1.0500,-1.4688)
  (1.1000,-1.5267)
  (1.1500,-1.5877)
  (1.2000,-1.6516)
  (1.2500,-1.7184)
  (1.3000,-1.7880)
  (1.3500,-1.8602)
  (1.4000,-1.9349)
  (1.4500,-2.0121)
  (1.5000,-2.0916)
  (1.5500,-2.1734)
  (1.6000,-2.2574)
  (1.6500,-2.3435)
  (1.7000,-2.4316)
  (1.7500,-2.5217)
  (1.8000,-2.6138)
  (1.8500,-2.7077)
  (1.9000,-2.8034)
  (1.9500,-2.9008)
  (2.0000,-3.0000)
  (2.0500,-3.1008)
  (2.1000,-3.2032)
  (2.1500,-3.3073)
  (2.2000,-3.4129)
  (2.2500,-3.5200)
  (2.3000,-3.6286)
  (2.3500,-3.7387)
  (2.4000,-3.8502)
  (2.4500,-3.9631)
  (2.5000,-4.0773)
  (2.5500,-4.1930)
  (2.6000,-4.3100)
  (2.6500,-4.4282)
  (2.7000,-4.5478)
  (2.7500,-4.6687)
  (2.8000,-4.7908)
  (2.8500,-4.9141)
  \endpspicture
\end{center}

{\large$$y^2 = x^3 - 6x - 4\qquad\text{ and }\qquad{}y^2 = x^3 - 5x + 4\vspace{-3ex}$$}
{\large$$E(\Q)\ncisom (\Z/2\Z) \cross\Z\quad \text{(for both curves) }$$}

\begin{center}
  \psset{unit=0.5}
  \pspicture(-2.7000,-7.9000)(5.3000,7.9000)
  \psgrid[gridcolor=gray, subgriddiv=1]
  \pscircle*[linecolor=red](-2,0){0.12640}\rput(-1.3680,0.12640){$(-2,0)$}
  \pscircle*[linecolor=red](-1,1){0.12640}\rput(-0.36800,1.1264){$(-1,1)$}
  \psline[linewidth=0.03]{->}(-2.7000,0)(5.3000,0)    \rput(5.6160,0){$x$}
  \psline[linewidth=0.03]{->}(0,-7.9000)(0,7.9000)    \rput(0,8.4160){$y$}
  \pscurve[linecolor=blue]
  (4.5266,7.8482)
  (4.5000,7.7540)
  (4.4733,7.6599)
  (4.4466,7.5659)
  (4.4200,7.4720)
  (4.3933,7.3781)
  (4.3666,7.2844)
  (4.3400,7.1907)
  (4.3133,7.0971)
  (4.2866,7.0035)
  (4.2600,6.9100)
  (4.2333,6.8166)
  (4.2066,6.7232)
  (4.1800,6.6298)
  (4.1533,6.5365)
  (4.1266,6.4431)
  (4.1000,6.3498)
  (4.0733,6.2566)
  (4.0466,6.1633)
  (4.0200,6.0700)
  (3.9933,5.9766)
  (3.9666,5.8833)
  (3.9400,5.7899)
  (3.9133,5.6964)
  (3.8866,5.6029)
  (3.8600,5.5093)
  (3.8333,5.4156)
  (3.8066,5.3217)
  (3.7800,5.2278)
  (3.7533,5.1337)
  (3.7266,5.0394)
  (3.7000,4.9450)
  (3.6733,4.8503)
  (3.6466,4.7554)
  (3.6200,4.6602)
  (3.5933,4.5647)
  (3.5666,4.4690)
  (3.5400,4.3728)
  (3.5133,4.2763)
  (3.4866,4.1793)
  (3.4600,4.0818)
  (3.4333,3.9838)
  (3.4066,3.8853)
  (3.3800,3.7860)
  (3.3533,3.6861)
  (3.3266,3.5854)
  (3.3000,3.4838)
  (3.2733,3.3812)
  (3.2466,3.2775)
  (3.2200,3.1727)
  (3.1933,3.0665)
  (3.1666,2.9588)
  (3.1400,2.8494)
  (3.1133,2.7380)
  (3.0866,2.6245)
  (3.0600,2.5085)
  (3.0333,2.3895)
  (3.0066,2.2672)
  (2.9800,2.1409)
  (2.9533,2.0098)
  (2.9266,1.8729)
  (2.9000,1.7288)
  (2.8733,1.5755)
  (2.8466,1.4099)
  (2.8200,1.2270)
  (2.7933,1.0176)
  (2.7666,0.75980)
  (2.7400,0.36169)
  (2.7400,-0.36169)
  (2.7666,-0.75980)
  (2.7933,-1.0176)
  (2.8200,-1.2271)
  (2.8466,-1.4099)
  (2.8733,-1.5755)
  (2.9000,-1.7288)
  (2.9266,-1.8729)
  (2.9533,-2.0098)
  (2.9800,-2.1409)
  (3.0066,-2.2672)
  (3.0333,-2.3895)
  (3.0600,-2.5085)
  (3.0866,-2.6245)
  (3.1133,-2.7380)
  (3.1400,-2.8494)
  (3.1666,-2.9588)
  (3.1933,-3.0665)
  (3.2200,-3.1727)
  (3.2466,-3.2776)
  (3.2733,-3.3812)
  (3.3000,-3.4838)
  (3.3266,-3.5854)
  (3.3533,-3.6861)
  (3.3800,-3.7861)
  (3.4066,-3.8853)
  (3.4333,-3.9839)
  (3.4600,-4.0818)
  (3.4866,-4.1793)
  (3.5133,-4.2763)
  (3.5400,-4.3728)
  (3.5666,-4.4690)
  (3.5933,-4.5648)
  (3.6200,-4.6602)
  (3.6466,-4.7554)
  (3.6733,-4.8503)
  (3.7000,-4.9450)
  (3.7266,-5.0394)
  (3.7533,-5.1337)
  (3.7800,-5.2278)
  (3.8066,-5.3217)
  (3.8333,-5.4156)
  (3.8600,-5.5093)
  (3.8866,-5.6029)
  (3.9133,-5.6964)
  (3.9400,-5.7899)
  (3.9666,-5.8833)
  (3.9933,-5.9766)
  (4.0200,-6.0700)
  (4.0466,-6.1633)
  (4.0733,-6.2566)
  (4.1000,-6.3499)
  (4.1266,-6.4432)
  (4.1533,-6.5365)
  (4.1800,-6.6298)
  (4.2066,-6.7232)
  (4.2333,-6.8166)
  (4.2600,-6.9100)
  (4.2866,-7.0035)
  (4.3133,-7.0971)
  (4.3400,-7.1907)
  (4.3666,-7.2844)
  (4.3933,-7.3781)
  (4.4200,-7.4720)
  (4.4466,-7.5659)
  (4.4733,-7.6599)
  (4.5000,-7.7540)
  (4.5266,-7.8482)
  \psccurve[linecolor=blue]
  (-0.75333,0.30409)
  (-0.78000,0.45326)
  (-0.80666,0.56133)
  (-0.83333,0.64907)
  (-0.86000,0.72384)
  (-0.88667,0.78925)
  (-0.91333,0.84741)
  (-0.94000,0.89967)
  (-0.96667,0.94694)
  (-0.99333,0.98988)
  (-1.0200,1.0289)
  (-1.0466,1.0645)
  (-1.0733,1.0970)
  (-1.1000,1.1265)
  (-1.1266,1.1531)
  (-1.1533,1.1772)
  (-1.1800,1.1987)
  (-1.2066,1.2178)
  (-1.2333,1.2344)
  (-1.2600,1.2488)
  (-1.2866,1.2609)
  (-1.3133,1.2707)
  (-1.3400,1.2782)
  (-1.3666,1.2835)
  (-1.3933,1.2864)
  (-1.4200,1.2871)
  (-1.4466,1.2854)
  (-1.4733,1.2813)
  (-1.5000,1.2747)
  (-1.5266,1.2656)
  (-1.5533,1.2538)
  (-1.5800,1.2392)
  (-1.6066,1.2217)
  (-1.6333,1.2010)
  (-1.6600,1.1771)
  (-1.6866,1.1496)
  (-1.7133,1.1182)
  (-1.7400,1.0825)
  (-1.7666,1.0421)
  (-1.7933,0.99627)
  (-1.8200,0.94415)
  (-1.8466,0.88461)
  (-1.8733,0.81594)
  (-1.9000,0.73552)
  (-1.9266,0.63884)
  (-1.9533,0.51675)
  (-1.9800,0.34294)
  (-1.9800,-0.34294)
  (-1.9533,-0.51675)
  (-1.9266,-0.63885)
  (-1.9000,-0.73552)
  (-1.8733,-0.81594)
  (-1.8466,-0.88461)
  (-1.8200,-0.94416)
  (-1.7933,-0.99627)
  (-1.7666,-1.0421)
  (-1.7400,-1.0825)
  (-1.7133,-1.1182)
  (-1.6866,-1.1496)
  (-1.6600,-1.1771)
  (-1.6333,-1.2010)
  (-1.6066,-1.2217)
  (-1.5800,-1.2392)
  (-1.5533,-1.2538)
  (-1.5266,-1.2656)
  (-1.5000,-1.2747)
  (-1.4733,-1.2813)
  (-1.4466,-1.2854)
  (-1.4200,-1.2871)
  (-1.3933,-1.2864)
  (-1.3666,-1.2835)
  (-1.3400,-1.2782)
  (-1.3133,-1.2707)
  (-1.2866,-1.2609)
  (-1.2600,-1.2488)
  (-1.2333,-1.2344)
  (-1.2066,-1.2178)
  (-1.1800,-1.1987)
  (-1.1533,-1.1772)
  (-1.1266,-1.1531)
  (-1.1000,-1.1265)
  (-1.0733,-1.0970)
  (-1.0466,-1.0646)
  (-1.0200,-1.0289)
  (-0.99333,-0.98988)
  (-0.96667,-0.94694)
  (-0.94000,-0.89967)
  (-0.91333,-0.84742)
  (-0.88667,-0.78925)
  (-0.86000,-0.72384)
  (-0.83333,-0.64907)
  (-0.80666,-0.56133)
  (-0.78000,-0.45326)
  (-0.75333,-0.30409)
  \endpspicture
  \qquad\qquad
  \psset{unit=1.47}
  \pspicture(-3.4300,-5.3000)(4.0000,5.3000)
  \psgrid[gridcolor=gray, subgriddiv=1]
  \pscircle*[linecolor=red](1,0){0.084800}\rput(1.4240,0.084800){$(1,0)$}
  \pscircle*[linecolor=red](0,2){0.084800}\rput(0.42400,2.0848){$(0,2)$}
  \psline[linewidth=0.03]{->}(-3.4300,0)(4.0000,0)\rput(4.4120,0){$x$}
  \psline[linewidth=0.03]{->}(0,-5.3000)(0,5.3000)\rput(0,5.7120){$y$}
  \pscurve[linecolor=blue]
  (3.4551,5.2888)
  (3.4303,5.2167)
  (3.4056,5.1449)
  (3.3808,5.0733)
  (3.3560,5.0019)
  (3.3313,4.9308)
  (3.3065,4.8598)
  (3.2817,4.7891)
  (3.2570,4.7186)
  (3.2322,4.6483)
  (3.2074,4.5782)
  (3.1827,4.5084)
  (3.1579,4.4388)
  (3.1331,4.3693)
  (3.1084,4.3002)
  (3.0836,4.2312)
  (3.0588,4.1625)
  (3.0341,4.0939)
  (3.0093,4.0256)
  (2.9845,3.9576)
  (2.9598,3.8897)
  (2.9350,3.8220)
  (2.9102,3.7546)
  (2.8855,3.6874)
  (2.8607,3.6204)
  (2.8359,3.5537)
  (2.8112,3.4871)
  (2.7864,3.4208)
  (2.7616,3.3547)
  (2.7369,3.2888)
  (2.7121,3.2231)
  (2.6873,3.1577)
  (2.6626,3.0924)
  (2.6378,3.0274)
  (2.6130,2.9625)
  (2.5883,2.8979)
  (2.5635,2.8335)
  (2.5387,2.7693)
  (2.5140,2.7053)
  (2.4892,2.6415)
  (2.4644,2.5779)
  (2.4397,2.5145)
  (2.4149,2.4513)
  (2.3901,2.3882)
  (2.3654,2.3254)
  (2.3406,2.2627)
  (2.3158,2.2002)
  (2.2911,2.1379)
  (2.2663,2.0757)
  (2.2415,2.0137)
  (2.2167,1.9518)
  (2.1920,1.8901)
  (2.1672,1.8284)
  (2.1424,1.7669)
  (2.1177,1.7055)
  (2.0929,1.6442)
  (2.0681,1.5829)
  (2.0434,1.5216)
  (2.0186,1.4603)
  (1.9938,1.3991)
  (1.9691,1.3377)
  (1.9443,1.2762)
  (1.9195,1.2146)
  (1.8948,1.1528)
  (1.8700,1.0906)
  (1.8452,1.0280)
  (1.8205,0.96497)
  (1.7957,0.90116)
  (1.7709,0.83642)
  (1.7462,0.77049)
  (1.7214,0.70293)
  (1.6966,0.63318)
  (1.6719,0.56033)
  (1.6471,0.48287)
  (1.6223,0.39803)
  (1.5976,0.29944)
  (1.5728,0.16365)
  (1.5728,-0.16365)
  (1.5976,-0.29944)
  (1.6223,-0.39803)
  (1.6471,-0.48288)
  (1.6719,-0.56033)
  (1.6966,-0.63318)
  (1.7214,-0.70293)
  (1.7462,-0.77049)
  (1.7709,-0.83643)
  (1.7957,-0.90116)
  (1.8205,-0.96497)
  (1.8452,-1.0280)
  (1.8700,-1.0906)
  (1.8948,-1.1528)
  (1.9195,-1.2146)
  (1.9443,-1.2763)
  (1.9691,-1.3377)
  (1.9938,-1.3991)
  (2.0186,-1.4603)
  (2.0434,-1.5216)
  (2.0681,-1.5829)
  (2.0929,-1.6442)
  (2.1177,-1.7055)
  (2.1424,-1.7669)
  (2.1672,-1.8284)
  (2.1920,-1.8901)
  (2.2167,-1.9518)
  (2.2415,-2.0137)
  (2.2663,-2.0757)
  (2.2911,-2.1379)
  (2.3158,-2.2002)
  (2.3406,-2.2627)
  (2.3654,-2.3254)
  (2.3901,-2.3882)
  (2.4149,-2.4513)
  (2.4397,-2.5145)
  (2.4644,-2.5779)
  (2.4892,-2.6415)
  (2.5140,-2.7053)
  (2.5387,-2.7693)
  (2.5635,-2.8335)
  (2.5883,-2.8979)
  (2.6130,-2.9626)
  (2.6378,-3.0274)
  (2.6626,-3.0924)
  (2.6873,-3.1577)
  (2.7121,-3.2231)
  (2.7369,-3.2888)
  (2.7616,-3.3547)
  (2.7864,-3.4208)
  (2.8112,-3.4871)
  (2.8359,-3.5537)
  (2.8607,-3.6205)
  (2.8855,-3.6874)
  (2.9102,-3.7546)
  (2.9350,-3.8221)
  (2.9598,-3.8897)
  (2.9845,-3.9576)
  (3.0093,-4.0256)
  (3.0341,-4.0939)
  (3.0588,-4.1625)
  (3.0836,-4.2312)
  (3.1084,-4.3002)
  (3.1331,-4.3694)
  (3.1579,-4.4388)
  (3.1827,-4.5084)
  (3.2074,-4.5782)
  (3.2322,-4.6483)
  (3.2570,-4.7186)
  (3.2817,-4.7891)
  (3.3065,-4.8598)
  (3.3313,-4.9308)
  (3.3560,-5.0019)
  (3.3808,-5.0733)
  (3.4056,-5.1449)
  (3.4303,-5.2167)
  (3.4551,-5.2888)


  \psccurve[linecolor=blue]
  (0.97846,0.21086)
  (0.95369,0.31456)
  (0.92892,0.39616)
  (0.90416,0.46728)
  (0.87939,0.53207)
  (0.85462,0.59251)
  (0.82986,0.64977)
  (0.80509,0.70454)
  (0.78032,0.75731)
  (0.75555,0.80841)
  (0.73079,0.85809)
  (0.70602,0.90653)
  (0.68125,0.95388)
  (0.65649,1.0002)
  (0.63172,1.0456)
  (0.60695,1.0903)
  (0.58219,1.1341)
  (0.55742,1.1773)
  (0.53265,1.2197)
  (0.50789,1.2615)
  (0.48312,1.3027)
  (0.45835,1.3433)
  (0.43359,1.3833)
  (0.40882,1.4227)
  (0.38405,1.4616)
  (0.35929,1.4999)
  (0.33452,1.5377)
  (0.30975,1.5751)
  (0.28499,1.6118)
  (0.26022,1.6481)
  (0.23545,1.6839)
  (0.21069,1.7192)
  (0.18592,1.7540)
  (0.16115,1.7884)
  (0.13639,1.8222)
  (0.11162,1.8556)
  (0.086857,1.8884)
  (0.062090,1.9208)
  (0.037323,1.9528)
  (0.012556,1.9842)
  (-0.012210,2.0152)
  (-0.036976,2.0456)
  (-0.061743,2.0756)
  (-0.086510,2.1052)
  (-0.11127,2.1342)
  (-0.13604,2.1628)
  (-0.16081,2.1908)
  (-0.18557,2.2184)
  (-0.21034,2.2455)
  (-0.23511,2.2721)
  (-0.25987,2.2982)
  (-0.28464,2.3238)
  (-0.30941,2.3489)
  (-0.33417,2.3735)
  (-0.35894,2.3976)
  (-0.38371,2.4211)
  (-0.40847,2.4442)
  (-0.43324,2.4667)
  (-0.45801,2.4887)
  (-0.48277,2.5102)
  (-0.50754,2.5312)
  (-0.53231,2.5516)
  (-0.55708,2.5714)
  (-0.58184,2.5908)
  (-0.60661,2.6095)
  (-0.63138,2.6277)
  (-0.65614,2.6454)
  (-0.68091,2.6624)
  (-0.70568,2.6789)
  (-0.73044,2.6949)
  (-0.75521,2.7102)
  (-0.77998,2.7249)
  (-0.80474,2.7390)
  (-0.82951,2.7526)
  (-0.85428,2.7654)
  (-0.87904,2.7777)
  (-0.90381,2.7894)
  (-0.92858,2.8004)
  (-0.95334,2.8107)
  (-0.97811,2.8204)
  (-1.0028,2.8294)
  (-1.0276,2.8377)
  (-1.0524,2.8454)
  (-1.0771,2.8523)
  (-1.1019,2.8586)
  (-1.1267,2.8641)
  (-1.1514,2.8689)
  (-1.1762,2.8729)
  (-1.2010,2.8762)
  (-1.2257,2.8787)
  (-1.2505,2.8804)
  (-1.2753,2.8813)
  (-1.3000,2.8814)
  (-1.3248,2.8807)
  (-1.3496,2.8792)
  (-1.3743,2.8767)
  (-1.3991,2.8734)
  (-1.4239,2.8692)
  (-1.4486,2.8641)
  (-1.4734,2.8580)
  (-1.4982,2.8509)
  (-1.5229,2.8429)
  (-1.5477,2.8339)
  (-1.5725,2.8238)
  (-1.5972,2.8127)
  (-1.6220,2.8004)
  (-1.6468,2.7871)
  (-1.6715,2.7725)
  (-1.6963,2.7568)
  (-1.7211,2.7399)
  (-1.7458,2.7217)
  (-1.7706,2.7022)
  (-1.7954,2.6813)
  (-1.8201,2.6590)
  (-1.8449,2.6353)
  (-1.8697,2.6100)
  (-1.8944,2.5832)
  (-1.9192,2.5547)
  (-1.9440,2.5245)
  (-1.9687,2.4925)
  (-1.9935,2.4586)
  (-2.0183,2.4227)
  (-2.0430,2.3847)
  (-2.0678,2.3445)
  (-2.0926,2.3020)
  (-2.1173,2.2570)
  (-2.1421,2.2092)
  (-2.1669,2.1586)
  (-2.1916,2.1049)
  (-2.2164,2.0478)
  (-2.2412,1.9870)
  (-2.2659,1.9221)
  (-2.2907,1.8528)
  (-2.3155,1.7783)
  (-2.3402,1.6981)
  (-2.3650,1.6113)
  (-2.3898,1.5166)
  (-2.4145,1.4125)
  (-2.4393,1.2967)
  (-2.4641,1.1656)
  (-2.4888,1.0133)
  (-2.5136,0.82820)
  (-2.5384,0.57935)
  (-2.5384,-0.57935)
  (-2.5136,-0.82820)
  (-2.4888,-1.0133)
  (-2.4641,-1.1656)
  (-2.4393,-1.2967)
  (-2.4145,-1.4125)
  (-2.3898,-1.5166)
  (-2.3650,-1.6113)
  (-2.3402,-1.6982)
  (-2.3155,-1.7783)
  (-2.2907,-1.8528)
  (-2.2659,-1.9222)
  (-2.2412,-1.9870)
  (-2.2164,-2.0478)
  (-2.1916,-2.1049)
  (-2.1669,-2.1586)
  (-2.1421,-2.2092)
  (-2.1173,-2.2570)
  (-2.0926,-2.3020)
  (-2.0678,-2.3446)
  (-2.0430,-2.3847)
  (-2.0183,-2.4227)
  (-1.9935,-2.4586)
  (-1.9687,-2.4925)
  (-1.9440,-2.5245)
  (-1.9192,-2.5547)
  (-1.8944,-2.5832)
  (-1.8697,-2.6100)
  (-1.8449,-2.6353)
  (-1.8201,-2.6590)
  (-1.7954,-2.6813)
  (-1.7706,-2.7022)
  (-1.7458,-2.7217)
  (-1.7211,-2.7399)
  (-1.6963,-2.7568)
  (-1.6715,-2.7725)
  (-1.6468,-2.7871)
  (-1.6220,-2.8004)
  (-1.5972,-2.8127)
  (-1.5725,-2.8238)
  (-1.5477,-2.8339)
  (-1.5229,-2.8429)
  (-1.4982,-2.8509)
  (-1.4734,-2.8580)
  (-1.4486,-2.8641)
  (-1.4239,-2.8692)
  (-1.3991,-2.8734)
  (-1.3743,-2.8767)
  (-1.3496,-2.8792)
  (-1.3248,-2.8807)
  (-1.3000,-2.8815)
  (-1.2753,-2.8813)
  (-1.2505,-2.8804)
  (-1.2257,-2.8787)
  (-1.2010,-2.8762)
  (-1.1762,-2.8729)
  (-1.1514,-2.8689)
  (-1.1267,-2.8641)
  (-1.1019,-2.8586)
  (-1.0771,-2.8523)
  (-1.0524,-2.8454)
  (-1.0276,-2.8377)
  (-1.0028,-2.8294)
  (-0.97811,-2.8204)
  (-0.95334,-2.8107)
  (-0.92858,-2.8004)
  (-0.90381,-2.7894)
  (-0.87904,-2.7777)
  (-0.85428,-2.7655)
  (-0.82951,-2.7526)
  (-0.80474,-2.7390)
  (-0.77998,-2.7249)
  (-0.75521,-2.7102)
  (-0.73044,-2.6949)
  (-0.70568,-2.6789)
  (-0.68091,-2.6625)
  (-0.65614,-2.6454)
  (-0.63138,-2.6277)
  (-0.60661,-2.6095)
  (-0.58184,-2.5908)
  (-0.55708,-2.5714)
  (-0.53231,-2.5516)
  (-0.50754,-2.5312)
  (-0.48277,-2.5102)
  (-0.45801,-2.4887)
  (-0.43324,-2.4667)
  (-0.40847,-2.4442)
  (-0.38371,-2.4211)
  (-0.35894,-2.3976)
  (-0.33417,-2.3735)
  (-0.30941,-2.3489)
  (-0.28464,-2.3238)
  (-0.25987,-2.2982)
  (-0.23511,-2.2721)
  (-0.21034,-2.2455)
  (-0.18557,-2.2184)
  (-0.16081,-2.1908)
  (-0.13604,-2.1628)
  (-0.11127,-2.1342)
  (-0.086510,-2.1052)
  (-0.061743,-2.0756)
  (-0.036976,-2.0456)
  (-0.012210,-2.0152)
  (0.012556,-1.9842)
  (0.037323,-1.9528)
  (0.062090,-1.9208)
  (0.086857,-1.8884)
  (0.11162,-1.8556)
  (0.13639,-1.8222)
  (0.16115,-1.7884)
  (0.18592,-1.7540)
  (0.21069,-1.7192)
  (0.23545,-1.6839)
  (0.26022,-1.6481)
  (0.28499,-1.6118)
  (0.30975,-1.5751)
  (0.33452,-1.5378)
  (0.35929,-1.4999)
  (0.38405,-1.4616)
  (0.40882,-1.4227)
  (0.43359,-1.3833)
  (0.45835,-1.3433)
  (0.48312,-1.3027)
  (0.50789,-1.2615)
  (0.53265,-1.2197)
  (0.55742,-1.1773)
  (0.58219,-1.1341)
  (0.60695,-1.0903)
  (0.63172,-1.0457)
  (0.65649,-1.0002)
  (0.68125,-0.95388)
  (0.70602,-0.90653)
  (0.73079,-0.85809)
  (0.75555,-0.80841)
  (0.78032,-0.75731)
  (0.80509,-0.70454)
  (0.82986,-0.64977)
  (0.85462,-0.59252)
  (0.87939,-0.53207)
  (0.90416,-0.46729)
  (0.92892,-0.39616)
  (0.95369,-0.31456)
  (0.97846,-0.21086)
  \endpspicture
\end{center}
\vspace{2ex}

\noindent(Exercise: Add the indicated points.)

\section{The Point $\O$ at Infinity}
The graphs of the previous section are each missing
a point at infinity.
They are graphs in the plane $\R^2$.
The plane is a subset of the projective plane $\P^2$.
The ``closure'' of the graph of $y^2 = x^3 +ax+b$
in $\P^2$ has exactly one extra point~$\O$, which has
rational coordinates, and which we sometimes call ``the point at infinity''.
\begin{definition}
  The {\em projective plane} $\P^2$ is the set of triples $(a,b,c)$,
  with $a,b,c$ not all~$0$, modulo the equivalence relation
  $$(a,b,c)\sim (\lambda a ,\lambda b , \lambda c)$$
  for any nonzero $\lambda$.
  We denote by $(a\!:\!b\!:\!c)$ the equivalence class of $(a,b,c)$.
\end{definition}

The ``closure'' in $\P^2$ of the graph of $y^2 = x^3 + ax+b$ is the
graph of
$$
  y^2 z = x^3 + axz^2 + bz^3
$$
and the extra point is $\O=(0\!:\!1\!:\!0)$.
All finite points are of the form $(a\!:\!b\!:\!1)$.

For more about the projective plane, see page 28 of [Kato et al.].

\section{The Group Law is a Group Law}
Let $E$ be an elliptic curve of the form $y^2 = x^3 +ax+b$
over a field~$K$.
Consider the set
$$
  E(K) = \{\O\} \union
  \left\{ (x, y)\in K\cross K\,:\, y^2 = x^3 + ax + b\right\}.
$$
Recall from the last lecture that there is
a natural way to endow the set $E(K)$ with a {\em group}
structure.  Here's how it works.  First, the element $\O\in E(K)$
is the zero element of the group.  Next, suppose~$P$ and~$Q$
are elements of $E(K)$.  Just like we did earlier,
let $R=(x_3,y_3)$ be the third point of intersection
of $E$ and the line determined by~$P$ and~$Q$ (try this
with the graphs on pages 1 and 2).
Define
$$
  P + Q = (x_3, -y_3).
$$
(For what goes wrong if you try to define $P+Q=(x_3,y_3)$, see
your homework assignment.)
There are various special cases to consider, such as when $P=Q$ or
the third point of intersection is~$\O$, but I will let you read about
them in [Kato et al.].

It is not surprising that this binary operation on $E(K)$
satisfies $P+Q = Q+P$.  Also, the inverse of $P=(x_1,y_1)$ is
$-P=(x_1,-y_1)$.  The only other axiom to check in order to
verify that $+$ gives $E(K)$ an abelian group structure is the associative
law.  This is simple but {\em tedious} to check using only elementary
methods.  The right way to prove that the associate law holds is to
develop the theory of algebraic curves and define the group law in
terms of divisor classes, but this is outside the scope of this course.
For fun, we can coerce the amazingly cool (but complicated)
computer algebra system {\sc Magma} into verifying
the associative law (over~$\Q$) for us:
\begin{verbatim}
// Define the field K = Q(a,b,x0,x1,x2) 
K<a,b,x0,x1,x2> := FieldOfFractions(PolynomialRing(Rationals(),5));
// Define the polynomial ring R = K[y0,y1,y2]
R<y0,y1,y2> := PolynomialRing(K,3);
// Define a maximal ideal of R:
I := ideal<R | y0^2 - (x0^3+a*x0+b), 
               y1^2 - (x1^3+a*x1+b), 
               y2^2 - (x2^3+a*x2+b)>;
// The quotient L = R/I is a field that contains three 
// distinct "generic" points on E.
L  := quo<R|I>;
// Define the elliptic curve y^2 = x^3 + a*x + b over L.
E  := EllipticCurve([L| a,b]);  
// Let P0, P1, and P2 be three distinct "generic" points on E.
P0 := E![L|x0,y0]; P1 := E![L|x1,y1]; P2 := E![L|x2,y2];
// The algebraic formulas for the group law are built into MAGMA.
lhs := (P0 + P1) + P2;  rhs := P0 + (P1 + P2);
// Verify the associative law.
lhs eq rhs;
true     // Yeah, it works!
\end{verbatim}

\section{An Example Over a Finite Field}
Let~$E$ be the elliptic curve $y^2 = x^3 + 3x + 3$ over the finite
field
$$
  K=\Z/5\Z = \{0, 1, 2, 3, 4\}.
$$
First, we find all points on $E$ using PARI:
\begin{verbatim}
? for(x=0,4, for(y=0,4, if((y^2-(x^3+3*x+3))%5==0, print1([x,y],"  "))))
[3, 2]  [3, 3]  [4, 2]  [4, 3]
\end{verbatim}
Thus
$
  E(K) = \left\{\O, (3,2), (3,3), (4,2), (4,3)\right\},
$
so $E(K)$ must be a cyclic abelian group of order~$5$.
Let's verify that $E(K)$ is generated by $(3,2)$.
\begin{verbatim}
? e = ellinit([0,0,0,Mod(3,5),Mod(3,5)])
? ?ellpow    \\ type ?5 for a complete list of elliptic-curve functions
ellpow(e,x,n): n times the point x on elliptic curve e (n in Z).
? x = [3,2];
? for(n=1,5,print(n,"*[3,2] =  ",lift(ellpow(e,x,n))))
1*[3,2] =  [3, 2]
2*[3,2] =  [4, 3]
3*[3,2] =  [4, 2]
4*[3,2] =  [3, 3]
5*[3,2] =  [0]
\end{verbatim}

\section{Mordell's Theorem}
\hd{Venerable Problem:} {\em Find an algorithm that, given
  an elliptic curve~$E$ over~$\Q$, outputs a complete description
  of the set of rational points $(x_0, y_0)$ on~$E$.}\vspace{2ex}

This problem is difficult.  In fact, so far it has stumped everyone!
There is a {\em conjectural algorithm}, but nobody has succeeded in proving
that it is really an algorithm, in the sense that it terminates for
any input curve~$E$.  Several of your profs at Harvard, including
Barry Mazur, myself, and Christophe Cornut (who will teach Math 129
next semester) have spent, or might spend, a huge chunk of
their life thinking about this problem.

How could one possible ``describe'' the group $E(\Q)$, since it can be
infinite?  In 1923, Mordell proved that there is
always a reasonable way to describe $E(\Q)$.

\begin{theorem}[Mordell]
  The group $E(\Q)$ is finitely generated.
\end{theorem}
This means that there are points $P_1,\ldots, P_s \in E(\Q)$
such that every element of $E(\Q)$ is of the form
$
  n_1 P_1 + \cdots + n_s P_s
$
for some $n_1, \ldots n_s\in\Z$.  I will not prove Mordell's theorem in
this course, but see \S1.3 of [Kato et al.].


\begin{example}
  Consider the elliptic curve $E$ given by $y^2 = x^3 -6x - 4$.
  Then $E(\Q)\ncisom (\Z/2\Z)\cross \Z$ with generators $(-2,0)$ and
  $(-1,1)$.  We have
  $$5(-1,1) = \left(-\frac{131432401}{121462441} , -\frac{1481891884199}{1338637562261}\right).$$
  Trying finding that point without knowing about the group law!
\end{example}




$(0,1)+(0,1)=(-1/4, -9/8)$,
$(0,1)+(0,1)+(0,1)=(72,611)$,
and $(0,1)+(0,1)+(0,1)+(0,1)=\left(-\frac{287}{1296}, \frac{40879}{46656}\right)$.


\chapter{Torsion Points on Elliptic Curves and Mazur's Big Theorem}



\section{Mordell's Theorem}
\hd{Venerable Problem:} {\em Find an algorithm that, given
  an elliptic curve~$E$ over~$\Q$, outputs a complete description
  of the set of rational points $(x_0, y_0)$ on~$E$.}\vspace{2ex}

This problem is difficult.  In fact, so far it has stumped everyone!
There is a {\em conjectural algorithm}, but nobody has succeeded in proving
that it is really an algorithm, in the sense that it terminates for
any input curve~$E$.  Several of your profs at Harvard, including
Barry Mazur, myself, and Christophe Cornut (who will teach Math 129
next semester) have spent, or might spend, a huge chunk of
their life thinking about variants of this problem.

How could one possible ``describe'' the group $E(\Q)$, since it can be
infinite?  In 1923, Mordell proved that there is
always a reasonable way to describe $E(\Q)$.

\begin{theorem}[Mordell]
  The group $E(\Q)$ is finitely generated.
\end{theorem}
This means that there are points $P_1,\ldots, P_s \in E(\Q)$
such that every element of $E(\Q)$ is of the form
$
  n_1 P_1 + \cdots + n_s P_s
$
for some $n_1, \ldots n_s\in\Z$.  I will not prove Mordell's theorem in
this course.  See \S1.3 of [Kato et al.] for a proof
in the special case when $E$ is given
by an equation of the form $y^2 = (x-a)(x-b)(x-c)$.


\begin{example}
  Consider the elliptic curve $E$ given by $y^2 = x^3 -6x - 4$.
  Then $E(\Q)\ncisom (\Z/2\Z)\cross \Z$ with generators $(-2,0)$ and
  $(-1,1)$.  We have
  $$5(-1,1) = \left(-\frac{131432401}{121462441} , -\frac{1481891884199}{1338637562261}\right).$$
  Trying finding that point without knowing about the group law!
\end{example}

\section{Exploring the Possibilities}
As~$E$ varies over all elliptic curves over~$\Q$, what
are the possibilities for $E(\Q)$?  What finitely generated
abelian groups occur?  Mordell's theorem implies that
$$
  E(\Q) \ncisom \Z^r \oplus E(\Q)_{\tor},
$$
where $E(\Q)_{\tor}$ is the set of points of finite
order in $E(\Q)$ and $\Z^r\ncisom E(\Q)/E(\Q)_{\tor}$.
The number~$r$ is called the {\em rank} of~$E$.

\subsection{The Torsion Subgroup}
\begin{theorem}[Mazur, April 16, 1976]
  Let~$E$ be an elliptic curve over~$\Q$.  Then $E(\Q)_{\tor}$ is
  isomorphic to one of the following 15 groups:
  \begin{align*}
    \Z/n\Z                   & \qquad\text{ for } n\leq 10 \text{ or } n=12, \\
    (\Z/2\Z)\cross (\Z/2n\Z) & \qquad \text{ for } n \leq 4.
  \end{align*}
\end{theorem}
As we will see in the next section, all of these torsion subgroups really
do occur.   Mazur's theorem is very deep, and I can barely begin to
hint at how he proved it.  The basic idea is to define, for each
positive integer~$N$, a curve $Y_1(N)$ with the
magnificient property that
the points of $Y_1(N)$ with complex coordinates are
in natural bijection with the (isomorphism classes of) pairs
$(E,P)$, where~$E$ is an elliptic curve and $P$ is a point
of $E$ of order~$N$. Moreover, $Y_1(N)$ is amazing in that
it has a rational point if and only if there is an elliptic curve
over $\Q$ with a rational point of order~$N$.  I won't
define $Y_1(N)$, but here it is for the first few~$N$:
\begin{center}
  \begin{tabular}{|l|l|}\hline
    $\quad N$    & A curve that contains $Y_1(N)$                                             \\\hline
    $1-10$, $12$ & a straight line; these have lots of points!                                \\\hline
    $11$         & $y^2 + y = x^3 - x^2$                                                      \\\hline
    $13$         & $y^2 = x^6 + 2x^5 + x^4 + 2x^3 + 6x^2 + 4x + 1$                            \\\hline
    $14$         & $y^2 +xy +y = x^3-x$                                                       \\\hline
    $15$         & $y^2+xy+y = x^3+x^2$                                                       \\\hline
    $16$         & $y^2 = (x - 1)(x + 1)(x^2 - 2x - 1)(x^2 + 1)$                              \\\hline
    $17$         & The intersection of the hypersurfaces in $\P^4$ defined by:                \\
                 & \hspace{.3in}$ac - b^2 + 5bd - 3be - c^2 - 4cd + 2ce - 4d^2 + 7de - 2e^2$, \\
                 & \hspace{.3in}$ad - bc + bd - be + c^2 - 2cd - 2d^2 + 4de - e^2$, and       \\
                 & \hspace{.3in}$ae - be - cd + 2d^2 - 2de + e^2$.                            \\\hline
    $18$         & $y^2 = x^6 + 4x^5 + 10x^4 + 10x^3 + 5x^2 + 2x + 1$                         \\\hline
  \end{tabular}
\end{center}
(Some of the curves in the right hand column have a few obvious rational
points, but these points ``don't count''.)

Mazur proved that if~$N=11$ or $N\geq 13$, then $Y_1(N)$ has no
rational points.   This result, together with the theory surrounding
$Y_1(N)$, yields his theorem.

\subsection{The Rank}
\begin{conjecture}\label{ref:rankconj}
  There exist elliptic curves over~$\Q$ of arbitrarily large rank.
\end{conjecture}
As far as I know, nobody has any real clue as to how to prove
Conjecture~\ref{ref:rankconj} (Doug Ulmer recently wrote a paper
which gives theoretical evidence).
The current ``world record'' is a curve of rank~$\geq 24$.  It was discovered
in January 2000 by Roland Martin and William McMillen of the
  {\bf National Security Agency}.  For security reasons, I won't tell
you anything about how they found it.
\begin{theorem}
  The elliptic curve
    {\tiny
      $$
        y^2 + xy + y = x^3 - 120039822036992245303534619191166796374x
        + 504224992484910670010801799168082726759443756222911415116
      $$
    }over $\Q$ has rank at least~$24$. The following points
  $P_1,...,P_{24}$ are independent points on the curve:
  {\tiny
  \begin{align*}
    P_1    & = (2005024558054813068, -16480371588343085108234888252)                                                                           \\
    P_2    & = (-4690836759490453344, -31049883525785801514744524804)                                                                          \\
    P_3    & = (4700156326649806635, -6622116250158424945781859743)                                                                            \\
    P_4    & = (6785546256295273860, -1456180928830978521107520473)                                                                            \\
    P_5    & = (6823803569166584943, -1685950735477175947351774817)                                                                            \\
    P_6    & = (7788809602110240789, -6462981622972389783453855713)                                                                            \\
    P_7    & = (27385442304350994620556, 4531892554281655472841805111276996)                                                                   \\
    P_8    & = (54284682060285253719/4, -296608788157989016192182090427/8)                                                                     \\
    P_9    & = (-94200235260395075139/25, -3756324603619419619213452459781/125)                                                                \\
    P_{10} & = (-3463661055331841724647/576, -439033541391867690041114047287793/13824)                                                         \\
    P_{11} & = (-6684065934033506970637/676, -473072253066190669804172657192457/17576)                                                         \\
    P_{12} & = (-956077386192640344198/2209,     -2448326762443096987265907469107661/103823)                                                   \\
    P_{13} & = (-27067471797013364392578/2809,    -4120976168445115434193886851218259/148877)                                                  \\
    P_{14} & = (-25538866857137199063309/3721,     -7194962289937471269967128729589169/226981)                                                 \\
    P_{15} & = (-1026325011760259051894331/108241,        -1000895294067489857736110963003267773/35611289)                                     \\
    P_{16} & = (9351361230729481250627334/1366561,        -2869749605748635777475372339306204832/1597509809)                                   \\
    P_{17} & = (10100878635879432897339615/1423249,        -5304965776276966451066900941489387801/1697936057)                                  \\
    P_{18} & = (11499655868211022625340735/17522596,        -1513435763341541188265230241426826478043/73349586856)                             \\
    P_{19} & = (110352253665081002517811734/21353641,        -461706833308406671405570254542647784288/98675175061)                             \\
    P_{20} & = (414280096426033094143668538257/285204544,        266642138924791310663963499787603019833872421/4816534339072)                  \\
    P_{21} & = (36101712290699828042930087436/4098432361,        -2995258855766764520463389153587111670142292/262377541318859)                 \\
    P_{22} & = (45442463408503524215460183165/5424617104,        -3716041581470144108721590695554670156388869/399533898943808)                 \\
    P_{23} & = (983886013344700707678587482584/141566320009,        -126615818387717930449161625960397605741940953/53264752602346277)          \\
    P_{24} & = (1124614335716851053281176544216033/152487126016,        -37714203831317877163580088877209977295481388540127/59545612760743936) \\
  \end{align*}
  }
\end{theorem}
\begin{proof}
  See
  \begin{verbatim}
    http://listserv.nodak.edu/scripts/wa.exe?A2=ind0005&L=nmbrthry&P=R182
\end{verbatim}
\end{proof}


\section{How to Compute $E(\Q)_{\tor}$}
The following theorem yields an algorithm to compute $E(\Q)_{\tor}$.
\begin{theorem}[Nagell-Lutz]\label{thm:nagell-lutz}
  Suppose that $y^2 = x^3 + ax + b$ (with $a,b\in\Z$) defines an elliptic
  curve~$E$ over~$\Q$, let $\Delta = -16(4a^3 + 27b^2)$ be the discriminant,
  and suppose
  that $P=(x,y)\in E(\Q)_{\tor}$. Then~$x$ and~$y$ are integers and
  either $y=0$, in which case $P$ has order~$2$, or $y^2 \mid \Delta$.
\end{theorem}
\begin{proof}[Non-proof]
  I will not prove this theorem.  However, you can find a readable
  proof in Chapter II of Silverman and Tate's {\em Rational Points on Elliptic Curves}.
\end{proof}

\hd{Warning:}
Nagell-Lutz is NOT an if and only if statement.  There are
points of infinite order that satisfy the conclusion
of Theorem~\ref{thm:nagell-lutz}.
For example, the point $(1,3)$ on $y^2 = x^3 + 8$ has integer coordinates
and $y^2 = 9 \mid \Delta = -16\cdot 27\cdot 3^2$.  However,
$$
  (1,3) + (1,3) = \left( -\frac{7}{4}, -\frac{13}{8}\right).
$$
Since the coordinates of $(1,3)+(1,3)$ are not integers, it
follows from the contrapositive (not converse!) of
Nagell-Lutz that $(1,3)$ must be a point of infinite order.

\begin{example}
  The following is a list of elliptic curves with each possible torsion
  subgroup. Tom Womack (a graduate student in Nottingham, where Robin
  Hood lives) has a web page, {\tt http://www.tom.womack.net/maths/torsion.htm},
  which contains PARI code that lists infinitely many elliptic
  curve with each torsion subgroup.
  \begin{center}
    \begin{tabular}{|l|l|}\hline
      Curve                               & $E(\Q)_{\tor}$            \\\hline
      $y^2 = x^3 - 2$                     & $\{0\}$                   \\
      $y^2 = x^3 + 8$                     & $\Z/2\Z$                  \\
      $y^2 = x^3 + 4$                     & $\Z/3\Z$                  \\
      $y^2 = x^3 + 4x$                    & $\Z/4\Z$                  \\
      $y^2 -y = x^3 - x^2$                & $\Z/5\Z$                  \\
      $y^2 = x^3 + 1$                     & $\Z/6\Z$                  \\
      $y^2 = x^3 -43x + 166$              & $\Z/7\Z$                  \\
      $y^2 + 7xy = x^3 +16x$              & $\Z/8\Z$                  \\
      $y^2 + xy +y = x^3 - x^2 - 14x +29$ & $\Z/9\Z$                  \\
      $y^2 + xy = x^3 -45x + 81$          & $\Z/10\Z$                 \\
      $y^2 + 43xy - 210y = x^3 - 210x^2$  & $\Z/12\Z$                 \\
      $y^2 = x^3 - 4x$                    & $(\Z/2\Z)\cross(\Z/2\Z)$  \\
      $y^2 = x^3 + 2x^2 - 3x$             & $(\Z/4\Z)\cross(\Z/2\Z)$  \\
      $y^2 + 5xy - 6y = x^3 - 3x^2$       & $(\Z/6\Z)\cross(\Z/2\Z)$  \\
      $y^2 + 17xy - 120y = x^3 -60x^2$    & $(\Z/8\Z)\cross (\Z/2\Z)$ \\
      \hline
    \end{tabular}
  \end{center}
\end{example}

The {\tt elltors} function in PARI computes torsion subgroups:
\begin{verbatim}
? ?elltors
elltors(e,{flag=0}): torsion subgroup of elliptic curve e: order, structure, 
generators. If flag = 0, use Doud's algorithm; if flag = 1, use Lutz-Nagell.
? e=ellinit([17,-60,-120,0,0]);
? elltors(e)
%4 = [16, [8, 2], [[30, -90], [-40, 400]]]
? e.disc
%5 = 51438240000
? e.disc % 90^2        \\ verify Nagell-Lutz
%6 = 0
? e.disc % 400^2       \\ verify Nagell-Lutz
%7 = 0
\end{verbatim}










\chapter{Computing with Elliptic Curves {\tiny (in PARI)}}

\section{Initializing Elliptic Curves}
We are concerned primarily with elliptic curves
$E$~given by an equation of the form
$$
  y^2 = x^3 + ax + b
$$
with~$a$ and~$b$ either rational
numbers or elements of a finite field $\Z/p\Z$.  If~$a$ and~$b$ are
in $\Q$, we initialize~$E$ in PARI using the following command:
\begin{verbatim}
? E = ellinit([0,0,0,a,b]);
\end{verbatim}
If you wish to view $a$ and $b$ as element of $\Z/p\Z$, initialize~$E$
as follows:
\begin{verbatim}
? E = ellinit([0,0,0,a,b]*Mod(1,p));
\end{verbatim}
If $\Delta = -16(4a^3 + 27b^2)= 0$ then
  {\tt ellinit} will complain; otherwise, {\tt ellinit} returns
a $19$-component vector of information about~$E$.  You can
access some of this information using the
dot notation, as shown below.
\begin{verbatim}
? E = ellinit([0,0,0,1,1]); 
? E.a4
%11 = 1
? E.a6
%12 = 1
? E.disc
%13 = -496
? E.j  
%14 = 6912/31
? E5 = ellinit([0,0,0,1,1]*Mod(1,5));
? E5.disc
%15 = Mod(4, 5)
? E5.j
%16 = Mod(2, 5)
\end{verbatim}
Here {\tt E.j} is the {\em $j$-invariant} of~$E$.  It is
equal to $ \frac{2^8 3^3 a^3}{4a^3 + 27b^2}$,
and has some remarkable properties that I probably
won't tell you about.

Most elliptic curves functions in PARI take as their first argument
the output of {\tt ellinit}.  For example, the function
  {\tt ellisoncurve(E,P)} takes the output of {\tt ellinit} as its
first argument and a point {\tt P=[x,y]}, and returns {\tt 1} if {\tt P}
lies on {\tt E} and {\tt 0} otherwise.
\begin{verbatim}
? P = [0,1]
? ellisoncurve(E, P)
%17 = 1
? P5 = [0,1]*Mod(1,5) 
? ellisoncurve(E5, P)
%18 = 1
\end{verbatim}


\section{Computing in The Group}
The following functions implement some basic arithmetic in
the group of points on an elliptic curve: {\tt elladd},
{\tt ellpow}, and {\tt ellorder}.
The {\tt elladd} function simply adds together two points
using the group law.  Warning: PARI does {\em not} check that the
two points are on the curve.
\begin{verbatim}
? P = [0,1]
%2 = [0, 1]
? elladd(E,P,P)
%3 = [1/4, -9/8]
? elladd(E,P,[1,0])    \\ nonsense, since [1,0] isn't even on E!!!
%4 = [0, -1]
? elladd(E5,P5,P5)
%12 = [Mod(4, 5), Mod(2, 5)]
? [1/4,-9/8]*Mod(1,5)
%13 = [Mod(4, 5), Mod(2, 5)]
\end{verbatim}
The {\tt ellpow} function computes $n P = P + P + \cdots + P$ ($n$ summands).
\begin{verbatim}
? ellpow(E,P,2)
%5 = [1/4, -9/8]
? ellpow(E,P,3)
%6 = [72, 611]
? ellpow(E,P,15)
\end{verbatim}
{\tiny \begin{verbatim}
%7 = [26449452347718826171173662182327682047670541792/9466094804586385762312509661837302961354550401, 
4660645813671121765025590267647300672252945873586541077711389394563791/920992883734992462745141522111225908861976098219465616585649245395649]
\end{verbatim}}


\section{The Generating Function $L(E,s)$}
Suppose~$E$ is an elliptic curve over~$\Q$ defined by an equation
$y^2=x^3 + ax + b$.  Then for every prime~$p$ that does not
divide $\Delta=-16(4a^3 + 27b^2)$, the same equation defines
an elliptic curve over the finite field $\Z/p\Z$.  As you will
discover in problem 3 of homework 9, it can be exciting to consider
the package of numbers $\# E(\Z/p\Z)$ of points on~$E$ over all
finite fields.  The function {\tt ellap} computes
$$
  a_p(E) = p+1 - \#E(\Z/p\Z).
$$
\begin{verbatim}
? E = ellinit([0,0,0,1,1]);
? ellap(E,5)
%19 = -3         \\ this should be 5+1 - #points
? E5 = ellinit([0,0,0,1,1]*Mod(1,5));
? for(x=0,4, for(y=0,4, if(ellisoncurve(E5,[x,y]),print([x,y]))))
[0, 1]
[0, 4]
[2, 1]
[2, 4]
[3, 1]
[3, 4]
[4, 2]
[4, 3]
? 5+1 - 9          \\ 8 points above, plus the point at infinity
%22 = -3
\end{verbatim}

There is a natural way to extend the definition of
$a_p$ to define integers $a_n$ for every integer~$n$.
For example, if $a_p$ and $a_q$ are defined as above
and~$p$ and~$q$ are distinct primes, then
$a_{pq}=a_p a_q$.
Today I won't tell you how to define the $a_p$ when,
e.g., $p\mid \Delta$.
However, you can compute the numbers $a_n$ quickly
in PARI using the function {\tt ellan}, which computes
the first few $a_n$.
\begin{verbatim}
? ellan(E,15)
%24 = [1, 0, 0, 0, -3, 0, 3, 0, -3, 0, -2, 0, -4, 0, 0]
\end{verbatim}
This output means that
$a_1=1$, $a_2=a_3=a_4=0$, $a_5=-3$, $a_6=0$, and so on.

When confronted by a mysterious list of numbers,
it is a ``reflex action'' for a mathematician to
package them together in a generating function, and see
if anything neat happens.   It turns out that for the above
numbers, a good way to do this is as follows.  Define
$$
  L(E,s) = \sum_{n=1}^{\infty} a_n n^{-s}.
$$
This might remind you of Riemann's $\zeta$-function, which
is the function you get if you make the simplest generating
function $\sum_{n=1}^{\infty} n^{-s}$ of this form.

Using {\tt elllseries(E,s,1)} I drew a
graph of $L(E,s)$ for $y^2=x^3+x+1$.
%for(x=1,100,print("(",1.0+x/50,",",elllseries(E,1+x/50,1),")"))
\vspace{1.2ex}

\begin{center}
  \psset{unit=.9in}
  \pspicture(-0.5,-2)(5,2)
  \psgrid[gridcolor=lightgray]

  \psline[linewidth=0.03]{->}(-0.5,0)(5,0)\rput(5.2,0){$x$}
  \psline[linewidth=0.03]{->}(0,-2)(0,2)\rput(0.1,2.25){$y$}
  \pscurve[linecolor=blue]
  %(-1.080000000000000000000000000,-33.49735351293417519211957696)
  %(-1.060000000000000000000000000,-23.30618230499020337000373142)
  %(-1.040000000000000000000000000,-14.40013549713258688142857476)
  %(-1.020000000000000000000000000,-6.666750787626234222798328824)
  %(-1.000000000000000000000000000,0)
  %(-0.9800000000000000000000000000,5.699704107463487881595135135)
  %(-0.9600000000000000000000000000,10.52556787250100408984833825)
  %(-0.9400000000000000000000000000,14.56453159610393139838452949)
  %(-0.9200000000000000000000000000,17.89742873496613271109224969)
  %(-0.9000000000000000000000000000,20.59918025294355028555601218)
  %(-0.8800000000000000000000000000,22.73901617411571677839171309)
  %(-0.8600000000000000000000000000,24.38071746335094659631695268)
  %(-0.8400000000000000000000000000,25.58287232837695046489301202)
  %(-0.8200000000000000000000000000,26.39914189997744671269276965)
  %(-0.8000000000000000000000000000,26.87853101325012323501793035)
  %(-0.7800000000000000000000000000,27.06566049148954261563524146)
  %(-0.7600000000000000000000000000,27.00103793325991540674742554)
  %(-0.7400000000000000000000000000,26.72132453010697097892001918)
  %(-0.7200000000000000000000000000,26.25959590409875291304792016)
  %(-0.7000000000000000000000000000,25.64559535743598483062338192)
  %(-0.6800000000000000000000000000,24.90597827668839462275666538)
  %(-0.6600000000000000000000000000,24.06454673727188223460902289)
  %(-0.6400000000000000000000000000,23.14247361460749258894737732)
  %(-0.6200000000000000000000000000,22.15851573159342147339773242)
  %(-0.6000000000000000000000000000,21.12921576176938711346658653)
  %(-0.5800000000000000000000000000,20.06909276767464971187633622)
  %(-0.5600000000000000000000000000,18.99082138786018776084292472)
  %(-0.5400000000000000000000000000,17.90539979694746777013357624)
  %(-0.5200000000000000000000000000,16.82230665386215252118639211)
  %(-0.5000000000000000000000000000,15.74964732646112346014832648)
  %(-0.4800000000000000000000000000,14.69428973850638644492412708)
  %(-0.4600000000000000000000000000,13.66199022937271508816132392)
  %(-0.4400000000000000000000000000,12.65750984984196302266387590)
  %(-0.4200000000000000000000000000,11.68472154047107162519256808)
  %(-0.4000000000000000000000000000,10.74670865377627554465731485)
  %(-0.3800000000000000000000000000,9.845855289140837890338474853)
  %(-0.3600000000000000000000000000,8.983928911065769297878245834)
  %(-0.3400000000000000000000000000,8.162155718144570107912295606)
  %(-0.3200000000000000000000000000,7.381289222833692468548176322)
  %(-0.3000000000000000000000000000,6.641672491479395154753170950)
  %(-0.2800000000000000000000000000,5.943294480819068068115729053)
  %(-0.2600000000000000000000000000,5.285840891882218253941665414)
  %(-0.2400000000000000000000000000,4.668739945375030087678873597)
  %(-0.2200000000000000000000000000,4.091203464673869404757383600)
  %(-0.2000000000000000000000000000,3.552263633845516346109042850)
  %(-0.1800000000000000000000000000,3.050805778967737778211393193)
  %(-0.1600000000000000000000000000,2.585597501706206602067421937)
  (-0.1400000000000000000000000000,2.155314474832464013637531220)
  (-0.1200000000000000000000000000,1.758563190324171519751084992)
  (-0.1000000000000000000000000000,1.393900932021487856105377543)
  (-0.08000000000000000000000000000,1.059853226641086843310683907)
  (-0.06000000000000000000000000000,0.7549290093658687480207620868)
  (-0.04000000000000000000000000000,0.4776337233057031201262147783)
  (-0.02000000000000000000000000000,0.2264805559156493803118203957)
  (0,0)
  (0.01999999999999999999999999999,-0.2032520877535539568223379065)
  (0.03999999999999999999999999999,-0.3846877716636320305684161282)
  (0.05999999999999999999999999999,-0.5456815194333011780057117379)
  (0.07999999999999999999999999999,-0.6875657484100655972540557382)
  (0.09999999999999999999999999999,-0.8116273136809889848837962660)
  (0.1199999999999999999999999999,-0.9191048267650755545566794975)
  (0.1399999999999999999999999999,-1.011186703745138138424848667)
  (0.1599999999999999999999999999,-1.089009851058302891852665591)
  (0.1799999999999999999999999999,-1.153658905866088754379687304)
  (0.1999999999999999999999999999,-1.206165955981486905986407388)
  (0.2199999999999999999999999999,-1.247510671772666874346231945)
  (0.2399999999999999999999999999,-1.278620789322545925619597602)
  (0.2599999999999999999999999999,-1.300372890432423643696103314)
  (0.2799999999999999999999999999,-1.313593430848048495241669883)
  (0.2999999999999999999999999999,-1.319059973389296869917585827)
  (0.3199999999999999999999999999,-1.317502587510923201584859498)
  (0.3399999999999999999999999999,-1.309605381241573265337027215)
  (0.3599999999999999999999999999,-1.296008135470457753215985704)
  (0.3799999999999999999999999999,-1.277308014203686366711641843)
  (0.3999999999999999999999999999,-1.254061327722016660355895314)
  (0.4200000000000000000000000000,-1.226785328564197224704491896)
  (0.4400000000000000000000000000,-1.195960022959433463616130785)
  (0.4600000000000000000000000000,-1.162029982761741908891411132)
  (0.4800000000000000000000000000,-1.125406145119763043660142255)
  (0.5000000000000000000000000000,-1.086467589068372650174137218)
  (0.5200000000000000000000000000,-1.045563279972311097100919485)
  (0.5400000000000000000000000000,-1.003013774304955571376749964)
  (0.5600000000000000000000000000,-0.9591128786240189664197604289)
  (0.5800000000000000000000000000,-0.9141292578259498110666694067)
  (0.6000000000000000000000000000,-0.8683079888366067906634184838)
  (0.6200000000000000000000000000,-0.8218720568408118402014145334)
  (0.6400000000000000000000000000,-0.7750237919800668885420992176)
  (0.6600000000000000000000000000,-0.7279462451675185956769638841)
  (0.6800000000000000000000000000,-0.6808045022927402284637821273)
  (0.7000000000000000000000000000,-0.6337469366257890191527356458)
  (0.7200000000000000000000000000,-0.5869063996892122017182497059)
  (0.7400000000000000000000000000,-0.5404013512563889572701712147)
  (0.7600000000000000000000000000,-0.4943369294622830581782945779)
  (0.7800000000000000000000000000,-0.4488059622851641593188750869)
  (0.8000000000000000000000000000,-0.4038899218813494363386264587)
  (0.8200000000000000000000000000,-0.3596598234351727950594217281)
  (0.8400000000000000000000000000,-0.3161770703283348256437946190)
  (0.8600000000000000000000000000,-0.2734942475411684152034277311)
  (0.8800000000000000000000000000,-0.2316558652773717109055306447)
  (0.9000000000000000000000000000,-0.1906990548572001700993837000)
  (0.9199999999999999999999999999,-0.1506542189553829966855976694)
  (0.9399999999999999999999999999,-0.1115456382721996468474137832)
  (0.9599999999999999999999999999,-0.07339203672196470358163098881)
  (0.9799999999999999999999999999,-0.03620710720507893548448253411)
  (0.9999999999999999999999999999,0)
  (1.019999999999999999999999999,0.03522422422808464711776250892)
  (1.039999999999999999999999999,0.06946417485271024216855076862)
  (1.059999999999999999999999999,0.1027217432506621900689048735)
  (1.079999999999999999999999999,0.1350017444698155569132309611)
  (1.099999999999999999999999999,0.1663115868277940075760342687)
  (1.119999999999999999999999999,0.1966609677351744043683728379)
  (1.139999999999999999999999999,0.2260615941052292064678392836)
  (1.159999999999999999999999999,0.2545269257817305156960185572)
  (1.179999999999999999999999999,0.2820719404864633473625883919)
  (1.199999999999999999999999999,0.3087129188582340627716183682)
  (1.219999999999999999999999999,0.3344672482248215621854238593)
  (1.239999999999999999999999999,0.3593532438180892035283095680)
  (1.259999999999999999999999999,0.3833899862100080840642276069)
  (1.279999999999999999999999999,0.4065971738133513630669936552)
  (1.299999999999999999999999999,0.4289949893550722205953400309)
  (1.319999999999999999999999999,0.4506039792926894394264809011)
  (1.339999999999999999999999999,0.4714449452042304129407476950)
  (1.359999999999999999999999999,0.4915388462403137429445355752)
  (1.379999999999999999999999999,0.5109067117827161313591335073)
  (1.399999999999999999999999999,0.5295695635072119455592787195)
  (1.419999999999999999999999999,0.5475483460995731751599798943)
  (1.439999999999999999999999999,0.5648638659223672340883195511)
  (1.459999999999999999999999999,0.5815367369766021423253881006)
  (1.479999999999999999999999999,0.5975873335463695329559109221)
  (1.499999999999999999999999999,0.6130357489564643252439695136)
  (1.519999999999999999999999999,0.6279017599125645011184908027)
  (1.539999999999999999999999999,0.6422047959309921457600496469)
  (1.559999999999999999999999999,0.6559639134004112594514662481)
  (1.579999999999999999999999999,0.6691977738511174495420198857)
  (1.599999999999999999999999999,0.6819246260389119800463141501)
  (1.619999999999999999999999999,0.6941622914800030896893440764)
  (1.639999999999999999999999999,0.7059281531010181221590821136)
  (1.659999999999999999999999999,0.7172391466941189991507749321)
  (1.679999999999999999999999999,0.7281117548914693790596475547)
  (1.699999999999999999999999999,0.7385620033959826728729213209)
  (1.719999999999999999999999999,0.7486054592264633387425260675)
  (1.739999999999999999999999999,0.7582572307550157430317718896)
  (1.759999999999999999999999999,0.7675319693330099929291831831)
  (1.779999999999999999999999999,0.7764438723190353013508747367)
  (1.799999999999999999999999999,0.7850066873382093486184291632)
  (1.819999999999999999999999999,0.7932337176170152088571653803)
  (1.839999999999999999999999999,0.8011378282515717680991356916)
  (1.859999999999999999999999999,0.8087314532799727362229138236)
  (1.879999999999999999999999999,0.8160266034411143442823407867)
  (1.900000000000000000000000000,0.8230348745133310194750085736)
  (1.919999999999999999999999999,0.8297674561362275232246321553)
  (1.939999999999999999999999999,0.8362351410283883951022239262)
  (1.959999999999999999999999999,0.8424483345222116518033929565)
  (1.979999999999999999999999999,0.8484170643450015831329129481)
  (1.999999999999999999999999999,0.8541509905827107049053188422)
  (2.019999999999999999999999999,0.8596594157693865729349664339)
  (2.039999999999999999999999999,0.8649512950514959641470191669)
  (2.059999999999999999999999999,0.8700352463819053261686788797)
  (2.079999999999999999999999999,0.8749195607034286052118554500)
  (2.099999999999999999999999999,0.8796122120865456697621026703)
  (2.119999999999999999999999999,0.8841208677901785922294259627)
  (2.139999999999999999999999999,0.8884528982183191080442260063)
  (2.159999999999999999999999999,0.8926153867488568426111693982)
  (2.180000000000000000000000000,0.8966151394141907941346802421)
  (2.199999999999999999999999999,0.9004586944161407961065734129)
  (2.220000000000000000000000000,0.9041523314603343522098018268)
  (2.240000000000000000000000000,0.9077020808976489005055080050)
  (2.260000000000000000000000000,0.9111137326624603334303714290)
  (2.280000000000000000000000000,0.9143928449994042134101653969)
  (2.299999999999999999999999999,0.9175447529721140233283892378)
  (2.320000000000000000000000000,0.9205745767489771978387429587)
  (2.340000000000000000000000000,0.9234872296623596667910146809)
  (2.360000000000000000000000000,0.9262874260390071957135721674)
  (2.380000000000000000000000000,0.9289796888004499045921865445)
  (2.400000000000000000000000000,0.9315683568332270065237235088)
  (2.420000000000000000000000000,0.9340575921296231607364210437)
  (2.440000000000000000000000000,0.9364513867003761726486768979)
  (2.460000000000000000000000000,0.9387535692614876081496438120)
  (2.480000000000000000000000000,0.9409678116978520009902969206)
  (2.500000000000000000000000000,0.9430976353069248204424359709)
  (2.520000000000000000000000000,0.9451464168260816951540168020)
  (2.540000000000000000000000000,0.9471173942476884304088491145)
  (2.560000000000000000000000000,0.9490136724262094308830115147)
  (2.580000000000000000000000000,0.9508382284819370583001476459)
  (2.600000000000000000000000000,0.9525939170061315460122202778)
  (2.620000000000000000000000000,0.9542834750725252515757226760)
  (2.640000000000000000000000000,0.9559095270602707352226615796)
  (2.660000000000000000000000000,0.9574745892935035084139870408)
  (2.680000000000000000000000000,0.9589810745027510525262029503)
  (2.700000000000000000000000000,0.9604312961134532880409926309)
  (2.720000000000000000000000000,0.9618274723668692036328211309)
  (2.740000000000000000000000000,0.9631717302786326788726077329)
  (2.760000000000000000000000000,0.9644661094401902442526287447)
  (2.780000000000000000000000000,0.9657125656683069720047600478)
  (2.800000000000000000000000000,0.9669129745077660171997530024)
  (2.820000000000000000000000000,0.9680691345923144670100977821)
  (2.840000000000000000000000000,0.9691827708688248586775386276)
  (2.860000000000000000000000000,0.9702555376895495762344373971)
  (2.880000000000000000000000000,0.9712890217772457595474494164)
  (2.900000000000000000000000000,0.9722847450678426413862997440)
  (2.919999999999999999999999999,0.9732441674352125229395878100)
  (2.939999999999999999999999999,0.9741686893024919398796018929)
  (2.959999999999999999999999999,0.9750596541442818847165060175)
  (2.979999999999999999999999999,0.9759183508839360618415438456)
  (2.999999999999999999999999999,0.9767460161900247931909794376)
  (3.019999999999999999999999999,0.9775438366759400155616537393)
  (3.039999999999999999999999999,0.9783129510064843902398142976)
  (3.059999999999999999999999999,0.9790544519151653878478716340)
  (3.079999999999999999999999999,0.9797693881357937596700447936)
  (3.099999999999999999999999999,0.9804587662518654479103440767)
  (3.119999999999999999999999999,0.9811235524670870566436114110)
  (3.139999999999999999999999999,0.9817646743002877913770988687)
  (3.159999999999999999999999999,0.9823830222078455248353416374)
  (3.179999999999999999999999999,0.9829794511366415685838778706)
  (3.199999999999999999999999999,0.9835547820104479990473301150)
  (3.219999999999999999999999999,0.9841098031525431463062521872)
  (3.239999999999999999999999999,0.9846452716472452212151986626)
  (3.259999999999999999999999999,0.9851619146429511226941147100)
  (3.279999999999999999999999999,0.9856604305991673023529708762)
  (3.299999999999999999999999999,0.9861414904799222181961196336)
  (3.319999999999999999999999999,0.9866057388958554159282840108)
  (3.339999999999999999999999999,0.9870537951971866528866323756)
  (3.359999999999999999999999999,0.9874862545196797298362541760)
  (3.379999999999999999999999999,0.9879036887856298118676165256)
  (3.399999999999999999999999999,0.9883066476618199830936167544)
  (3.419999999999999999999999999,0.9886956594763125633767027957)
  (3.439999999999999999999999999,0.9890712320958632837119574978)
  (3.459999999999999999999999999,0.9894338537656717282188195564)
  (3.479999999999999999999999999,0.9897839939131094572996760737)
  (3.499999999999999999999999999,0.9901221039169978759330464817)
  (3.519999999999999999999999999,0.9904486178439411467933073775)
  (3.539999999999999999999999999,0.9907639531531552101491327067)
  (3.559999999999999999999999999,0.9910685113711721988694467144)
  (3.579999999999999999999999999,0.9913626787377401628776365783)
  (3.599999999999999999999999999,0.9916468268241809770156590241)
  (3.619999999999999999999999999,0.9919213131254145323963901729)
  (3.639999999999999999999999999,0.9921864816268047361415122196)
  (3.659999999999999999999999999,0.9924426633469323998079734711)
  (3.679999999999999999999999999,0.9926901768573517146768069203)
  (3.699999999999999999999999999,0.9929293287803406245620133988)
  (3.719999999999999999999999999,0.9931604142656109465533086785)
  (3.739999999999999999999999999,0.9933837174469014905134559443)
  (3.759999999999999999999999999,0.9935995118793366234868483046)
  (3.779999999999999999999999999,0.9938080609583936507724848491)
  (3.799999999999999999999999999,0.9940096183212849777866304069)
  (3.819999999999999999999999999,0.9942044282315252137962006230)
  (3.839999999999999999999999999,0.9943927259474191193342646358)
  (3.859999999999999999999999999,0.9945747380751735242613615491)
  (3.879999999999999999999999999,0.9947506829073049951724587995)
  (3.899999999999999999999999999,0.9949207707469850528904034456)
  (3.919999999999999999999999999,0.9950852042189360784455561936)
  (3.939999999999999999999999999,0.9952441785674636461447818362)
  (3.959999999999999999999999999,0.9953978819421848336344820844)
  (3.979999999999999999999999999,0.9955464956719870314462145826)
  (3.999999999999999999999999999,0.9956901945277278601920233627)
  (4.019999999999999999999999999,0.9958291469741639557811460590)
  (4.039999999999999999999999999,0.9959635154115745567956503216)
  (4.059999999999999999999999999,0.9960934564075249801098767935)
  (4.079999999999999999999999999,0.9962191209191951591486002631)
  (4.099999999999999999999999999,0.9963406545066794035670335077)
  (4.119999999999999999999999999,0.9964581975376453808219607797)
  (4.139999999999999999999999999,0.9965718853837229817791534936)
  (4.159999999999999999999999999,0.9966818486089771782972241264)
  (4.179999999999999999999999999,0.9967882131508031761734395767)
  (4.199999999999999999999999999,0.9968911004935670788286234528)
  (4.219999999999999999999999999,0.9969906278353008738686053062)
  (4.239999999999999999999999999,0.9970869082477468056990830640)
  (4.259999999999999999999999999,0.9971800508300330734483705558)
  (4.279999999999999999999999999,0.9972701608562502665368631586)
  (4.299999999999999999999999999,0.9973573399171859934618454750)
  (4.319999999999999999999999999,0.9974416860564637470111108356)
  (4.339999999999999999999999999,0.9975232939013211565404758980)
  (4.359999999999999999999999999,0.9976022547882523815640173693)
  (4.379999999999999999999999999,0.9976786568837294781428390859)
  (4.399999999999999999999999999,0.9977525853002080988279657476)
  (4.419999999999999999999999999,0.9978241222076138475670031295)
  (4.439999999999999999999999999,0.9978933469404969832800117391)
  (4.459999999999999999999999999,0.9979603361010349308761758162)
  (4.479999999999999999999999999,0.9980251636580541982847871068)
  (4.499999999999999999999999999,0.9980879010422357953809351745)
  (4.519999999999999999999999999,0.9981486172376610890385566058)
  (4.539999999999999999999999999,0.9982073788698481922215186352)
  (4.559999999999999999999999999,0.9982642502904224590170408962)
  (4.579999999999999999999999999,0.9983192936585584274948042166)
  (4.599999999999999999999999999,0.9983725690193246045607965370)
  (4.619999999999999999999999999,0.9984241343790568085123795418)
  (4.639999999999999999999999999,0.9984740457778803633324969282)
  (4.659999999999999999999999999,0.9985223573594962620001479394)
  (4.679999999999999999999999999,0.9985691214383414729016898187)
  (4.699999999999999999999999999,0.9986143885642288429865234966)
  (4.719999999999999999999999999,0.9986582075845675433044377798)
  (4.739999999999999999999999999,0.9987006257042606971512855329)
  (4.759999999999999999999999999,0.9987416885433727188522121635)
  (4.779999999999999999999999999,0.9987814401926549632809405214)
  (4.799999999999999999999999999,0.9988199232670145340197490690)
  (4.819999999999999999999999999,0.9988571789570075134755955834)
  (4.839999999999999999999999999,0.9988932470784344535308592568)
  (4.859999999999999999999999999,0.9989281661201126930323239535)
  (4.879999999999999999999999999,0.9989619732898969415650654604)
  (4.899999999999999999999999999,0.9989947045590165808045677663)
  (5,0.999999)

  \pscircle*[linecolor=red](1,0){0.06}

  \endpspicture

\end{center}

That the value of $L(E,s)$ makes sense at $s=1$, where the series above
doesn't obviously converge, follows from the nontrivial
fact that the function
$$
  f(z)=\sum_{n=1}^{\infty} a_n e^{2\pi i nz}
$$
is a {\em modular form}.  Also, keep your eyes on the dot;
it plays a central roll in the Birch and Swinnerton-Dyer conjecture,
which asserts that $L(E,1)=0$ if and only if the group $E(\Q)$
is infinite.

\subsection{A Curve of Rank Two}
Let $E$ be the simplest rank~$2$ curve:
$$
  y^2 + y = x^3 + x^2 - 2x.
$$
The discriminant is $389$.
%e=ellinit([0,1,1,-2,0]);
%for(x=1,100,print("(",x/20.0,",",elllseries(e,x/20.0,1),")"))
\vspace{1.2ex}

\begin{center}
  \psset{unit=.9in}
  \pspicture(-0.5,-2)(5,2)
  \psgrid[gridcolor=lightgray]

  \psline[linewidth=0.03]{->}(-0.5,0)(5,0)\rput(5.2,0){$x$}
  \psline[linewidth=0.03]{->}(0,-2)(0,2)\rput(0.1,2.25){$y$}
  \pscurve[linecolor=blue]
  (-0.2500000000000000000000000000,-1.869301080746668455985357875)
  (-0.2000000000000000000000000000,-1.307452174275307812975265456)
  (-0.1500000000000000000000000000,-0.8508834566546463310621688366)
  (-0.1000000000000000000000000000,-0.4886364479565607868738206842)
  (-0.05000000000000000000000000000,-0.2089451667158738982563246704)
  (0.04999999999999999999999999999,0.1495303531105348677905016281)
  (0.09999999999999999999999999999,0.2501634032128948577781162685)
  (0.1499999999999999999999999999,0.3114082461725666566870413872)
  (0.1999999999999999999999999999,0.3416748620723204017123107521)
  (0.2500000000000000000000000000,0.3482578941959046100721623459)
  (0.2999999999999999999999999999,0.3373718043623533659424285446)
  (0.3499999999999999999999999999,0.3142195810543822817144335937)
  (0.3999999999999999999999999999,0.2830815077294071810755392342)
  (0.4499999999999999999999999999,0.2474139993042702609559524431)
  (0.5000000000000000000000000000,0.2099513032520556529211768679)
  (0.5499999999999999999999999999,0.1728050497929077261080500819)
  (0.5999999999999999999999999999,0.1375583252730762514803768786)
  (0.6499999999999999999999999999,0.1053522245757870934123179003)
  (0.6999999999999999999999999999,0.07696379100480972080761890705)
  (0.7500000000000000000000000000,0.05287494269328086191489706832)
  (0.7999999999999999999999999999,0.03333246962187091404798015891)
  (0.8499999999999999999999999999,0.01839951146778021091061749336)
  (0.8999999999999999999999999999,0.007999131868965912642441395573)
  (0.9499999999999999999999999999,0.001950719995076086523458768854)
  (1.000000000000000000000000000,0)
  (1.049999999999999999999999999,0.001843432162623526805421027989)
  (1.099999999999999999999999999,0.007147761132339355951297819538)
  (1.149999999999999999999999999,0.01556541797619881097845183612)
  (1.199999999999999999999999999,0.02674642212028410492580900502)
  (1.250000000000000000000000000,0.04034736297109149972237511052)
  (1.299999999999999999999999999,0.05603797340009158542754001710)
  (1.349999999999999999999999999,0.07350574140846672344937000182)
  (1.399999999999999999999999999,0.09245894414351856480180224415)
  (1.449999999999999999999999999,0.1126284312117006189401349698)
  (1.500000000000000000000000000,0.1337684325463184144182903735)
  (1.549999999999999999999999999,0.1556566201470275288035905334)
  (1.599999999999999999999999999,0.1780936127319682621754391509)
  (1.649999999999999999999999999,0.2009020774591902803388255939)
  (1.699999999999999999999999999,0.2239255529909046600601038464)
  (1.750000000000000000000000000,0.2470270928360595987690243203)
  (1.799999999999999999999999999,0.2700878066314372903924979782)
  (1.849999999999999999999999999,0.2930053593272868093124930495)
  (1.899999999999999999999999999,0.3156924736677139737711561022)
  (1.949999999999999999999999999,0.3380754694663440402210869532)
  (2.000000000000000000000000000,0.3600928635788807296980040920)
  (2.049999999999999999999999999,0.3816940468108779983880996151)
  (2.099999999999999999999999999,0.4028380479566454785017768724)
  (2.149999999999999999999999999,0.4234923904683971243274757788)
  (2.199999999999999999999999999,0.4436320436652311156968651002)
  (2.250000000000000000000000000,0.4632384677050697254303080822)
  (2.299999999999999999999999999,0.4822987495858363724860086952)
  (2.349999999999999999999999999,0.5008048260688364847201755901)
  (2.399999999999999999999999999,0.5187527885055396888373777886)
  (2.449999999999999999999999999,0.5361422639976527614363980368)
  (2.500000000000000000000000000,0.5529758670464501924162602402)
  (2.549999999999999999999999999,0.5692587157830759193525914748)
  (2.599999999999999999999999999,0.5849980069622239219107713778)
  (2.649999999999999999999999999,0.6002026441034326616295762561)
  (2.699999999999999999999999999,0.6148829134424614342827547772)
  (2.750000000000000000000000000,0.6290502026826420217480678266)
  (2.799999999999999999999999999,0.6427167578916698305616933645)
  (2.849999999999999999999999999,0.6558954742569908667854371904)
  (2.899999999999999999999999999,0.6685997167807884964349752979)
  (2.949999999999999999999999999,0.6808431673548621557265187577)
  (3.000000000000000000000000000,0.6926396950002846117222293821)
  (3.049999999999999999999999999,0.7040032463825176015043907577)
  (3.099999999999999999999999999,0.7149477540171236680904994188)
  (3.149999999999999999999999999,0.7254870598630050867062509271)
  (3.199999999999999999999999999,0.7356348522588055534985384207)
  (3.250000000000000000000000000,0.7454046143939708829454230379)
  (3.299999999999999999999999999,0.7548095827197031814199544254)
  (3.349999999999999999999999999,0.7638627138977091675110432410)
  (3.399999999999999999999999999,0.7725766590575001282836744756)
  (3.449999999999999999999999999,0.7809637442874340762918718280)
  (3.500000000000000000000000000,0.7890359564221410713637781201)
  (3.549999999999999999999999999,0.7968049333108866931198608023)
  (3.599999999999999999999999999,0.8042819578592214559330635355)
  (3.649999999999999999999999999,0.8114779552312940874578182219)
  (3.699999999999999999999999999,0.8184034926837589712869664778)
  (3.750000000000000000000000000,0.8250687815754835493137719202)
  (3.799999999999999999999999999,0.8314836811613714233702023912)
  (3.849999999999999999999999999,0.8376577038345813375485705589)
  (3.899999999999999999999999999,0.8436000215301708943820184830)
  (3.949999999999999999999999999,0.8493194730455690754202973327)
  (4.000000000000000000000000000,0.8548245720700425168614353307)
  (4.049999999999999999999999999,0.8601235157471480128605405330)
  (4.099999999999999999999999999,0.8652241936216662643009316377)
  (4.149999999999999999999999999,0.8701341968462309909853208257)
  (4.199999999999999999999999999,0.8748608275432836179837562920)
  (4.250000000000000000000000000,0.8794111082355217942116722409)
  (4.299999999999999999999999999,0.8837917912730448714765732526)
  (4.349999999999999999999999999,0.8880093681982610195943916738)
  (4.399999999999999999999999999,0.8920700790005984563619489026)
  (4.449999999999999999999999999,0.8959799212224110098972823458)
  (4.500000000000000000000000000,0.8997446588854076963520304475)
  (4.549999999999999999999999999,0.9033698312136607404862465887)
  (4.599999999999999999999999999,0.9068607611349251047730284956)
  (4.649999999999999999999999999,0.9102225635467817730780041656)
  (4.699999999999999999999999999,0.9134601533381241020682024834)
  (4.750000000000000000000000000,0.9165782531598519236573296568)
  (4.799999999999999999999999999,0.9195814009414173557072563010)
  (4.849999999999999999999999999,0.9224739571521621136793041110)
  (4.899999999999999999999999999,0.9252601118082698830816512369)
  (4.949999999999999999999999999,0.9279438912276905530726670644)
  (5.000000000000000000000000000,0.9305291645366288221292825852)

  \pscircle*[linecolor=red](1,0){0.06}

  \endpspicture

\end{center}

\subsection{A Curve of Rank Three}
Let $E$ be the simplest rank~$3$ curve:
$$
  y^2 + y = x^3 - 7x + 6.
$$
The discriminant is $5077$.
%e=ellinit([0,0,1,-7,6]);
%for(x=1,120,print("(",-1+x/20.0,",",elllseries(e,-1+x/20.0,1),")"))
\vspace{1.2ex}

\begin{center}
  \psset{unit=.9in}
  \pspicture(-0.5,-2)(5,2)
  \psgrid[gridcolor=lightgray]

  \psline[linewidth=0.03]{->}(-0.5,0)(5,0)\rput(5.2,0){$x$}
  \psline[linewidth=0.03]{->}(0,-2)(0,2)\rput(0.1,2.25){$y$}
  \pscurve[linecolor=blue]
  %(-0.05000000000000000000000000000,2.665459353842516300887862596)
  (-0.05000000000000000000000000000,2)
  (0.04999999999999999999999999999,-1.436313690317728678707910214)
  (0.09999999999999999999999999999,-2.080058567173062689443317490)
  (0.1499999999999999999999999999,-2.236939072193606938632963309)
  (0.1999999999999999999999999999,-2.115456837327142818018141335)
  (0.2500000000000000000000000000,-1.853432905059353050907893277)
  (0.2999999999999999999999999999,-1.538400874605764615403251437)
  (0.3499999999999999999999999999,-1.222950308809072438872831689)
  (0.3999999999999999999999999999,-0.9360357684041337897681360899)
  (0.4499999999999999999999999999,-0.6911433407224225907444126881)
  (0.5000000000000000000000000000,-0.4920628837884663767162722526)
  (0.5499999999999999999999999999,-0.3368727272381728237157201340)
  (0.5999999999999999999999999999,-0.2206157061565192332634880189)
  (0.6499999999999999999999999999,-0.1370359284572558537858170991)
  (0.6999999999999999999999999999,-0.07965546780820809922285492823)
  (0.7500000000000000000000000000,-0.04239799885735108894403555342)
  (0.7999999999999999999999999999,-0.01991002302575148987446085200)
  (0.8499999999999999999999999999,-0.007687221254587426202010872917)
  (0.8999999999999999999999999999,-0.002081118652996614152056766731)
  (0.9499999999999999999999999999,-0.0002374050371406603390332040359)
  (1.000000000000000000000000000,0)
  (1.049999999999999999999999999,0.0001973055383879500596605611727)
  (1.099999999999999999999999999,0.001438329075870453641136902056)
  (1.149999999999999999999999999,0.004423593813688950588261765297)
  (1.199999999999999999999999999,0.009557371165349786145716519831)
  (1.250000000000000000000000000,0.01702143669397141056910104126)
  (1.299999999999999999999999999,0.02683578433704244666524467576)
  (1.349999999999999999999999999,0.03890736792440829376782056899)
  (1.399999999999999999999999999,0.05306833385534747781079476114)
  (1.449999999999999999999999999,0.06910533155796129556286888417)
  (1.500000000000000000000000000,0.08678144750494967257462341313)
  (1.549999999999999999999999999,0.1058521772685890314517613547)
  (1.599999999999999999999999999,0.1260766763059780939693254888)
  (1.649999999999999999999999999,0.1472253441231011839679738745)
  (1.699999999999999999999999999,0.1690846162559851612628322841)
  (1.750000000000000000000000000,0.1914596740374486075847895246)
  (1.799999999999999999999999999,0.2141756379606673828509888436)
  (1.849999999999999999999999999,0.2370776878254622994561836946)
  (1.899999999999999999999999999,0.2600304509861604269868339229)
  (1.949999999999999999999999999,0.2829169170544625420470579397)
  (2.000000000000000000000000000,0.3056370709993943665549003165)
  (2.049999999999999999999999999,0.3281063842610022498080182847)
  (2.099999999999999999999999999,0.3502542628853236001868016155)
  (2.149999999999999999999999999,0.3720225206421691524288643731)
  (2.199999999999999999999999999,0.3933639217203174874682380442)
  (2.250000000000000000000000000,0.4142408203036086038975532818)
  (2.299999999999999999999999999,0.4346239117815307688696096270)
  (2.349999999999999999999999999,0.4544911014482147977054116887)
  (2.399999999999999999999999999,0.4738264904162215078882895996)
  (2.449999999999999999999999999,0.4926194744192423955045506465)
  (2.500000000000000000000000000,0.5108639486548272542604836536)
  (2.549999999999999999999999999,0.5285576104019794566057004842)
  (2.599999999999999999999999999,0.5457013505159362590105184873)
  (2.649999999999999999999999999,0.5622987248098805143223571624)
  (2.699999999999999999999999999,0.5783554965991442740802876900)
  (2.750000000000000000000000000,0.5938792421744403906389750161)
  (2.799999999999999999999999999,0.6088790115907074308408645888)
  (2.849999999999999999999999999,0.6233650378393919194295903746)
  (2.899999999999999999999999999,0.6373484881677044668307021565)
  (2.949999999999999999999999999,0.6508412519875435061611998327)
  (3.000000000000000000000000000,0.6638557604598125793076126510)
  (3.049999999999999999999999999,0.6764048334354289801609480539)
  (3.099999999999999999999999999,0.6885015499768967519166251132)
  (3.149999999999999999999999999,0.7001591391723403383493824245)
  (3.199999999999999999999999999,0.7113908883884519063483917773)
  (3.250000000000000000000000000,0.7222100664926462501663795576)
  (3.299999999999999999999999999,0.7326298599115607103526447545)
  (3.349999999999999999999999999,0.7426633196870802560372874286)
  (3.399999999999999999999999999,0.7523233179466495118385487603)
  (3.449999999999999999999999999,0.7616225124260154850860002514)
  (3.500000000000000000000000000,0.7705733178737709065680951776)
  (3.549999999999999999999999999,0.7791878833318839640945432804)
  (3.599999999999999999999999999,0.7874780744282131691193445996)
  (3.649999999999999999999999999,0.7954554599388734439430500380)
  (3.699999999999999999999999999,0.8031313019829548321680034308)
  (3.750000000000000000000000000,0.8105165493018862487783973805)
  (3.799999999999999999999999999,0.8176218331527697405642974946)
  (3.849999999999999999999999999,0.8244574654110984549210934544)
  (3.899999999999999999999999999,0.8310334385349814375219085843)
  (3.949999999999999999999999999,0.8373594270916809277550879970)
  (4.000000000000000000000000000,0.8434447905890828398119005816)
  (4.049999999999999999999999999,0.8492985773906623268686357452)
  (4.099999999999999999999999999,0.8549295295234234862643042166)
  (4.149999999999999999999999999,0.8603460882149109792910197989)
  (4.199999999999999999999999999,0.8655564000183305574796269165)
  (4.250000000000000000000000000,0.8705683234046028238972415412)
  (4.299999999999999999999999999,0.8753894357172596229730437515)
  (4.349999999999999999999999999,0.8800270404008583682046530145)
  (4.399999999999999999999999999,0.8844881744263629263290703651)
  (4.449999999999999999999999999,0.8887796158479988297367871982)
  (4.500000000000000000000000000,0.8929078914356731922323888573)
  (4.549999999999999999999999999,0.8968792843353586902299472732)
  (4.599999999999999999999999999,0.9006998417170498471541577335)
  (4.649999999999999999999999999,0.9043753823761571479875307212)
  (4.699999999999999999999999999,0.9079115042596375262452695937)
  (4.750000000000000000000000000,0.9113135918928777967199333318)
  (4.799999999999999999999999999,0.9145868236874446176388673658)
  (4.849999999999999999999999999,0.9177361791133714732896009535)
  (4.899999999999999999999999999,0.9207664457227397728495168876)
  (4.949999999999999999999999999,0.9236822260139877758604273374)
  (5.000000000000000000000000000,0.9264879441286998841153195103)
  \pscircle*[linecolor=red](1,0){0.06}

  \endpspicture

\end{center}


\subsection{A Curve of Rank Four}
Let $E$ be the simplest {\em known} rank~$4$ curve:
$$
  y^2 + xy = x^3 - x^2 - 79x + 289
$$
The conductor is $2\cdot 117223$.
%e=ellinit([1, -1, 0 ,-79 ,289]);
%for(x=1,20,print("(",x/10.0,",",elllseries(e,x/10.0,1),")"))
%for(x=1,20,print("(",0.9+x/100.0,",",elllseries(e,x/100.0,1),")"))
%for(x=0,9,s=x/10.0;print("(",s,",",elllseries(e,s,1),")"))
%for(x=0,90,s=x/100.0;print("(",s,",",elllseries(e,s,1),")"))
%for(x=1,21,s=2+x/7.0;print("(",s,",",elllseries(e,s,1),")"))
\vspace{1.2ex}

\begin{center}
  \psset{unit=.9in}
  %\psset{unit=1.5in}
  \pspicture(-0.5,-2)(5,2)
  \psgrid[gridcolor=lightgray]

  \psline[linewidth=0.03]{->}(-0.5,0)(5,0)\rput(5.2,0){$x$}
  \psline[linewidth=0.03]{->}(0,-2)(0,2)\rput(0.1,2.25){$y$}
  \pscircle*[linecolor=red](1,0){0.06}



  \pscurve[linecolor=blue]
  (0,0)
  (0.009999999999999999999999999999,20.17460740732031218029067105)
  (0.01999999999999999999999999999,36.49248092043218759414227962)
  (0.02999999999999999999999999999,49.48695269194186255570395989)
  (0.03999999999999999999999999999,59.62809499064306722407223772)
  (0.04999999999999999999999999999,67.32949817701725860474850393)
  (0.05999999999999999999999999999,72.95437998695321784902246547)
  (0.06999999999999999999999999999,76.82108703674534698230608976)
  (0.07999999999999999999999999999,79.20804440435897043647662713)
  (0.08999999999999999999999999999,80.35820445115398146915461672)
  (0.09999999999999999999999999999,80.48304170441465810207415387)
  (0.1099999999999999999999999999,79.76613660406133174625793745)
  (0.1199999999999999999999999999,78.36638720713565471334218075)
  (0.1299999999999999999999999999,76.42088452189766469084520681)
  (0.1399999999999999999999999999,74.04748399111477869470341911)
  (0.1499999999999999999999999999,71.34710274352131875238762686)
  (0.1599999999999999999999999999,68.40576956639155116679637679)
  (0.1699999999999999999999999999,65.29645210439220133457469776)
  (0.1799999999999999999999999999,62.08068354485849675320142694)
  (0.1899999999999999999999999999,58.81000899267982264641223734)
  (0.1999999999999999999999999999,55.52726985520684582203751378)
  (0.2099999999999999999999999999,52.26774283592287994132381501)
  (0.2199999999999999999999999999,49.06014856276556567828076763)
  (0.2299999999999999999999999999,45.92754344141284383534396879)
  (0.2399999999999999999999999999,42.88810701477216095300037175)
  (0.2500000000000000000000000000,39.95583591725886436605311523)
  (0.2599999999999999999999999999,37.14115442682657456540803882)
  (0.2699999999999999999999999999,34.45145063037828623093305962)
  (0.2799999999999999999999999999,31.89154632102089971062111326)
  (0.2899999999999999999999999999,29.46410793109383578525135981)
  (0.2999999999999999999999999999,27.17000506602518061597947147)
  (0.3099999999999999999999999999,25.00862253439015019890448691)
  (0.3199999999999999999999999999,22.97813116310177967557620869)
  (0.3299999999999999999999999999,21.07572213794885965683462997)
  (0.3399999999999999999999999999,19.29780911363895258131960784)
  (0.3499999999999999999999999999,17.64020188943555017633327563)
  (0.3599999999999999999999999999,16.09825504210457925139305818)
  (0.3699999999999999999999999999,14.66699454326279499576807542)
  (0.3799999999999999999999999999,13.34122505973041656327517788)
  (0.3899999999999999999999999999,12.11562033981553963057313496)
  (0.3999999999999999999999999999,10.98479882256070828538534132)
  (0.4099999999999999999999999999,9.943386368083383947466728763)
  (0.4199999999999999999999999999,8.986067792701650934369969225)
  (0.4299999999999999999999999999,8.107628700234563525818535593)
  (0.4399999999999999999999999999,7.302988928586518979021916724)
  (0.4499999999999999999999999999,6.567228776537510403916594514)
  (0.4599999999999999999999999999,5.895609037808749758128609910)
  (0.4699999999999999999999999999,5.283585746356747623589733925)
  (0.4799999999999999999999999999,4.726820427014403603207828295)
  (0.4899999999999999999999999999,4.221186547723964451899689776)
  (0.5000000000000000000000000000,3.762772782494687754526881867)
  (0.5099999999999999999999999999,3.347883616780079475458459212)
  (0.5199999999999999999999999999,2.973037758219996513199228676)
  (0.5299999999999999999999999999,2.634964754739207607589602182)
  (0.5399999999999999999999999999,2.330600168028682449712596851)
  (0.5499999999999999999999999999,2.057079602728938881782014276)
  (0.5599999999999999999999999999,1.811731849526851974888215643)
  (0.5699999999999999999999999999,1.592071363273333449258930595)
  (0.5799999999999999999999999999,1.395790264592613793193107804)
  (0.5899999999999999999999999999,1.220750024801029588629612509)
  (0.5999999999999999999999999999,1.064972968849014570738009102)
  (0.6099999999999999999999999999,0.9266337090529745829497283098)
  (0.6199999999999999999999999999,0.8040506032421436529502188197)
  (0.6299999999999999999999999999,0.6956773142935737278953165664)
  (0.6399999999999999999999999999,0.6000945335828484520729664871)
  (0.6499999999999999999999999999,0.5160019183851058048608615062)
  (0.6599999999999999999999999999,0.4422102824932734723852967661)
  (0.6699999999999999999999999999,0.3776340700748442896394133312)
  (0.6799999999999999999999999999,0.3212841348834891250179487532)
  (0.6899999999999999999999999999,0.2722608402152899327179309314)
  (0.6999999999999999999999999999,0.2297474893069316371408115573)
  (0.7099999999999999999999999999,0.1930040910861989698630156271)
  (0.7199999999999999999999999999,0.1613614621891930253258516617)
  (0.7299999999999999999999999999,0.1342156628522034383917738953)
  (0.7399999999999999999999999999,0.1110227615790083457114122966)
  (0.7500000000000000000000000000,0.09129392129667414518098480549)
  (0.7599999999999999999999999999,0.07459079797405312211187159854)
  (0.7699999999999999999999999999,0.06052124132473794610248824865)
  (0.7799999999999999999999999999,0.04873528619520886474474134312)
  (0.7899999999999999999999999999,0.03892142250086749266271155850)
  (0.7999999999999999999999999999,0.03080313107503010887527356618)
  (0.8099999999999999999999999999,0.02413567250142808775689375590)
  (0.8199999999999999999999999999,0.01870311587666825308627117243)
  (0.8299999999999999999999999999,0.01431559446691944601678069902)
  (0.8399999999999999999999999999,0.01080677535797030118779995171)
  (0.8499999999999999999999999999,0.008031530428166537313088826674)
  (0.8599999999999999999999999999,0.005863796280892651616045409318)
  (0.8699999999999999999999999999,0.004194611141076388195622582279)
  (0.8799999999999999999999999999,0.002930317134784240786576375213)
  (0.8899999999999999999999999999,0.001990916820450637832948807979)
  (0.8999999999999999999999999999,0.001308573314500336314393575834)
  (0.9099999999999999999999999999,0.0008262438444779515009749122133)
  (0.9199999999999999999999999999,0.0004964370620328922663686765112)
  (0.9299999999999999999999999999,0.0002800849501432968528544120962)
  (0.9399999999999999999999999999,0.0001455206587485309187589983425)
  (0.9499999999999999999999999999,0.00006755409634153535113332324454)
  (0.9599999999999999999999999999,0.00002663758867231954273129854073)
  (0.9699999999999999999999999999,0.000008114386827604695137558360759)
  (0.9799999999999999999999999999,0.000001543263454951985960816259945)
  (0.9899999999999999999999999999,0.00000009287616753530200698777679283)
  (1.000000000000000000000000000,0)
  (1.009999999999999999999999999,0.00000008613535242271793720817881641)
  (1.019999999999999999999999999,0.000001327383632453574056960242273)
  (1.029999999999999999999999999,0.000006472849519208711184577737210)
  (1.039999999999999999999999999,0.00001970717637751214949546526075)
  (1.049999999999999999999999999,0.00004635314997888051879883070784)
  (1.059999999999999999999999999,0.00009261061561523324548962036876)
  (1.069999999999999999999999999,0.0001653282453215230156051138830)
  (1.079999999999999999999999999,0.0002718049657487095079831033217)
  (1.089999999999999999999999999,0.0004196181138178058712824946368)
  (1.099999999999999999999999999,0.0006164756272648974614962677827)
  (1.099999999999999999999999999,0.0006164756272648974614962677827)
  (1.199999999999999999999999999,0.006870279746547869692524606156)
  (1.299999999999999999999999999,0.02451425929226668848552443352)
  (1.399999999999999999999999999,0.05530486595233524676133790522)
  (1.500000000000000000000000000,0.09765544070412340678441505311)
  (1.599999999999999999999999999,0.1484133662890015288766060733)
  (1.699999999999999999999999999,0.2041828834745631596498302481)
  (1.799999999999999999999999999,0.2620135130277175192931976650)
  (1.899999999999999999999999999,0.3196398583984175776395640284)
  (2.000000000000000000000000000,0.3754772776917356690282702906)
  (2.142857142857142857142857142,0.4502313491226790796900981145)
  (2.285714285714285714285714285,0.5178521605298004955131946576)
  (2.428571428571428571428571428,0.5779802204499079942278058454)
  (2.571428571428571428571428571,0.6308850124153752579541337641)
  (2.714285714285714285714285714,0.6771425457165369193256163032)
  (2.857142857142857142857142857,0.7174458390192943996234368404)
  (3.000000000000000000000000000,0.7525010360986485727517631927)
  (3.142857142857142857142857142,0.7829749879942698159458508626)
  (3.285714285714285714285714285,0.8094722550337551220911958042)
  (3.428571428571428571428571428,0.8325281629388328047823509839)
  (3.571428571428571428571428571,0.8526101345199758064458287717)
  (3.714285714285714285714285714,0.8701229137173289116087300433)
  (3.857142857142857142857142857,0.8854152900121340605812737059)
  (4.000000000000000000000000000,0.8987870701281932147462079378)
  (4.142857142857142857142857142,0.9104956815780882370859628481)
  (4.285714285714285714285714285,0.9207621415644603190499847130)
  (4.428571428571428571428571428,0.9297763100393346042082318535)
  (4.571428571428571428571428571,0.9377014395533425632455706355)
  (4.714285714285714285714285714,0.9446780780755271456938317299)
  (4.857142857142857142857142857,0.9508273974961405102958036830)
  (5.000000000000000000000000000,0.9562540230526573686678401977)


  \comment{\pscircle*[linecolor=green](0,0){0.02}
  \pscircle*[linecolor=green](0.09999999999999999999999999999,80.48304170441465810207415387){0.02}
  \pscircle*[linecolor=green](0.1999999999999999999999999999,55.52726985520684582203751378){0.02}
  \pscircle*[linecolor=green](0.2999999999999999999999999999,27.17000506602518061597947147){0.02}
  \pscircle*[linecolor=green](0.3999999999999999999999999999,10.98479882256070828538534132){0.02}
  \pscircle*[linecolor=green](0.5000000000000000000000000000,3.762772782494687754526881867){0.02}
  \pscircle*[linecolor=green](0.5999999999999999999999999999,1.064972968849014570738009102){0.02}
  \pscircle*[linecolor=green](0.6999999999999999999999999999,0.2297474893069316371408115573){0.02}
  \pscircle*[linecolor=green](0.7999999999999999999999999999,0.03080313107503010887527356618){0.02}
  \pscircle*[linecolor=green](0.8999999999999999999999999999,0.001308573314500336314393575834){0.02}
  \pscircle*[linecolor=green](0.9099999999999999999999999999,0.0008262438444779515009749122133){0.02}
  \pscircle*[linecolor=green](0.9199999999999999999999999999,0.0004964370620328922663686765112){0.02}
  \pscircle*[linecolor=green](0.9299999999999999999999999999,0.0002800849501432968528544120962){0.02}
  \pscircle*[linecolor=green](0.9399999999999999999999999999,0.0001455206587485309187589983425){0.02}
  \pscircle*[linecolor=green](0.9499999999999999999999999999,0.00006755409634153535113332324454){0.02}
  \pscircle*[linecolor=green](0.9599999999999999999999999999,0.00002663758867231954273129854073){0.02}
  \pscircle*[linecolor=green](0.9699999999999999999999999999,0.000008114386827604695137558360759){0.02}
  \pscircle*[linecolor=green](0.9799999999999999999999999999,0.000001543263454951985960816259945){0.02}
  \pscircle*[linecolor=green](0.9899999999999999999999999999,0.00000009287616753530200698777679283){0.02}
  \pscircle*[linecolor=green](1.000000000000000000000000000,0){0.02}
  \pscircle*[linecolor=green](1.009999999999999999999999999,0.00000008613535242271793720817881641){0.02}
  \pscircle*[linecolor=green](1.019999999999999999999999999,0.000001327383632453574056960242273){0.02}
  \pscircle*[linecolor=green](1.029999999999999999999999999,0.000006472849519208711184577737210){0.02}
  \pscircle*[linecolor=green](1.039999999999999999999999999,0.00001970717637751214949546526075){0.02}
  \pscircle*[linecolor=green](1.049999999999999999999999999,0.00004635314997888051879883070784){0.02}
  \pscircle*[linecolor=green](1.059999999999999999999999999,0.00009261061561523324548962036876){0.02}
  \pscircle*[linecolor=green](1.069999999999999999999999999,0.0001653282453215230156051138830){0.02}
  \pscircle*[linecolor=green](1.079999999999999999999999999,0.0002718049657487095079831033217){0.02}
  \pscircle*[linecolor=green](1.089999999999999999999999999,0.0004196181138178058712824946368){0.02}
  \pscircle*[linecolor=green](1.099999999999999999999999999,0.0006164756272648974614962677827){0.02}
  \pscircle*[linecolor=green](1.099999999999999999999999999,0.0006164756272648974614962677827){0.02}
  \pscircle*[linecolor=green](1.199999999999999999999999999,0.006870279746547869692524606156){0.02}
  \pscircle*[linecolor=green](1.299999999999999999999999999,0.02451425929226668848552443352){0.02}
  \pscircle*[linecolor=green](1.399999999999999999999999999,0.05530486595233524676133790522){0.02}
  \pscircle*[linecolor=green](1.500000000000000000000000000,0.09765544070412340678441505311){0.02}
  \pscircle*[linecolor=green](1.599999999999999999999999999,0.1484133662890015288766060733){0.02}
  \pscircle*[linecolor=green](1.699999999999999999999999999,0.2041828834745631596498302481){0.02}
  \pscircle*[linecolor=green](1.799999999999999999999999999,0.2620135130277175192931976650){0.02}
  \pscircle*[linecolor=green](1.899999999999999999999999999,0.3196398583984175776395640284){0.02}
  \pscircle*[linecolor=green](2.000000000000000000000000000,0.3754772776917356690282702906){0.02}
  }

  \endpspicture
\end{center}



\section{Other Functions and Programs}
You can see a complete list of elliptic-curves functions by typing {\tt ?5}:
{\tiny\begin{verbatim}
                                ? ?5
                                elladd          ellak           ellan           ellap
                                ellbil          ellchangecurve  ellchangepoint  elleisnum
                                elleta          ellglobalred    ellheight       ellheightmatrix
                                ellinit         ellisoncurve    ellj            elllocalred
                                elllseries      ellorder        ellordinate     ellpointtoz
                                ellpow          ellrootno       ellsigma        ellsub
                                elltaniyama     elltors         ellwp           ellzeta          ellztopoint
\end{verbatim}}
I have only described a small subset of these.  To understand many of
them, you must first learn how to view an elliptic curve as a
``donut'', that is, as quotient of the complex numbers by a {\em
    lattice}, and also as a quotient of the upper half plane.

There is a Maple package called APECS for computing with elliptic
curves, which is more sophisticated than PARI in certain ways,
especially in connection with algorithms that involve lots of
commutative algebra.  MAGMA also offers sophisticated features for
computing with elliptic curves, which are built in to the standard
distribution.  I will give a demonstrations of MAGMA in the Basic
Notions seminar at 3pm on Monday, December 3 in SC 507. There is also
a C++ library called LiDIA that has libraries with some powerful
elliptic curves features.

%\section{My Curve is Bigger than Yours}

%\section{Computing $E(\Q)$}

%\section{Literature}




\chapter{Elliptic Curve Cryptography}

Today's lecture is about an application of elliptic curves to
cryptography.

\hd{Disclaimer:} I do not endorse breaking laws, and give the examples
below as a pedagogical tool in the hope of making the mathematics in
our course more fun and relevant to everyday life.  I don't think I
have violated the Digital Millenium Copyright Act, because I have
given very few details about Microsoft's actual protocols, and I've
given absolutely no source code.

\section{Microsoft Digital Rights Management}
Today I will describe one way to use elliptic curves in cryptography.
Our central example will involve
version 2 of the Microsoft Digital Rights Management (MS-DRM) system,
as applied to {\tt .wma} audio files.
\begin{center}
  \includegraphics[width=1in]{wma.eps}
\end{center}
I learned about this protocol from
a paper by ``Beale Screamer''.

\subsection{Microsoft's Favorite Elliptic Curve}
\noindent{}The elliptic curve used in MS-DRM is an elliptic curve over the finite
field $k=\Z/p\Z$, where
$$
  p=785963102379428822376694789446897396207498568951.
$$
As Beale Screamer remarks, this modulus has high nerd appeal because in
hexadecimal it is
\begin{center}
  89ABCDEF012345672718281831415926141424F7,
\end{center}
which
includes counting in hexadecimal, and digits of~$e$,
$\pi$, and $\sqrt{2}$.
The Microsoft elliptic curve~$E$ is
\begin{align*}
  y^2 = x^3 & + 317689081251325503476317476413827693272746955927x      \\
            & \qquad +79052896607878758718120572025718535432100651934.
\end{align*}
We have
$$\# E(k) = 785963102379428822376693024881714957612686157429,$$
and the group $E(k)$ is cyclic with generator
\begin{align*}
  B & = (771507216262649826170648268565579889907769254176,      \\
    & \qquad 390157510246556628525279459266514995562533196655).
\end{align*}
\subsection{Nikita and Michael}
\vspace{-4ex}Our heros Nikita and Michael love to share digital music
\includegraphics[width=1in]{notes.eps}
when they aren't out thwarting terrorists.
When Nikita installed Microsoft's
content rights management software on
her compute, it sneakily generated a private key
$$
  n = 670805031139910513517527207693060456300217054473,
$$
which it very stealthily hid in bits and pieces of files (e.g., {\tt
    blackbox.dll}, {\tt v2ks.bla}, and {\tt IndivBox.key}).  In order for
Nikita to play Juno Reactor's latest hit {\tt juno.wma}, her web
browser contacts a Microsoft rights management partner.  After Nikita
gives Microsoft her credit card number, she is allowed to download a
license to play {\tt juno.wma}.  Microsoft created the license using
the ElGamal public-key cryptosystem (see below) in the group $E(k)$.
Nikita's license file can now be used to unlock {\tt juno.wma}, but
  {\em only} on Nikita's computer.  When she shares both {\tt juno.wma}
and the license file with Michael, he is very annoyed because he can't
play {\tt juno.wma}. This is because Michael's computer doesn't know
Nikita's computer's private key (that integer~$n$ above),
so Michael's computer can't decrypt the license file.
\begin{center}
  \includegraphics[width=1.5in]{juno.eps}\\
  {\tt juno.wma}
\end{center}


\section{The Elliptic Curve Discrete Logarithm Problem}
\begin{definition}
  If $E$ is an elliptic curve over $\Z/p\Z$ and~$B$ is a point on~$E$,
  then the {\em discrete log problem} on~$E$ to the base~$B$ is the
  following problem: given a point $P\in E$, find an integer~$n$
  such that $nB = P$, if such an integer exists.
\end{definition}

For example, let $E$ be the elliptic curve given by $y^2 = x^3 + x+1$
over the field $\Z/7\Z$.  We have
$$
  E(\Z/7\Z) = \{\O, (2, 2), (0,1), (0,6), (2,5) \}.
$$
If $B=(2,2)$ and $P=(0,6)$, then $3B=P$, so $n=3$
is a solution to the discrete logarithm problem.

To the best of my knowledge, the discrete logarithm problem on~$E$
is {\em really hard} unless $\#E(\Z/p\Z)$ is ``smooth'', i.e., a
product of small primes, or $E$ is ``supersingular'' in the sense
that $p\mid \#E(\Z/p\Z)$.
The Microsoft curve has neither of these deficiencies, and I
expect that the discrete logarithm on that curve is quite difficult.
This is not the weekness that ``Beale Screamer'' exploits in
breaking MS-DRM.

\section{ElGamal}
How can we set up a public-key cryptosystem using an elliptic curve?
The only public-key crytosystem that we've studied so far is the RSA
cryptosystem; unfortunately, there is no analogue of RSA for elliptic
curves!  (Informal Exercise: Think about what goes wrong.)

MS-DRM uses the El Gamal system.  Here's how it works.
Start with a fixed, publicly known prime~$p$, an elliptic
curve~$E$ over $\Z/p\Z$, and a point $B\in E(\Z/p\Z)$.
Michael and Nikita choose random integers~$m$ and~$n$, which
are kept secret, and compute and publish $mB$ and $nB$.

In order to send a message $P$ to Michael, Nikita
computes a random integer~$r$ and sends the pair of
points $(rB, P+r(mB))$.  To read the message,
Michael multiplies $rB$ by his secret key~$m$
to get $m(rB) = r(mB)$, and subtracts this from
the second point to get
$$P = P+r(mB)-r(mB).$$

As far as I can tell, breaking this cryptosystem requires solving the
discrete logarithm problem, so it's very difficult.

The following example is based on an example taken from Beale
Screamer's paper.
\begin{example}
  Nikita's license files contains the pair of points
  $(rB,P+r(nB))$, where
    {\tiny$$
        rB = (179671003218315746385026655733086044982194424660,
        697834385359686368249301282675141830935176314718)
      $$}
  and
    {\tiny$$
        P+r(nB) = (137851038548264467372645158093004000343639118915,
        110848589228676224057229230223580815024224875699).
      $$}
  Nikita's computer sneakily loads the secret key
  $$
    n = 670805031139910513517527207693060456300217054473
  $$ into memory
  and computes
    {\tiny$$n(rB) = r(nB) =
        (328901393518732637577115650601768681044040715701,
        586947838087815993601350565488788846203887988162).
      $$}
  It then subtracts this from $P+r(nB)$ to get
  $$
    P = (14489646124220757767, \,669337780373284096274895136618194604469696830074).
  $$
  That~$x$ coordinate, $14489646124220757767$, is the top secret magic ``content
  key'' that unlocks {\tt juno.wma}.
\end{example}

If Nikita knew the private key~$n$ that her computer generated, she
could compute~$P$ herself and unlock {\tt juno.wma} and share her
music with Michael, just like she used to share her favorite CDs with
Michael.  Beale Screamer found a weakness in Microsoft's system that
let him find~$n$:
\begin{quote}
  ``These secret keys are stored in linked lists ... interspersed with the
  code in the library.  The idea is that they can be read by that
  library, used internally by that library, and never communicated
  outside the library.  Since the {\tt IndivBox.key} file is shuffled in
  a random way for each client, these keys would be extremely difficult
  to extract from the file itself. Fortunately, we don't have to: these
  keys are part of the object state that is maintained by this library,
  and since the offset within this object of these secret keys is known,
  we can let the library itself extract the secret keys!  The code for
  this simply loads up the `black box' library, has it initialize an
  instance of the object, and then reads the keys right out of that
  object.  This is clearly a weakness in the code which can be corrected
  by the DRM software fairly easily, but for now it is the basis of our
  exploit.''
\end{quote}

As you can see, Microsoft has undertaken a difficult and interesting
problem.  {\em How can Microsoft  store data on Nikita's
    computer in such a way that Nikita can not access it, but Nikita's
    computer can?}

\section{Why Use Elliptic Curves?}
There are several advantages to using elliptic curves instead
of $\Z/p\Z$ for cryptography, though the people at RSA Corporation
might disagree.  Elliptic curve cryptosystems with smaller key sizes
appear to be just as secure as ``classical'' $\Z/p\Z$ cryptosystems
with much larger key sizes, so elliptic curve cryptosystems can
be more efficient.  Another advantage, which I won't explain
at all, is that elliptic curve cryptosystems appear to be vastly
more secure over
``large finite fields of characteristic~$2$'' than RSA, which is
is very important in practical applications.  Also,
elliptic curves are simply way cooler than $\Z/p\Z$, so
they (used to) attract venture capitalists.

Some mobile phones also use elliptic curve cryptography.  Do you have an
elliptic curve in your pocket right now?


\begin{verbatim}
/* Base conversion */

function ToDeci(s)
   c := ["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];
   ans := 0;
   b := 1;
   for i in [1..#s] do
      ans := ans + b*(Index(c,s[#s-i+1])-1);
      b := b*16;
   end for;  
   return ans;
end function;


p := 785963102379428822376694789446897396207498568951
k := GF(p);
E := EllipticCurve([317689081251325503476317476413827693272746955927,  79052896607878758718120572025718535432100651934]);
B  := E![k|771507216262649826170648268565579889907769254176, 390157510246556628525279459266514995562533196655];
rB := E![k|179671003218315746385026655733086044982194424660,697834385359686368249301282675141830935176314718];
PrnB := E![k|137851038548264467372645158093004000343639118915,110848589228676224057229230223580815024224875699];
n := 670805031139910513517527207693060456300217054473;
PrnB - n*rB;
(14489646124220757767 : 669337780373284096274895136618194604469696830074 : 1)
\end{verbatim}

\chapter{Using Elliptic Curves to Factor, Part I}

In 1987, Hendrik Lenstra published the landmark paper {\em Factoring
    Integers with Elliptic Curves}, Annals of Mathematics, {\bf 126},
649--673, which you can download from the Math 124 web page.
Lenstra's method is also described in \S{}IV.4 of Silverman and Tate's {\em
Rational Points on Elliptic Curves}, \S{}VIII.5 of [Davenport], and in
\S10.3 of Cohen's {\em A Course in Computational Algebraic Number
Theory}.

In this lecture and the next, I will tell you about Lenstra's clever
algorithm.  It shines at finding ``medium sized'' factors of an
integer~$N$, which these days means~$10$ to~$20$ decimal digits but
probaby not $30$ decimal digits.  The ECM method is thus not useful
for earning money by factoring RSA challenge numbers, but is
essential when factoring most integers.  It also has small storage
requirements.   Lenstra writes:\vspace{1ex}

\begin{tabular}{lr}
  \begin{minipage}{.6\textwidth}
    {\sl ``It turns out that ... the elliptic curve method is one
      of the fastest integer factorization methods that is currently
      used in practice.  The quadratic sieve algorithm still seems
      to perform better on integers that are built up from two prime
      numbers of the same order of magnitude; such integers are of interest
      in cryptography.''}
  \end{minipage}\hspace{.5in}
   &
  \begin{minipage}{1.5in}
    \includegraphics[width=1.5in]{lenstra.eps}
  \end{minipage}
\end{tabular}

%Because one takes apart an integer using several different methods in
%concert, so it is necessary to know a wide range of factorization
%algorithms.

Lenstra's discover of the elliptic curve method was inspired by
Pollard's $(p-1)$-method.  I will spend most of the rest
of this lecture introducing you to it.

\section{Power-Smoothness}
\begin{definition}[Power-smooth]
  Let $B$ be a positive integer.  A positive integer~$n$ is
    {\em $B$-power-smooth} if all prime powers dividing~$n$ are
  less than or equal to~$B$.  The {\em power-smoothness} of~$n$ is
  the largest~$B$ such that~$n$ is $B$-power-smooth.
\end{definition}

The following two PARI functions compute whether or not an
integer is $B$-power-smooth and also the power-smoothness of~$n$.
\begin{verbatim}
{ispowersmooth(n, B) =   \\ true if and only if n is B-powersmooth
   local(F,i);
   F = factor(n);
   for(i=1,matsize(F)[1],if(F[i,1]^F[i,2]>B,return(0)));
   return(1);
}

{powersmoothness(n) =    \\ the powersmoothness of n.
   local(F,L,i);
   F = factor(n);
   L = 1;
   for(i=1,matsize(F)[1],L=max(L,F[i,1]^F[i,2]));
   return(L);
}
\end{verbatim}

\section{Pollard's $(p-1)$-Method}
Let~$N$ be an integer that we wish to factor.  Choose a positive
integer~$B$ (usually $\leq 10^6$ in practice).  The Pollard
$(p-1)$-method hunts for prime divisors~$p$ of~$N$ such that $p-1$ is
$B$-power-smooth.  Here is the strategy.
Suppose that $p\mid N$ and $a>1$ is an integer that is prime to~$p$.
By Fermat's Little Theorem,
$$
  a^{p-1}\con 1\pmod{p}.
$$
Assume that further that $p-1$
is $B$-power-smooth and let $m=\lcm(1,2,3,\ldots B)$.  Then
$B\mid m$, so
$p-1\mid m$, and so
$$
  a^m \con 1 \pmod{p}.
$$
Thus
$$
  p\mid \gcd(a^m-1,N) > 1.
$$
Usually $\gcd(a^m-1,N)<N$ also, and when this is the case
we have split~$N$.  In the
unlikely case when $\gcd(a^m-1,N)=N$, then $a^m\con 1\pmod{q^r}$ for
every prime power divisor of~$N$.  In this case, repeat the above
steps but with a smaller choice of~$B$ (so that~$m$ is smaller).
Also, it's a good idea to check from the start whether or
not~$N$ is not a perfect power $M^r$, and if so replace~$N$ by~$M$.

In practice, we don't know~$p$.  We choose a~$B$, then an~$a$,
cross our fingers, and proceed.  If we split~$N$, great!
If not, increase~$B$ or change~$a$ and try again.

For fixed~$B$, this algorithm works when~$N$ is divisible by a
prime~$p$ such that $p-1$ is $B$-power-smooth.  How many primes $p$
have the property that $p-1$ is $B$-power-smooth?    Is this very
common or not?
Using the above two functions, we find that roughly 15\% of
primes~$p$ between $10^{15}$ and $10^{15}+10000$ are such that
$p-1$ is $10^6$ power-smooth.

\begin{verbatim}
\\ Count the number of B-power-smooth numbers an interval.
{cnt(B)= s=0;t=0; 
   for(p=10^15, 10^15+10000,  
      if(isprime(p), 
         t++;if(ispowersmooth(p-1,B),s++)
      )
   ); 
   s/t*1.0
}
? cnt(10^6)
%5 = 0.1482889733840304182509505703
\end{verbatim}
Thus the Pollard $(p-1)$-method with $B=10^6$ is blind to 85\% of the
primes around~$10^{15}$.  There are nontrivial theorems about
densities of power-smooth numbers, but I will not discuss them today.

\section{Pollard's Method in Action!}
We now illustrate the Pollard $(p-1)$-method through several
examples.

\begin{example}
  Let $N=5917$.  We try to use the Pollard $p-1$ method with
  $B=5$ to split~$N$.  We have
  $m = \lcm(1,2,3,4,5)=60$.  Take $a=2$.  We have
  $$
    2^{60} - 1 \con 3416\pmod{5917},\qquad\text{(can compute quickly!)}
  $$
  so
  $$
    \gcd(2^{60}-1,5917) = \gcd(3416,5917) = 61.
  $$
  Wow, we found a prime factor of~$N$!

  In PARI, these computations are carried out as follows:
  \begin{verbatim}
      {lcmfirst(B) =  \\ compute the lcm of 1,2,3,...,B
          local(L,i);
          L=1;
          for(i=2,B,L=lcm(L,i));
          return(L);}
      ? lcmfirst(5)
      %8 = 60
      ? Mod(2,5917)^60 - 1
      %9 = Mod(3416, 5917)
      ? gcd(3416,5917)
      %10 = 61
\end{verbatim}
\end{example}

\begin{example}
  Let $N=779167$.  First try $B=5$ and $a=2$:
  $$
    2^{60}-1 \con 710980\pmod{N},
  $$
  and $\gcd(2^{60}-1,N) = 1.$
  Thus no prime divisor~$p$ of~$N$ has the property that
  $p-1$ is $5$-power-smooth.  Next, we try $B=15$.
  We have $m=\lcm(1,2,\ldots,15)=360360$, and
  $$
    2^{360360}-1 \con 584876\pmod{N},
  $$
  so
  $$\gcd(2^{360360}-1 , N) = 2003,$$
  and we have split~$N$!
\end{example}


\begin{example}
  Let $N=61\cdot 71$.  Then both $61-1=60=2^2\cdot 3\cdot 5$
  and $71-1=2\cdot 5\cdot 7$ are $7$-power-smooth, so
  Pollard's $(p-1)$-method with any $B\geq 7$ will fail, but
  in a confidence-inspiring way.
  Suppose  $B=7$, so
  $
    m=\lcm(1,2,\ldots,7)=420.
  $
  Then
  $$
    2^{420} - 1 \con 0 \pmod{N},
  $$
  so
  $\gcd(2^{420} - 1, N) = N$,
  and we get nothing.
  If we shrink~$B$ to $5$, then Pollard works:
  $$
    2^{60} - 1\con 1464\pmod{N},
  $$
  and $\gcd(2^{60}-1,N) = 61$, so we split~$N$.
\end{example}


\section{Motivation for the Elliptic Curve Method}
Fix an integer~$B$.  If $N=pq$ with $p$ and $q$ prime and
neither $p-1$ nor $q-1$ a $B$-power-smooth number, then
the Pollard $(p-1)$-method
is extremely unlikely to work.  For example,
let $B=20$ and
suppose that $N=59\cdot 101 = 5959$.  Note that
neither~$59-1=2\cdot29$ nor $107-1=2\cdot 53$ is $B$-power-smooth.
With $m=\lcm(1,2,3,\ldots,20)=232792560$, we have
$$2^m - 1 \con 5944\pmod{N},$$
and
$\gcd(2^m-1,N)=1$, so we get nothing.

As remarked above, the problem is that $p-1$ is not $20$-power-smooth for
either $p=59$ or $p=101$.  However, notice that $p-2=3\cdot 19$ is
$20$-power-smooth!  If we could somehow
replace the group $(\Z/p\Z)^*$, which has order $p-1$, by a group
of order $p-2$, and compute $a^m$ for an element of this {\em new}
group, then we might easily split~$N$.  Roughly speaking, this is
what Lenstra's elliptic curve factorization method does; it
replaces $(\Z/p\Z)^*$ by an elliptic curve~$E$ over $\Z/p\Z$.
The order of the group $E(\Z/p\Z)$ is $p+1\pm s$ for some
nonnegative integer $s<2\sqrt{p}$ (any $s$ can occur).
For example, if~$E$ is the elliptic curve
$$
  y^2 = x^3 + x + 54
$$
over $\Z/59\Z$ then $E(\Z/59\Z)$ is cyclic of order~$57$.
The set of numbers $59+1\pm s$ for $s\leq 15$ contain
numbers with very small power-smoothness.

I won't describe the elliptic curve factorization method until the
next lecture.  The basic idea is as follows.  Suppose that we wish to
factor~$N$.  Choose an integer~$B$.  Choose a random point~$P$ and a
random elliptic curve $y^2=x^3+ax+b$ ``over $\Z/N\Z$'' that goes
through~$P$.  Let $m=\lcm(1,2,\ldots,B)$.  Try to compute~$mP$ working
modulo~$N$ and using the group law formulas.  If at some
point it is necessary to divide modulo~$N$, but division is not
possible, we (usually) find a nontrivial factor of~$N$.  Something
going wrong and not being able to divide is analogous to $a^m$ being
congruent to~$1$ modulo~$p$.

More details next time!



\section{The Elliptic Curve Method}
\begin{verbatim}
{isalmostpowersmooth(p,B)= local(r);
   for(r=p+1-floor(2*sqrt(p)),p+1+floor(2*sqrt(p)),
   if(ispowersmooth(r,B), return(1)) ) }
   cnt(B)=s=0;t=0;for(p=10^15,10^15+10000,
   if(isprime(p),t++;if(isalmostpowersmooth(p,B),s++,print("BAD
   ",p));print(s/t*1.0))); s/t*1.0
\end{verbatim}

\section{The Method in Action!}




\comment{
for a in [0..58] do
for b in [0..58] do
d := [GF(59)|a,b];
t, E := IsEllipticCurve(d);
if t and #E eq 57 then
print E;
error "done";
end if;
end for;
end for;
}

\chapter{Using Elliptic Curves to Factor, Part II}

I constructed
$
  N = 800610470601655221392794180058088102053408423
$
by multiplying together five random (and promptly
forgotten) primes~$p$ with the property that $p-1$ is not
$B$-power-smooth for $B=10^8$.  Since~$N$ is a product of five
not-too-big primes,~$N$ begs to be factored using the elliptic
curve method.

\section{The Elliptic Curve Method (ECM)}
The following description of the algorithm is taken from Lenstra's
paper [{\em Factoring Integers with Elliptic Curves}, Annals of Mathematics,
    {\bf 126}, 649--673], which you can download from the Math 124
web page.\vspace{1em}

\hspace{-4em}\noindent\begin{tabular}{lr}
  \begin{minipage}{1.5in}
    \includegraphics[width=1.5in]{cohen_lenstra.eps}
    \vspace{-2em}
    \begin{center}
      \small Cohen and Lenstra
    \end{center}
    \vspace{.4in}
  \end{minipage}
   & \begin{minipage}{.77\textwidth}
       ``The new method is obtained from Pollard's $(p-1)$-method by replacing
       the multiplicative group by the group of points on a random elliptic curve.
       To find a non-trivial divisor of an integer $n>1$, one begins by
       selecting an elliptic curve~$E$ over $\Z/n\Z$, a point~$P$ on~$E$
       with coordinates in $\Z/n\Z$, and an integer~$k$ as above
         [$k=\lcm(2,3,\ldots,B)$].
       Using the addition law of the curve, one next calculates the
       multiple $k\cdot P$ of $P$.  One now hopes that there is a prime
       divisor~$p$ of~$n$ for which $k\cdot P$ and the neutral element~$\O$
       of the curve become the same modulo~$p$; if~$E$ is
       given by a homogeneous Weierstrass equation
       $y^2 z = x^3 + axz^2 + bz^3$, with $\O=(0:1:0)$, then this
       is equivalent to the $z$-coordinate of $k\cdot P$ being divisible
       by~$p$.  Hence one hopes to find a  non-trivial factor of~$n$
       by calculating the greatest common divisor of this $z$-coordinate with~$n$.''
     \end{minipage}\hspace{.1in}
  \vspace{1em}
\end{tabular}

If the above algorithm fails with a specific elliptic curve~$E$, there
is an option that is unavailable with Pollard's $(p-1)$-method.  We may
repeat the above algorithm with a different choice of~$E$.  The number
of points on~$E$ over $\Z/p\Z$ is of the form $p+1-t$ for some~$t$
with $|t|<2\sqrt{p}$, and the algorithm is likely to succeed if
$p+1-t$ is $B$-power-smoth.

Suppose that $P=(x_1,y_1)$ and $Q=(x_2,y_2)$ are nonzero points
on an elliptic curve $y^2 = x^3 + ax + b$ and that $P\neq \pm Q$.
Let $\lambda = (y_1-y_2)/(x_1-x_2)$ and
$\nu = y_1 - \lambda x_1$.
Recall that $P+Q = (x_3,y_3)$ where
$$x_3 = \lambda^2 -x_1 - x_2\qquad\text{and}\qquad
  y_3 = -\lambda x_3 - \nu.$$

If we do arithmetic on an elliptic curve modulo~$N$ and at some point
we can not compute~$\lambda$ because we can not compute the inverse
modulo~$N$ of $x_1-x_2$, then we (usually) factor~$N$.

\section{Implementation and Examples}
For simplicity, we use an elliptic curve
of the form
$$y^2 = x^3 + ax + 1,$$
which has the point $P=(0,1)$ already on it.

The following tiny PARI function implements the ECM.  It
generates an error message along with a usually nontrivial factor of~$N$
exactly when the ECM succeeds.
\begin{verbatim}
{ECM(N, m)= local(E);
   E = ellinit([0,0,0,random(N),1]*Mod(1,N));
   print("E: y^2 = x^3 + ",lift(E[4]),"x+1,  P=[0,1]");
   ellpow(E,[0,1]*Mod(1,N),m);  \\ this fails if and only if we win!
}
\end{verbatim}
The following two functions are also useful:
\begin{verbatim}
{lcmfirst(B) =  
   local(L,i); L=1; for(i=2,B,L=lcm(L,i));
   return(L);
}
numpoints(a,p) = return(p+1 - ellap(ellinit([0,0,0,a,1]),p));
\end{verbatim}

First we will try the program on a small integer~$N$, then we will try
it on the~$N$ at the top of this lecture.  ({\tt ECM} uses the random
function, so the results of your run may differ from the one below.)

\begin{verbatim}
? N = 5959;           \\ This number motivated the ECM last time.
\\ Recall what happened when we tried to factor 5959 using the p-1 method.
? m = lcmfirst(20);   \\ B = 20.
? Mod(2,N)^m-1
%108 = Mod(5944, 5959)
? gcd(5944,5959)
%109 = 1               \\ bummer!
\\ Now we try the ECM:
? ECM(N,m)
E: y^2 = x^3 + 1201x+1,  P=[0,1]
%112 = [Mod(666, 5959), Mod(3229, 5959)]
? ECM(N,m)
E: y^2 = x^3 + 1913x+1,  P=[0,1]
  ***   impossible inverse modulo: Mod(101, 5959).
\\ Wonderful!! There's a factor--------/\
? factor(numpoints(1913,101))
%120 =
[2 4]                          \\ #E(Z/101) is 16-power-smooth, 
[7 1]                          \\ so ECM sees 101.
? factor(numpoints(1913,59))
%119 =
[2 1]                          \\ #E(Z/59) is 29-power-smooth,
[29 1]                         \\ so ECM doesn't see 59.

\\ Here's the view from another angle:
? E = ellinit([0,0,0,1752,0]*Mod(1,5959));
? P = [0,1]*Mod(1,5959);
? ellpow(E,P,2)
%127 = [Mod(4624, 5959), Mod(1495, 5959)]
? ellpow(E,P,3)
%128 = [Mod(3435, 5959), Mod(1031, 5959)]
? ellpow(E,P,4)
%129 = [Mod(803, 5959), Mod(5856, 5959)]
? ellpow(E,P,8)
%133 = [Mod(1347, 5959), Mod(2438, 5959)]
? ellpow(E,P,m)
  ***   impossible inverse modulo: Mod(101, 5959).
\end{verbatim}

Now we are ready to try the big integer~$N$ from the begining of the lecture.
\begin{verbatim}
? N = 800610470601655221392794180058088102053408423;
? B = 100;
? m = lcmfirst(B);
? ECM(N,m);
E: y^2 = x^3 + 273687051132207711452727265152539544370874547x+1,  P=[0,1]
... many tries .. 
? ECM(N,m);
E: y^2 = x^3 + 174264237886300715545169749498695137077020788x+1,  P=[0,1]
? B=1000;  \\ give up and try a bigger B.
? m=lcmfirst(B);
? ECM(N,m);
E: y^2 = x^3 + 652986182461202633808585244537305097270008449x+1,  P=[0,1]
... many tries ...
? ECM(N,m);
E: y^2 = x^3 + 755060727645891482095225151281965348765197238x+1,  P=[0,1]
? B=10000; \\ try an even bigger B
? m=lcmfirst(B);
? ECM(N,m);
E: y^2 = x^3 + 722355978919416556225676818691766898771312229x+1,  P=[0,1]
? ECM(N,m);
E: y^2 = x^3 + 124781379199538996805045456359983628056546634x+1,  P=[0,1]
? ECM(N,m);
E: y^2 = x^3 + 350310715627251979278144271594744514052364663x+1,  P=[0,1]
? ECM(N,m);
E: y^2 = x^3 + 39638500146503230913823829562620410547947307x+1,  P=[0,1]
  ***   impossible inverse modulo: Mod(1004320322301182911, 
                            800610470601655221392794180058088102053408423).
\end{verbatim}
Thus
$
  N = N_1 \cdot N_2 = 1004320322301182911 \cdot 797166454590134548773760793.
$
One checks that neither $N_1$ nor $N_2$ is prime.  Next we try ECM on each:
\begin{verbatim}
? N1 = 1004320322301182911; N2 = N / N1;
? ECM(N1,m);
E: y^2 = x^3 + 725771039569085210x+1,  P=[0,1]
  ***   impossible inverse modulo: Mod(1406051123, 1004320322301182911).
? ECM(N2,m);
E: y^2 = x^3 + 573369475441522110156437806x+1,  P=[0,1]
  ***   impossible inverse modulo: Mod(2029256729, 
                                        797166454590134548773760793).
\end{verbatim}
Now
$$N = N_{1,1}\cdot N_{1,2} \cdot N_{2,1} \cdot N_{2,2}
  = 1406051123\cdot 714284357 \cdot 2029256729 \cdot  392836669307471617,$$
and one can check that $N_{1,1}$, $N_{1,2}$, $N_{2,1}$ are
prime but that $N_{2,2}$ is composite.  Again, we apply ECM:
\begin{verbatim}
? N22 = 392836669307471617
%173 = 392836669307471617
? ECM(N22,m)
E: y^2 = x^3 + 133284810657519512x+1,  P=[0,1]
%174 = [0]
? ECM(N22,m)
E: y^2 = x^3 + 368444010842952211x+1,  P=[0,1]
%175 = [Mod(236765299763600601, 392836669307471617), 
                       Mod(63845045623767003, 392836669307471617)]
? ECM(N22,m)
E: y^2 = x^3 + 245772885854824846x+1,  P=[0,1]
%176 = [0]
? ECM(N22,m)
E: y^2 = x^3 + 33588046732320063x+1,  P=[0,1]
  ***   impossible inverse modulo: Mod(615433499, 392836669307471617).
\end{verbatim}
This time it took a long time to factor $N_{2,2}$ because~$m$
is too large so we often get both factors.   A smaller~$m$ would
have worked more quickly.
In any case, we discover that the prime factorization is
$$N = 1406051123\cdot 714284357 \cdot 2029256729 \cdot 615433499 \cdot 638308883.$$



\section{How Good is ECM?}

According to Henri Cohen (page 476 of {\em A Course in Computational
    Algebraic Number Theory}):
\begin{quote}
  ``Unique among modern factoring algorithms however, it is sensitive to
  the size of the prime divisors .  In other words, its running time
  depends on the size of the smallest prime divisor~$p$ of~$N$, and not
  on~$N$ itself.  Hence, it can be profitably used to remove ``small''
  factors [...]. Without too much trouble, it can find prime factors
  having~$10$ to~$20$ decimal digits [with $B$ around $10^4$].
  On the other hand, it very rarely finds prime factors having
  more than~$30$ decimal digits.
\end{quote}




\chapter{Fermat's Last Theorem and Modularity of Elliptic Curves}



In this lecture I will sketch an outline of the proof of Fermat's
last theorem, then give a  rigorous account of what
it means for an elliptic curve to be ``modular''.

The are several exercises below.  They are
optional, but if you do them and give them to Grigor, I suspect that
he would look at them (whether or not you do the exercises will
not directly affect your course grade in any way).

\section{Fermat's Last Theorem}

\begin{theorem}
  Let $n>2$ be an integer.  If $a, b, c\in\Z$ and
  $$a^n + b^n = c^n,$$
  then $abc=0$.
\end{theorem}


\begin{proof}[Proof (sketch)]
  First reduce to the case when $n=\ell$ is
  a prime greater than~$3$ (see Exercise~\ref{ex:prime}).  Suppose that
  $$
    a^\ell + b^\ell = c^\ell
  $$
  with $a, b,c\in\Z$ and $abc\neq 0$.
  Permuting $(a,b,c)$, we may suppose that $b$ is even and
  that we have $a\con 3\pmod{4}$.
  Following Gerhard Frey, consider the elliptic curve~$E$ defined by
  $$
    y^2 = x(x-a^{\ell})(x+b^{\ell}).
  $$
  The discriminant of~$E$ is~$2^4(abc)^{2\ell}$
  (see Exercise~\ref{ex:disc} below).


  Andrew Wiles and Richard Taylor [Annals of Math., May 1995] proved that~$E$
  must be ``modular''.  This means that there is a ``modular form''
  $$f(z) = \sum_{n=1}^{\infty} a_n e^{2\pi i n z}$$
  of ``level $N=abc$''
  such that for all primes $p\nmid abc$,
  $$
    a_p = p+1-\#E(\Z/p\Z).
  $$

  Ken Ribet [Inventiones Math., 1991] used that the
  discriminant of~$E$ is a perfect~$\ell$th power (away from~$2$)
  to prove that there is  a cuspidal modular form
  $$
    g(z) = \sum_{n=1}^{\infty} b_n e^{2\pi i n z}
  $$
  of ``level~$2$'' such that
  $$
    a_p \con b_p \pmod{\ell}\qquad\text{for all }p\nmid abc.
  $$
  This is a contradiction because
  the space of ``cuspidal modular forms'' of level~$2$
  has dimension~$0$ (see Section~\ref{sec:dim}).
\end{proof}

\begin{exercise}\label{ex:prime}
  Reduce to the prime case.  That is, show that if Fermat's last theorem is
  true for prime exponents, then it is true.
\end{exercise}

\begin{exercise}\label{ex:disc}
  Prove that
  $y^2 = x(x-a^{\ell})(x+b^{\ell})$ has
  discriminant $2^4(abc)^{2\ell}$.
\end{exercise}
% disc = (al^2*bl^2*2^4)*(al+bl)^2 = (al^2*bl^2*2^4)*(cl)^2 = (a*b*c)^(2l)*2^4.

The rest of this lecture is about the words in the proof that
are in quotes.

\section{Holomorphic Functions}
The complex {\em upper half plane} is the set
$$
  \h = \{z\in\C \,:\, \Im(z) > 0\}.
$$
A {\em holomorphic function} $f:\h\ra\C$ is a function
such that for all $z\in\h$ the derivative
$$
  f'(z) = \lim_{h\ra 0} \frac{f(z+h) - f(z)}{h}
$$
exists.  Holomorphicity is a very strong condition
because $h\in\C$ can approach~$0$ in many ways.

\begin{example}
  Let $\SL_2(\Z)$ denote the set of $2\times 2$ integers matrices
  with determinant~$1$.   If $\gamma=\abcd{a}{b}{c}{d}\in\SL_2(\Z)$,
  then the corresponding {\em linear fractional transformation}
  $$
    \gamma(z) = \frac{az+b}{cz+d}
  $$
  is a holomorphic function on $\h$.  (Note that the only possible
  pole of $\gamma$ is $-\frac{d}{c}$, which is not an element of~$\h$.)

  For future use, note that if $f : \h\ra \C$ is a holomorphic
  function, and $\gamma=\abcd{a}{b}{c}{d}\in\SL_2(\Z)$, then
  $$
    f|_\gamma(z) = f(\gamma(z)) (cz+d)^{-2}
  $$
  is again a holomorphic function.
\end{example}

\begin{example}
  Let $q(z) = e^{2\pi i z}$.  Then~$q$ is a holomorphic function
  on~$\h$ and $q'=2\pi i q$.  Moreover,~$q$ defines a surjective
  map from $\h$ onto the punctured open unit
  disk $D=\{z\in\C : 0<|z|<1\}$.
\end{example}

\section{Cuspidal Modular Forms}
Let~$N$ be a positive integer and consider the set
$$
  \Gamma_0(N) = \left\{\mtwo{a}{b}{c}{d}\in\SL_2(\Z) \,:\, N \mid c\right\}.
$$

\begin{definition}[Cuspidal Modular Form]
  A {\em cuspidal modular form} of level~$N$ is a holomorphic function
  $
    f : \h \ra \C
  $
  such that
  \begin{enumerate}
    \item
          $f|_\gamma = f$ for all $\gamma\in\Gamma_0(N)$,
    \item
          for every $\gamma\in\SL_2(\Z)$,
          $$\lim_{z\ra \infty} f(\gamma(z)) = 0,$$ and
    \item
          $f$ has a Fourier expansion:
          $$
            f = \sum_{n=1}^{\infty} a_n  q^n.
          $$
  \end{enumerate}
\end{definition}

\begin{exercise}
  Prove that condition 3 is implied by conditions 1 and 2, so condition
  3 is redundant.  [Hint: Since
      $\gamma=\abcd{1}{1}{0}{1}\in\Gamma_0(N)$, condition 1 implies that
      $f(z+1)=f(z)$, so there is a function $F(q)$ on the open punctured
      unit disc such that $F(q(z)) = f(z)$.
      Condition 2 implies that $\lim_{q\ra 0} F(q) = 0$,
      so by complex analysis $F$ extends to a holomorphic function on the
      full open unit disc.]


\end{exercise}



\begin{definition}
  The {\em $q$-expansion} of~$f$ is the Fourier expansion
  $
    f = \sum_{n=1}^\infty a_n q^n.
  $
\end{definition}

\begin{exercise}
  Suppose that $f\in S_2(\Gamma_0(N))$.  Prove that
  $$
    f(z) dz = f(\gamma(z))d(\gamma(z))
  $$
  for all $\gamma\in\Gamma_0(N)$.  [Hint: This is simple
      algebraic manipulation.]
\end{exercise}

\begin{exercise}
  Let $S_2(\Gamma_0(N))$ denote the set of cuspidal modular forms
  of level~$N$.
  Prove that
  $S_2(\Gamma_0(N))$ forms a $\C$-vector space under addition.
\end{exercise}

\subsection{The Dimension of $S_2(\Gamma_0(N))$}\label{sec:dim}
The dimension of $S_2(\Gamma_0(N))$ is
$$
  \dim_\C S_2(\Gamma_0(N)) =
  1+ \frac{\mu}{12} - \frac{\nu_2}{4} - \frac{\nu_3}{3}
  - \frac{\nu_\infty}{2},
$$
where
$\mu = N \prod_{p\mid N} (1+1/p)$,
and $\nu_2 = \prod_{p\mid N} \left(1+\kr{-4}{p}\right)$ unless $4\mid N$ in which case
$\nu_2=0$, and
$\nu_3 = \prod_{p\mid N} \left(1+\kr{-3}{p}\right)$ unless $2\mid N$ or $9\mid N$
in which case $\nu_3=0$, and
$\nu_\infty = \sum_{d\mid N} \vphi(\gcd(d,N/d))$.
For example,
$$
  \dim_\C S_2(\Gamma_0(2)) =
  1+ \frac{3}{12} - \frac{1}{4} - \frac{0}{3} - \frac{2}{2} = 0,
$$
and
$$
  \dim_\C S_2(\Gamma_0(11)) =
  1+ \frac{12}{12} - \frac{0}{4} - \frac{0}{3} - \frac{2}{2} = 1.
$$
One can prove that the vector space $S_2(\Gamma_0(11))$ has basis
$$
  f = q\prod_{n=1}^{\infty}(1-q^n)^2(1-q^{11n})^2 =
  q - 2q^2 - q^3 + 2q^4 + q^5 + 2q^6 - 2q^7 + \cdots.
$$

\begin{exercise}
  Compute the dimension of $S_2(\Gamma_0(25))$.
\end{exercise}


\section{Modularity of Elliptic Curves}
Let $E$ be an elliptic curve defined
by a Weierstrass equation $y^2 = x^3 + ax + b$
with $a,b\in\Q$.
For each prime $p\nmid \Delta = -16(4a^3 + 27b^2)$,
set
$$
  a_p  = p+1 - \#E(\Z/p\Z).
$$

\begin{definition}[Modular]
  $E$ is {\em modular} if there
  exists a cuspidal modular form
  $$
    f(z) = \sum_{n=1}^{\infty} b_n q^n\in S_2(\Gamma_0(\Delta))
  $$
  such that
  $b_p = a_p$ for all $p\nmid \Delta$.
\end{definition}

At first glance, modularity appears to be a bizarre and unlikely
property for an elliptic curve to have.  When poor Taniyama (and
Shimura) first suggested in 1955 that every elliptic curve is modular,
people were dubious.  But Taniyama was right.  The proof of that conjecture
is one of the crowning achievements of number theory.

\begin{theorem}[Breuil, Conrad, Diamond, Taylor, Wiles]
  \mbox{}\vspace{-1.5em}\\
  \begin{center}
    {\sc Every elliptic curve over~$\Q$ is modular.}
  \end{center}

\end{theorem}

\vfill
%\noindent{}What is next?
%\vspace{3em}

\noindent{}\includegraphics[width=1in]{wiles.eps}\\
\mbox{ }\hspace{1.5em}{\small Wiles}








\chapter{The Birch and Swinnerton-Dyer Conjecture, Part 1}



The next three lectures will be about the
Birch and Swinnerton-Dyer conjecture, which is
considered by many people to be the most important
accessible open problem in number theory.  Today I
will guide you through Wiles's Clay Math Institute
paper on the Birch and Swinnerton-Dyer conjecture.

On Friday, I will talk about the following open problem, which is a
frustrating specific case of the Birch and Swinnerton-Dyer conjecture.
Let~$E$ be the elliptic curve defined by
$$
  y^2 + xy = x^3 - x^2 -79x + 289.
$$
Denote by
$L(E,s)=\sum_{n=1}^{\infty} a_n n^{-s}$
the corresponding $L$-series, which extends to a function
everywhere.  The graph of $L(E,s)$ for $s\in (0,5)$ is
given on the next page.
It can be proved that
$E(\Q) \ncisom \Z^4$
by showing that
$$
  (8,7),\ \left(\frac{120}{27},\frac{29}{27}\right),\,
  \left(\frac{70}{8},\frac{81}{8}\right),\,
  \text{ and }\left(\frac{564}{8}, \frac{665}{64}\right)
$$
generate a ``subgroup of finite index'' in $E(\Q)$.
The Birch and Swinnerton-Dyer Conjecture then predicts that
$$\ord_{s=1}L(E,s)=4,$$
which looks plausible from the shape of the graph on the next page.
It is relatively easy to prove that
the following is equivalent to showing that
$\ord_{s=1}L(E,s)=4$:

\hd{Open Problem:}
{\sl Prove that $L''(E,1) = 0$.}\vspace{.7em}

If you could solve this open problem, people like Gross, Tate, Mazur,
Zagier, Wiles, me, etc., would be {\bf very} excited.  The related
problem of giving an example of an $L$-series with
$\ord_{s=1}L(E,s)=3$, was solved as a consequence of a very deep
theorem of Gross and Zagier, and resulting in an effective solution to
Gauss's class number problem.

John Tate gave a talk about the BSD conjecture for the Clay Math
Institute.  I strongly encourage you to watch it online at\vspace{0.5em}

\hspace{-3.5em}
\begin{minipage}{1.1\textwidth}
  \begin{verbatim}
http://www.msri.org/publications/ln/hosted/cmi/2000/cmiparis/index-tate.html
\end{verbatim}
\end{minipage}



\newpage
\mbox{} \vfill
\begin{center}
  \psset{unit=.9in}
  \pspicture(-0.5,-2)(5,2)
  \psgrid[gridcolor=lightgray]
  \psline[linewidth=0.03]{->}(-0.5,0)(5,0)\rput(5.2,0){$x$}
  \psline[linewidth=0.03]{->}(0,-2)(0,2)\rput(0.1,2.25){$y$}
  \pscircle*[linecolor=red](1,0){0.06}

  \pscurve[linecolor=blue]
  (0,0)
  (0.009999999999999999999999999999,20.17460740732031218029067105)
  (0.01999999999999999999999999999,36.49248092043218759414227962)
  (0.02999999999999999999999999999,49.48695269194186255570395989)
  (0.03999999999999999999999999999,59.62809499064306722407223772)
  (0.04999999999999999999999999999,67.32949817701725860474850393)
  (0.05999999999999999999999999999,72.95437998695321784902246547)
  (0.06999999999999999999999999999,76.82108703674534698230608976)
  (0.07999999999999999999999999999,79.20804440435897043647662713)
  (0.08999999999999999999999999999,80.35820445115398146915461672)
  (0.09999999999999999999999999999,80.48304170441465810207415387)
  (0.1099999999999999999999999999,79.76613660406133174625793745)
  (0.1199999999999999999999999999,78.36638720713565471334218075)
  (0.1299999999999999999999999999,76.42088452189766469084520681)
  (0.1399999999999999999999999999,74.04748399111477869470341911)
  (0.1499999999999999999999999999,71.34710274352131875238762686)
  (0.1599999999999999999999999999,68.40576956639155116679637679)
  (0.1699999999999999999999999999,65.29645210439220133457469776)
  (0.1799999999999999999999999999,62.08068354485849675320142694)
  (0.1899999999999999999999999999,58.81000899267982264641223734)
  (0.1999999999999999999999999999,55.52726985520684582203751378)
  (0.2099999999999999999999999999,52.26774283592287994132381501)
  (0.2199999999999999999999999999,49.06014856276556567828076763)
  (0.2299999999999999999999999999,45.92754344141284383534396879)
  (0.2399999999999999999999999999,42.88810701477216095300037175)
  (0.2500000000000000000000000000,39.95583591725886436605311523)
  (0.2599999999999999999999999999,37.14115442682657456540803882)
  (0.2699999999999999999999999999,34.45145063037828623093305962)
  (0.2799999999999999999999999999,31.89154632102089971062111326)
  (0.2899999999999999999999999999,29.46410793109383578525135981)
  (0.2999999999999999999999999999,27.17000506602518061597947147)
  (0.3099999999999999999999999999,25.00862253439015019890448691)
  (0.3199999999999999999999999999,22.97813116310177967557620869)
  (0.3299999999999999999999999999,21.07572213794885965683462997)
  (0.3399999999999999999999999999,19.29780911363895258131960784)
  (0.3499999999999999999999999999,17.64020188943555017633327563)
  (0.3599999999999999999999999999,16.09825504210457925139305818)
  (0.3699999999999999999999999999,14.66699454326279499576807542)
  (0.3799999999999999999999999999,13.34122505973041656327517788)
  (0.3899999999999999999999999999,12.11562033981553963057313496)
  (0.3999999999999999999999999999,10.98479882256070828538534132)
  (0.4099999999999999999999999999,9.943386368083383947466728763)
  (0.4199999999999999999999999999,8.986067792701650934369969225)
  (0.4299999999999999999999999999,8.107628700234563525818535593)
  (0.4399999999999999999999999999,7.302988928586518979021916724)
  (0.4499999999999999999999999999,6.567228776537510403916594514)
  (0.4599999999999999999999999999,5.895609037808749758128609910)
  (0.4699999999999999999999999999,5.283585746356747623589733925)
  (0.4799999999999999999999999999,4.726820427014403603207828295)
  (0.4899999999999999999999999999,4.221186547723964451899689776)
  (0.5000000000000000000000000000,3.762772782494687754526881867)
  (0.5099999999999999999999999999,3.347883616780079475458459212)
  (0.5199999999999999999999999999,2.973037758219996513199228676)
  (0.5299999999999999999999999999,2.634964754739207607589602182)
  (0.5399999999999999999999999999,2.330600168028682449712596851)
  (0.5499999999999999999999999999,2.057079602728938881782014276)
  (0.5599999999999999999999999999,1.811731849526851974888215643)
  (0.5699999999999999999999999999,1.592071363273333449258930595)
  (0.5799999999999999999999999999,1.395790264592613793193107804)
  (0.5899999999999999999999999999,1.220750024801029588629612509)
  (0.5999999999999999999999999999,1.064972968849014570738009102)
  (0.6099999999999999999999999999,0.9266337090529745829497283098)
  (0.6199999999999999999999999999,0.8040506032421436529502188197)
  (0.6299999999999999999999999999,0.6956773142935737278953165664)
  (0.6399999999999999999999999999,0.6000945335828484520729664871)
  (0.6499999999999999999999999999,0.5160019183851058048608615062)
  (0.6599999999999999999999999999,0.4422102824932734723852967661)
  (0.6699999999999999999999999999,0.3776340700748442896394133312)
  (0.6799999999999999999999999999,0.3212841348834891250179487532)
  (0.6899999999999999999999999999,0.2722608402152899327179309314)
  (0.6999999999999999999999999999,0.2297474893069316371408115573)
  (0.7099999999999999999999999999,0.1930040910861989698630156271)
  (0.7199999999999999999999999999,0.1613614621891930253258516617)
  (0.7299999999999999999999999999,0.1342156628522034383917738953)
  (0.7399999999999999999999999999,0.1110227615790083457114122966)
  (0.7500000000000000000000000000,0.09129392129667414518098480549)
  (0.7599999999999999999999999999,0.07459079797405312211187159854)
  (0.7699999999999999999999999999,0.06052124132473794610248824865)
  (0.7799999999999999999999999999,0.04873528619520886474474134312)
  (0.7899999999999999999999999999,0.03892142250086749266271155850)
  (0.7999999999999999999999999999,0.03080313107503010887527356618)
  (0.8099999999999999999999999999,0.02413567250142808775689375590)
  (0.8199999999999999999999999999,0.01870311587666825308627117243)
  (0.8299999999999999999999999999,0.01431559446691944601678069902)
  (0.8399999999999999999999999999,0.01080677535797030118779995171)
  (0.8499999999999999999999999999,0.008031530428166537313088826674)
  (0.8599999999999999999999999999,0.005863796280892651616045409318)
  (0.8699999999999999999999999999,0.004194611141076388195622582279)
  (0.8799999999999999999999999999,0.002930317134784240786576375213)
  (0.8899999999999999999999999999,0.001990916820450637832948807979)
  (0.8999999999999999999999999999,0.001308573314500336314393575834)
  (0.9099999999999999999999999999,0.0008262438444779515009749122133)
  (0.9199999999999999999999999999,0.0004964370620328922663686765112)
  (0.9299999999999999999999999999,0.0002800849501432968528544120962)
  (0.9399999999999999999999999999,0.0001455206587485309187589983425)
  (0.9499999999999999999999999999,0.00006755409634153535113332324454)
  (0.9599999999999999999999999999,0.00002663758867231954273129854073)
  (0.9699999999999999999999999999,0.000008114386827604695137558360759)
  (0.9799999999999999999999999999,0.000001543263454951985960816259945)
  (0.9899999999999999999999999999,0.00000009287616753530200698777679283)
  (1.000000000000000000000000000,0)
  (1.009999999999999999999999999,0.00000008613535242271793720817881641)
  (1.019999999999999999999999999,0.000001327383632453574056960242273)
  (1.029999999999999999999999999,0.000006472849519208711184577737210)
  (1.039999999999999999999999999,0.00001970717637751214949546526075)
  (1.049999999999999999999999999,0.00004635314997888051879883070784)
  (1.059999999999999999999999999,0.00009261061561523324548962036876)
  (1.069999999999999999999999999,0.0001653282453215230156051138830)
  (1.079999999999999999999999999,0.0002718049657487095079831033217)
  (1.089999999999999999999999999,0.0004196181138178058712824946368)
  (1.099999999999999999999999999,0.0006164756272648974614962677827)
  (1.099999999999999999999999999,0.0006164756272648974614962677827)
  (1.199999999999999999999999999,0.006870279746547869692524606156)
  (1.299999999999999999999999999,0.02451425929226668848552443352)
  (1.399999999999999999999999999,0.05530486595233524676133790522)
  (1.500000000000000000000000000,0.09765544070412340678441505311)
  (1.599999999999999999999999999,0.1484133662890015288766060733)
  (1.699999999999999999999999999,0.2041828834745631596498302481)
  (1.799999999999999999999999999,0.2620135130277175192931976650)
  (1.899999999999999999999999999,0.3196398583984175776395640284)
  (2.000000000000000000000000000,0.3754772776917356690282702906)
  (2.142857142857142857142857142,0.4502313491226790796900981145)
  (2.285714285714285714285714285,0.5178521605298004955131946576)
  (2.428571428571428571428571428,0.5779802204499079942278058454)
  (2.571428571428571428571428571,0.6308850124153752579541337641)
  (2.714285714285714285714285714,0.6771425457165369193256163032)
  (2.857142857142857142857142857,0.7174458390192943996234368404)
  (3.000000000000000000000000000,0.7525010360986485727517631927)
  (3.142857142857142857142857142,0.7829749879942698159458508626)
  (3.285714285714285714285714285,0.8094722550337551220911958042)
  (3.428571428571428571428571428,0.8325281629388328047823509839)
  (3.571428571428571428571428571,0.8526101345199758064458287717)
  (3.714285714285714285714285714,0.8701229137173289116087300433)
  (3.857142857142857142857142857,0.8854152900121340605812737059)
  (4.000000000000000000000000000,0.8987870701281932147462079378)
  (4.142857142857142857142857142,0.9104956815780882370859628481)
  (4.285714285714285714285714285,0.9207621415644603190499847130)
  (4.428571428571428571428571428,0.9297763100393346042082318535)
  (4.571428571428571428571428571,0.9377014395533425632455706355)
  (4.714285714285714285714285714,0.9446780780755271456938317299)
  (4.857142857142857142857142857,0.9508273974961405102958036830)
  (5.000000000000000000000000000,0.9562540230526573686678401977)
  \endpspicture
  \vspace{1.5em}

  The $L$-series of the ``simplest'' known elliptic curve of rank~$4$.
\end{center}






\chapter{The Birch and Swinnerton-Dyer Conjecture, Part 2}



\section{The BSD Conjecture}
Let $E$ be an elliptic curve over~$\Q$ given by an equation
$$
  y^2 = x^3 + ax + b
$$
with $a,b\in\Z$.  For $p\nmid \Delta = -16(4a^3 + 27b^2)$,
let
$a_p = p+1 - \# E(\Z/p\Z)$.
Let
$$
  L(E,s) = \prod_{p\nmid\Delta} \frac{1}{1-a_p p^{-s} + p^{1-2s}}.
$$

\begin{theorem}[Breuil, Conrad, Diamond, Taylor, Wiles]\mbox{}\\
  \mbox{}\hspace{5em}$L(E,s)$ extends to an analytic function on all of $\C$.
\end{theorem}

\begin{conjecture}[Birch and Swinnerton-Dyer]
  The Taylor expansion of $L(E,s)$ at $s=1$ has the form
  $$
    L(E,s) = c(s-1)^r + \text{\rm  higher order terms}
  $$
  with $c\neq 0$ and $E(\Q)\ncisom \Z^r \cross E(\Q)_{\tor}$.
\end{conjecture}

A special case of the conjecture is the assertion that $L(E,1)=0$ if
and only if $E(\Q)$ is infinite.  The assertion ``$L(E,1)=0$ implies
that $E(\Q)$ is infinite'' is the part of the conjecture that secretely
motives much of my own research.

\section{What is Known}
On page 5 of Wiles's paper, he discusses the history
of the following theorem.
\begin{theorem}[Gross,  Kolyvagin, Zagier, et al.]
  Suppose that
  $$
    L(E,s) = c(s-1)^r + \text{\rm  higher order terms}
  $$
  with $r\leq 1$.  Then the Birch and Swinnerton-Dyer conjecture
  is true for~$E$, that is,
  $E(\Q)\ncisom \Z^r\oplus E(\Q)_{\tor}.$
\end{theorem}

I suspect that most elliptic curves satisfy the hypothesis of the
above theorem, i.e., they have rank $0$ or $1$.  For example, almost
96\% of the ``first $78198$'' elliptic curves have $r\leq 1$.  I
suspect that the curves with $r>1$ have ``density'' $0$ amongst all
elliptic curves.  This doesn't mean that we are done.  In practice it
is often the curves with $r>1$ that are interesting and useful, and
experts can still be observed saying ``almost nothing is known about the
Birch and Swinnerton-Dyer conjecture''.

%The Birch and Swinnerton-Dyer conjecture has inspired the creation of
%a vast web of conjectures by the likes of Beilinson, Deligne, Mazur,
%Tate, Bloch, Kato, Perrin-Riou, and others.

\section{How to Compute $L(E,s)$ with a Computer}
\subsection{Best Models}
Let~$E$ be an elliptic curve over~$\Q$, defined by
a Weierstrass equation
$$
  y^2 + a_1 xy + a_3 y = x^3 + a_2 x^2 + a_4 x + a_6.
$$
There are many choices of Weierstrass equations that
define an elliptic curve that is ``essentially the same''
as~$E$.  E.g., you found others by completing the square.
Among all of these, there is a best possible model, which
is the one with smallest discriminant.  It can be computed
in PARI as follows:
\begin{verbatim}
? E = ellinit([0,0,0,-43,166]);
? E.disc
%61 = -6815744
? E = ellchangecurve(E,ellglobalred(E)[2])
%62 = [1, -1, 1, -3, 3, ...]
? E.disc
%63 = -1664
\end{verbatim}
Thus $y^2 + xy +y = x^3 -x^2 -3x +3$ is a ``better''
model than $y^2 = x^3 - 43x + 166$.

\hd{WARNING:} Some of the elliptic curves functions in PARI will {\em
    LIE} if you give as input an elliptic curve that is defined by a model
that isn't the best possible.  These devious liars include
  {\tt elltors, ellap, ellak}, and {\tt elllseries}.

\subsection{Formula for $L(E,s)$}
As mentioned before, the PARI function {\tt elllseries}
can compute $L(E,s)$.   I figured out how this function works, and
explain it below.

Because~$E$ is modular, one can show that we have the following
rapidly-converging series expression for $L(E,s)$, for $s>0$:
$$
  L(E,s) = N^{-s/2}\cdot (2\pi)^s\cdot  \Gamma(s)^{-1}\cdot
  \sum_{n=1}^{\infty} a_n \cdot \left(F_n(s-1) - \eps F_n(1-s)\right)
$$
where
$$
  F_n(t) =
  \Gamma\left(t+1, \frac{2\pi n}{\sqrt{N}}\right)
  \cdot \left(\frac{\sqrt{N}}{2\pi n}\right)^{t+1}.
$$
Here
$$
  \Gamma(z) = \int_{0}^{\infty} t^{z-1} e^{-t} dt
$$
is the {\em $\Gamma$-function} (e.g., $\Gamma(n) = (n-1)!$),
and
$$
  \Gamma(z,\alpha) = \int_{\alpha}^{\infty} t^{z-1} e^{-t}dt
$$
is the {\em incomplete $\Gamma$-function}.
The number~$N$ is called the {\em conductor} of~$E$ and is
very similar to the discriminant of~$E$; it is only divisible
by primes that divide the best possible discriminant of~$E$.
You can compute~$N$ using the PARI command {\tt ellglobalred(E)[1]}.

As usual, for $p\nmid \Delta$, we have
$$
  a_p = p+1 - \# E(\Z/p\Z),
$$
and for $r\geq 2$,
$$
  a_{p^r} = a_{p^{r-1}}a_p - p a_{p^{r-2}},
$$
and
$a_{nm} = a_n a_m$ if $\gcd(n,m)=1$
(I won't define the $a_p$ when $p\mid \Delta$, but it's
not difficult.)
Finally, $\eps$ depends only on~$E$ and is either $+1$ or $-1$.
I won't define $\eps$ either, but you can compute it in PARI
using {\tt ellrootno(E)}.

At $s=1$, the formula can be massively simplified, and we have
$$
  L(E,1) = (1+\eps) \cdot \sum_{n=1}^{\infty}
  \frac{a_n}{n} e^{-2\pi n/\sqrt{N}}.
$$
This sum converges rapidly, because
$e^{-2\pi n/\sqrt{N}}\ra 0$ quickly as $n\ra \infty$.


Monday's lecture will be filled with numerical examples and numerical
evidence for the Birch and Swinnerton-Dyer conjecture.   Wednesday's
lecture will be a review for the take-home {\bf FINAL EXAM}.






\chapter{The Birch and Swinnerton-Dyer Conjecture, Part 3}




\section{A Rationality Theorem}
In the last lecture, I mentioned that it can be surprisingly difficult to
say anything precise about $L(E,s)$, even with the above formulas.
For example, it is a very deep theorem of Gross and Zagier that for
the elliptic curve $y^2 + y = x^3 - 7x + 6$ we have
$$
  L(E,s) = c(s-1)^3 + \text{ higher order terms},
$$
and nobody has any idea how to prove that there is an elliptic curve
with
$$
  L(E,s) = c(s-1)^4 + \text{ higher order terms}.
$$

Fortunately, it is possible to decide whether or not $L(E,1)=0$.
\begin{theorem}
  Let $y^2 = x^3 + ax + b$ be an elliptic curve.
  Let
  $$
    \Omega_E = 2^n\int_{\gamma}^{\infty} \frac{dx}{\sqrt{x^3+ax+b}},
  $$
  where $\gamma$ is the largest real root of $x^3 +ax+b$, and
  $n=0$ if $\Delta(E)<0$, $n=1$ if $\Delta(E)>0$.
  Then
  $$
    \frac{L(E,1)}{\Omega_E} \in \Q,
  $$
  and the denominator is $\leq 24$.
\end{theorem}
In practice, one computes $\Omega_E$ using
the ``Arithmetic-Geometric Mean'', {\em NOT} numerical
integration.  In PARI, $\Omega_E$
is approximated by {\tt E.omega[1]*2\^{}(E.disc>0)}.

\begin{remark}
  I don't know if the denominator is ever really as big as~$24$.
  It would be a fun student project to either find an example,
  or to understand the proof that the quotient is rational and
  prove that $24$ can be replaced by something smaller.
\end{remark}


\begin{example}
  Let $E$ be the elliptic curve $y^2 = x^3 - 43x + 166$.
  We compute $L(E,1)$ using the above formula and observe
  that $L(E,1)/\Omega_E$ appears to be a rational number, as predicted by
  the theorem.
  \begin{verbatim}
? E = ellinit([0,0,0,-43,166]);
? E = ellchangecurve(E, ellglobalred(E)[2]);
? eps = ellrootno(E)
%77 = 1
? N = ellglobalred(E)[1]
%78 = 26
? L = (1+eps) * sum(n=1,100, ellak(E,n)/n * exp(-2*Pi*n/sqrt(N)))
%79 = 0.6209653495490554663758626727
? Om = E.omega[1]*2^(E.disc>0)
%80 = 4.346757446843388264631038710
? L/Om
%81 = 0.****************************
? contfrac(L/Om)
%84 = [0, 7]
? 1/7.0
%85 = 0.****************************
? elltors(E)
%86 = [7, [7], [[1, 0]]]
\end{verbatim}
  Notice that in this example, $L(E,1)/\Omega_E = 1/7 = 1/\#E(\Q)$.
  This is shadow of a more refined conjecture of Birch and Swinnerton-Dyer.
\end{example}

\begin{example}
  In this example, we verify that $L(E,1)=0$ computationally.
  \begin{verbatim}
? E=ellinit([0, 1, 1, -2, 0]);
? L1 = elllseries(E,1)
%4 = -6.881235151133426545894712438 E-29
? Omega = E.omega[1]*2^(E.disc>0)
%5 = 4.980425121710110150642715583
? L1/Omega
%6 = 1.795732353252503036074927634 E-20
\end{verbatim}
\end{example}

\section{Approximating the Rank}
Fix an elliptic curve $E$ over~$\Q$.

The usual method to {\em approximate} the rank is to find a series
that rapidly converges to $L^{(r)}(E,1)$ for $r=0,1,2,3,\ldots$,
then compute $L(E,1)$, $L'(E,1)$, $L^{(2)}(E,1)$, etc., until
one appears to be nonzero.   You can read about this method in \S2.13
of Cremona's book {\em Algorithms for Elliptic Curves}.   For variety,
I will describe a slightly different method that I've played with
recently, which uses the formula for $L(E,s)$ from the last lecture,
the definition of the derivative, and a little calculus.

\begin{proposition}
  Suppose that
  $$
    L(E,s) = c(s-1)^r + \text{ higher terms}.
  $$
  Then
  $$
    \lim_{s\ra 1}\,
    (s-1)\cdot \frac{L'(E,s)}{L(E,s)} = r.
  $$
\end{proposition}
\begin{proof}
  Write
  $$
    L(s) = L(E,s) = c_r(s-1)^r + c_{r+1}(s-1)^{r+1} + \cdots.
  $$
  Then
  \begin{align*}
    \lim_{s\ra 1} \,
    (s-1)\cdot \frac{L'(s)}{L(s)}
     & =  \lim_{s\ra 1} \,
    (s-1)\cdot \frac{r c_r (s-1)^{r-1} + (r+1) c_{r+1}(s-1)^r + \cdots}
    {c_r(s-1)^r + c_{r+1}(s-1)^{r+1} + \cdots} \\
     & =  r\cdot \lim_{s\ra 1} \,
    \frac{c_r (s-1)^{r} + \frac{(r+1)}{r} c_{r+1}(s-1)^{r+1} + \cdots}
    {c_r(s-1)^r + c_{r+1}(s-1)^{r+1} + \cdots} \\
     & = r.
  \end{align*}
\end{proof}

Thus the rank~$r$ is ``just''
the limit as $s\ra 1$ of a certain (smooth) function.  We know this
limit is an integer.  But, for example, for the curve
$$
  y^2 +xy = x^3 - x^2 - 79x + 289
$$
nobody has succeeded in proving that this integer limit is $4$.  (One
can prove that the limit is either $2$ or $4$.)

Using the definition of derivative, we {\em heuristically} approximate
$(s-1)\frac{L'(s)}{L(s)}$ as follows.  For $|s-1|$ small, we have
\begin{align*}
  (s-1)\frac{L'(s)}{L(s)} & =
  \frac{s-1}{L(s)}\cdot \lim_{h\ra 0}  \frac{L(s+h) - L(s)}{h}      \\
                          & \approx \frac{s-1}{L(s)}\cdot
  \frac{L(s+(s-1)^2) - L(s)}{(s-1)^2}                               \\
                          & = \frac{L(s^2 - s+1) - L(s)}{(s-1)L(s)}
\end{align*}

\begin{question}
  Does
  $$
    \lim_{s\ra 1}\, (s-1)\cdot \frac{L'(s)}{L(s)}
    = \lim_{s\ra 1} \frac{L(s^2 - s+1) - L(s)}{(s-1)L(s)}?
  $$
\end{question}

\noindent In any case, we can use this formula in PARI to ``approximate''~$r$.
\begin{verbatim}
? E = ellinit([ 0, 1, 1, -2, 0 ]);
? r(E,s) = L1=elllseries(E,s); L2=elllseries(E,s^2-s+1); (L2-L1)/((s-1)*L1);
? r(E,1.01)
%8 = 2.004135342473941928617680057
? r(E,1.001)
%9 = 2.000431337547225544819319104
\\ One can prove that 2 is the correct limit.
\end{verbatim}

Now let's try the mysterious curve $y^2 +xy = x^3 - x^2 - 79x + 289$
of rank~$4$:
\begin{verbatim}
? E=ellinit([ 1,-1,0,-79,289]);
? r(E,1.001)         \\ takes 6 seconds on PIII 1Ghz
%1 = 4.002222374519085610896440642
? r(E,1.00001) 
%2 = 4.000016181256911064613006133
\end{verbatim}
It certainly looks like
$\lim_{s\ra 1} r(s) = 4$.
We know for a fact that
$\lim_{s\ra 1} r(s)\in\Z$,
and if only there were a good way to bound the
error we could conclude that
the limit is~$4$.   But this has stumped people for
years, and maybe it is impossible without a very deep
result that somehow interprets this limit in a different
way.  This problem has totally stumped the experts for years.
We desperately need a new idea!!

If one of you wants to do a reading or research project on this
problem in the next year or two, let me know.  One could draw
pictures of $L^{(3)}(E,s)$ or investigate the analogous problem
for other more accessible $L$-series.





\begin{verbatim}
? E=ellinit([0,0,1,-7,6]);
? r(E,s) = L1=elllseries(E,s); L2=elllseries(E,s^2-s+1); (L2-L1)/((s-1)*L1);
? r(E,1.001)
%2 = 3.001144104985619206504448552
\end{verbatim}




\chapter{Homework}
\section{Primes and the Euclidean Algorithm}

\begin{enumerate}
  \item Let~$p$ be a prime number and~$r$ and integer such that
        $1\leq r < p$. Prove that~$p$ divides the binomial
        coefficient
        $$
          \frac{p!}{r!(p-r)!}.
        $$
        You may not assume that this coefficient is a integer.

  \item Compute the following gcd's using a pencil and
        the Euclidean algorithm:
        $$
          \gcd(15,35),\quad
          \gcd(247,299),\quad
          \gcd(51,897), \quad
          \gcd(136,304)
        $$

  \item Using mathematical induction to prove that
        $$1+2+3 + \cdots + n = \frac{n(n+1)}{2},$$
        then find a formula for
        $$1 - 2 + 3 - 4 + \cdots \pm n = \sum_{a=1}^{n}(-1)^{a-1} a.$$

  \item What was the most recent prime year?
        I.e., which of $2001, 2000, \ldots$ was it?

  \item Use the Euclidean algorithm to find integers $x,\, y\in\Z$
        such that
        $$2261x + 1275y = 17.$$
        [I did not tell you how to do this; see \S1.8 of Davenport's book.]

  \item Factor the year that you should graduate from Harvard
        as a product of primes.  E.g., frosh answer $2005=5\times 401$.

        %\item Using the fact that $9262001$ is divisible by exactly two
        %primes, factor it by hand.

  \item
        \vspace{-4ex}Write a PARI program to print ``Hello Kitty'' five times.
        \hfill \includegraphics[width=7em]{hkcomp.eps}

  \item Let $f(x)\in\Z[x]$ be a polynomial with integer coefficients.
        Formulate a conjecture about when the set
        $\{ f(a) : a \in \Z \text{ and $f(a)$ is prime }\}$
        is infinite.  Give computational evidence for your conjecture.

  \item Is it easy or hard for PARI to compute the gcd
        of two random 2000-digit numbers?

  \item Prove that there are infinitely many primes of the form $6x-1$.

  \item \begin{enumerate}
          \item Use PARI to compute
                $$\pi(2001) = \#\{ \text{ primes } p \leq 2001\}.$$

          \item The prime number theorem predicts that $\pi(x)$ is
                asymptotic to $x/\log(x)$.  How close is $\pi(2001)$ to
                $2001/\log(2001)$?
        \end{enumerate}

\end{enumerate}

\section{Congruences}
\begin{enumerate}
  \item Find complete sets of residues modulo $7$, all of whose elements
        are (a) nonnegative, (b) odd, (c) even, (d) prime.

  \item Find an integer $x$ such that $37x \con 1\pmod{101}$.

  \item What is the order of $5$ modulo $37$?

  \item Let $n=\vphi(7!)$.  Compute the prime factorization of~$n$.

  \item Find $x, y\in\Z$ such that
        $$
          6613x + 8947y = 389.
        $$

  \item Find an $x\in\Z$ such that
        $x \con -4 \pmod{17}$ and $x\con 3\pmod{23}$.

  \item Compute
        $7^{100}\pmod{389}$.

  \item Find a number $a$ such that $0\leq a < 111$ and
        $$
          (102^{70}+1)^{35} \con a\pmod{111}.
        $$
        (See Problem 2.05 on page 217 of Davenport.)


  \item Prove that if $n>4$ is composite then
        $$
          (n-1)! \con 0 \pmod{n}.
        $$

  \item For what values of~$n$ is $\vphi(n)$ odd?

  \item Find your own $100$-digit number~$n$ such that
        $
          a^{n-1}\con 1\pmod{n}
        $
        for $a=2,3,5$.

  \item Seven thieves try to share a hoard of gold bars equally between
        themselves.  Unfortunately, six bars are left over, and in the fight
        over them, one thief is killed.  The remaining six thieves, still
        unable to share the bars equally since two are left over, again fight,
        and another is killed.  When the remaining five share the bars, one
        bar is left over, and it is only after yet another thief is killed
        that an equal sharing is possible.  What is the minimum number of bars
        which allows this to happen?

  \item
        An elderly woman goes to a market where a horse tramples her basket
        crushing her eggs. The horse's honest rider offers to pay for the
        damages and asks her how many eggs she had brought. She doesn't
        remember the exact number, but recalls that when she had taken them
        out two at a time, there was one egg left. The same happened when she
        picked them out three, four, five, and six at a time, but when she
        took them out seven at a time two were left. What is the smallest
        number of eggs she could have had?

\end{enumerate}


\section{Public-Key Cryptography}
\begin{enumerate}
  \item (3 points) You and Nikita wish to agree on a secret key using
        the Diffie-Hellman protocol.  Nikita announces that $p=3793$ and
        $g=7$.  Nikita secretely chooses a number~$n<p$ and tells you
        that $g^n\con 454\pmod{p}$.  You choose the random number
        $m=1208$.  Tell me what the secret key is!

  \item (4 points) This problem concerns encoding phrases using numbers.
        \begin{enumerate}
          \item Find the number that corresponds to
                \verb*|VE RI TAS|, where we view this string as a number in
                base~$27$ using the encoding of Section~2 of Lecture~9.
                (Note that the left-most ``digit'', \verb*|V|, is the
                least significant digit, and \verb*| | denotes a blank space.)
          \item What is the longest sequence of letters (and space) that
                can be stored using a number that is less than $10^{20}$?
        \end{enumerate}

  \item (4 points) You see Michael and
        Nikita agree on a secret key using the Diffie-Hellman key exchange
        protocol.  Michael and Nikita choose $p=97$ and $g=5$.  Nikita chooses
        a random number~$n$ and tells Michael that $g^n\con 3\pmod{97}$, and
        Michael chooses a random number~$m$ and tells Nikita that $g^m\con
          7\pmod{97}$.  Crack their code: What is the secret key that Nikita and
        Michael agree upon?  What is~$n$?  What is~$m$?

  \item (2 points)
        Using the RSA public key is $(n,e) = (441484567519, 238402465195)$,
        encrypt the year that you will graduate from Harvard.

  \item (6 points) In this problem, you will ``crack'' an RSA cryptosystem.
        \begin{enumerate}
          \item What is the secret decoding number~$d$ for the RSA
                cryptosystem with public key $(n,e) = (5352381469067, 4240501142039)$?
          \item The number $3539014000459$ encrypts an important question using
                the RSA cryptosystem from part (a).  What is the question?  (After decoding,
                you'll get a number.  To find the corresponding word, see Section~2 of
                Lecture~9.)
        \end{enumerate}

        \newpage
  \item (4 points)
        Suppose Michael creates an RSA cryptosystem with a very large
        modulus~$N$ for which the factorization of~$N$ cannot be found
        in a reasonable amount of time. Suppose that Nikita sends
        messages to Michael by representing each alphabetic character
        as an integer between~$0$ and~$26$ (\verb*|A| corresponds
        to $1$, \verb*|B| to~$2$, etc., and a space \verb*| | to~$0$),
        then encrypts each number {\em separately}
        using Michael's RSA cryptosystem.  Is this method secure?
        Explain your answer.

  \item (6 points)
        Nikita creates an RSA cryptosystem with public key
        $$(n,e)=(1433811615146881, 329222149569169).$$
        In the following two problems, show the steps you take.
        Don't simply factor~$n$ directly using the {\tt factor}
        function in PARI.
        \begin{enumerate}
          \item
                Somehow you discover that $d=116439879930113$.  Show how to use the
                probabilistic algorithm of Lecture~10 to use~$d$ to factor~$n$.
          \item
                In part (a) you found that the factors~$p$ and~$q$ of~$n$ are very close.
                Show how to use
                the ``Fermat Factorization'' method of Lecture~10 to factor~$n$.
        \end{enumerate}

\end{enumerate}

\section{Primitive Roots and Quadratic Reciprocity}

\begin{problems}
  %\comment{\item 
  %(1 point) Why do {\em you} think that quadratic reciprocity is
  %so cool?
  %\ans{An example answer: ``I think it is amazing that
  %$5^\frac{p-1}{2}\pmod{p}$ depends only on the residue class of~$p$
  %modulo~$5$, because it's not easy for me to see this directly.  QR is at
  %first somewhat counterintuitive, but easy to believe after doing
  %experiments.''}
  %}


  \item (2 points) Calculate the following symbols by hand: $\kr{3}{97}$, $\kr{5}{389}$,
  $\kr{2003}{11}$, and $\kr{5!}{7}$.

  \item (3 points) Prove that
  $\ds
    \kr{3}{p} =
    \begin{cases} \hfill 1 & \text{ if }p\con 1, 11\pmod{12}, \\
              -1       & \text{ if }p\con 5, 7\pmod{12}.
    \end{cases}
  $
  \item (3 points) Prove that there is no primitive root
  modulo~$2^n$ for any $n\geq 3$.
  %\ans{If there were a primitive root modulo $2^n$, then there would
  %be one modulo $2^3$, but there isn't as a direct check shows.}

  \item (6 points) Prove that if~$p$ is a prime, then
  there is a primitive root modulo~$p^2$.

  \item (5 points)\label{ex:direct}
  Use the fact that $(\Z/p\Z)^*$ is cyclic to give a direct proof
  that $\kr{-3}{p}=1$ when $p\con 1\pmod{3}$. [Hint: There is an $c\in (\Z/p\Z)^*$ of
      order~$3$.  Show that $(2c+1)^2=-3$.]

  \item (6 points)
  If $p\con 1\pmod{5}$, show directly that $\kr{5}{p}=1$ by the method of
  Exercise~\ref{ex:direct}. [Hint: Let $c\in(\Z/p\Z)^*$ be an element of
  order~$5$.  Show that $(c+c^4)^2+(c+c^4)-1=0$, etc.]

  \item (4 points) For which primes~$p$ is
  $\ds \sum_{a=1}^{p-1} \kr{a}{p}=0$?

  \item (4 points) Artin conjectured that the number of primes $p\leq x$
  such that~$2$ is a primitive root
  modulo~$p$ is asymptotic to $C\pi(x)$ where $\pi(x)$ is the number of
  primes $\leq x$ and~$C$ is a fixed constant called Artin's constant.
  Using a computer, make an educated guess as to what~$C$ should be, to
  a few decimal places of accuracy.  Explain your reasoning.  (Note:
  Don't try to prove that your guess is correct.)
  %\ans{\tt ispr(n, p) =
  %if(n\%p == 0, 0, znorder(Mod(n,p))==p-1);\\ A(n, d, gen=0,
  %prim=0)=forprime(p=2,d,prim++;if(ispr(n,p),gen++));gen/prim\\ ?
  %A(2,500000)*1.0\\ \%37 = 0.3738263758486205402282247580\\ ?
  %x=1;forprime(p=2,100000,x=x*(1.0-1/(p*(p-1))));x*1.0
  %$\backslash\backslash$ for fun, notice\\ \%38 =
  %0.3739561136265594983326614081 }

\end{problems}


\section{Continued Fractions}
\begin{problems}

  \item (3 points) Draw some
  sort of diagram that illustrates the partial convergents
  of the following continued fractions:
  \begin{subprob}
    \item $[13,1,8,3]$
    \item $[1,1,1,1,1,1,1,1]$
    \item $[1,2,3,4,5,6,7,8]$
  \end{subprob}


  \item (5 points)
  If $c_n=p_n/q_n$ is the $n$th convergent of the continued fraction
  $[a_0,a_1,\ldots,a_n]$ and $a_0>0$, show that
  $$
    [a_n,a_{n-1},\ldots, a_1, a_0] = \frac{p_n}{p_{n-1}}
  $$
  and
  $$
    [a_n,a_{n-1},\ldots, a_2, a_1] = \frac{q_n}{q_{n-1}}.
  $$
  (Hint: In the first case, notice that
  $\frac{p_n}{p_{n-1}} = a_n + \frac{p_{n-2}}{p_{n-1}}
    = a_n + \frac{1}{\frac{p_{n-1}}{p_{n-2}}}.$)

  \item (4 points) There is a function~$j(\tau)$, denoted by {\tt
      ellj} in PARI, which takes as input a complex number~$\tau$ with
  positive imaginary part, and returns a complex number called the
  ``$j$-invariant of the associated elliptic curve''.  Suppose
  that~$\tau$ is {\em approximately} $-0.5+ 0.3281996289i$ and that you
  know that~$j=j(\tau)$ is a rational number.  Use continued fractions
  and PARI to compute a reasonable guess for the rational number
  $j=\mbox{\tt ellj}(\tau)$.  (Hint: In PARI $\sqrt{-1}$ is represented
  by {\tt I}.)

  \item
        (3 points) Evaluate each of the following infinite continued fractions:
  \begin{subprob}
    \item $[\overline{2,3}]$
    \item $[2,\overline{1,2,1}]$
    \item $[0,\overline{1,2,3}]$
  \end{subprob}

  \item
        (3 points) Determine the infinite continued fraction of each of the following
  numbers:
  \begin{subprob}
    \item $\sqrt{5}$
    \item $\ds\frac{1+\sqrt{13}}{2}$
    \item $\ds\frac{5+\sqrt{37}}{4}$
  \end{subprob}

  \newpage
  \item
  \begin{subprob}
    \item (4 points) For any positive integer~$n$, prove that
    $ \sqrt{n^2+1} = [n,\overline{2n}].$
    \item (2 points)
    Find a convergent to
    $\sqrt{5}$ that approximates $\sqrt{5}$
    to within four decimal places.
  \end{subprob}

  \item (4 points) A famous theorem of
  Hurwitz (1891) says that for any irrational
  number~$x$, there exists infinitely many rational numbers $a/b$
  such that
  $$\left| x - \frac{a}{b}\right| < \frac{1}{\sqrt{5}b^2}.$$
  Taking $x=\pi$, obtain three rational numbers that satisfy this
  inequality.

  \item (3 points) The continued fraction expansion of~$e$ is
  $$
    [2,1,2,1,1,4,1,1,6,1,1,8,1,1,\ldots].
  $$
  It is a theorem that the obvious pattern continues indefinitely.  Do
  you think that the continued fraction expansion of $e^2$ also exhibits
  a nice pattern?  If so, what do you think it is?

  \item
  \begin{subprob}
    \item (4 points) Show that there are infinitely many even integers~$n$
    with the property that both $n+1$ and $\frac{n}{2}+1$ are perfect
    squares.
    \item (3 points) Exhibit two such integers that are greater than $389$.
  \end{subprob}


  \item (7 points)
  A primitive Pythagorean triple is a triple $x, y, z$ of integers
  such that $x^2 + y^2 = z^2$.
  Prove that there exists infinitely many primitive Pythagorean
  triples $x, y, z$ in which~$x$ and~$y$ are consecutive integers.

\end{problems}

\section{Binary Quadratic Forms}
\begin{problems}
  \item (3 points)
  Which of the following numbers is a sum of two
  squares?  Express those that are as a sum of
  two squares.
  $$
    -389,\,\, 12345, \,\,91210,\,\,729,\,\, 1729,\,\, 68252
  $$

  \item
  \begin{subprob}
    \item (4 points) Write a PARI program that takes a positive integer~$n$
    as input and outputs a sequence {\tt [x,y,z,w]} of integers
    such that $x^2 + y^2 + z^2 + w^2=n$. (Hint: Your program does
    not have to be efficient.)
    \item (2 point) Write $2001$ as a sum of three squares.
  \end{subprob}


  \item (3 points) Find a positive integer that has a least three different
  representations as the sum of two squares, disregarding signs and
  the order of the summands.

  \item (5 points) Show that a natural number~$n$ is the sum of two
  integer squares if and only if it is the sum of two rational squares.

  \item (6 points) Mimic the proof of the main theorem of Lecture 21 to
  show that an odd prime~$p$ is of the form $8m+1$ or $8m+3$ if and only
  if it can be written as $p=x^2 + 2y^2$ for some choice of integers~$x$
  and~$y$.  (Hint: Use the formula for the quadratic residue symbol
  $\kr{-2}{p}$ from Lecture 13.)

  \item (4 points) A {\em triangular number} is a number that is the sum
  of the first $m$ integers for some positive integer~$m$.
  If~$n$ is a triangular number, show that all three of the
  integers $8n^2$, $8n^2+1$, and $8n^2+2$
  can be written as a sum of two squares.

  \item (3 points)
  Prove that of any four consecutive integers, at least one is not
  representable as a sum of two squares.

  \item (4 points) Show that $13x^2+36xy+25y^2$ and
  $58x^2+82xy+29y^2$ are each equivalent to the form
  $x^2+y^2$, then find integers~$x$ and~$y$ such that
  $13x^2+36xy+25y^2 = 389$.

  \item (4 points) What are the discriminants of the forms
  $199x^2-162xy+33y^2$ and $35x^2-96xy+66y^2$?  Are these
  forms equivalent?

  %\item (6 points) Use results about quadratic forms to show
  %that a prime~$p$ can be written as $p = x^2 + 3y^2$ if and only
  %if $p=3$ or $p\con 1\pmod{6}$.


\end{problems}

\section{Class Groups and Elliptic Curves}
\begin{problems}
  \item (10 points) For any negative discriminant~$D$, let $C_D$ denote
  the finite abelian group of equivalence classes of primitive positive
  definite quadratic forms of discriminant~$D$.  Use the PARI program
    {\tt forms.gp} from lecture 24 (download it from my web page) to
  compute representatives for $C_D$ and determine the structure of $C_D$
  as a produce of cyclic groups for each of the following five
  values of~$D$:
  $$
    D=-155, -231, -660, -12104, -10015.
  $$

  \item (6 points) Draw a beautiful graph of the set $E(\R)$ of real
  points on each of the following elliptic curves:
  \begin{subprob}
    \item $y^2 = x^3 - 1296x + 11664$,
    \item $y^2 + y = x^3 - x$,
    \item $y^2 + y = x^3 - x^2 - 10x - 20$.
  \end{subprob}

  \item (4 points) A rational solution
  to the equation $y^2-x^3=-2$ is $(3,5)$.
  Find a rational solution with $x\neq 3$
  by drawing the tangent line to $(3,5)$ and
  computing the third point of intersection.

\end{problems}



\section{Elliptic Curves I}
\begin{problems}

  \item (3 points) Consider the elliptic curve $y^2 + xy + y = x^3$ over $\Q$.
  Find a linear change of variables that transforms this curve into
  a curve of the form $Y^2 = X^3+a X + b$ for rational numbers~$a$ and~$b$.

  \item (6 points) Let~$E$ be the elliptic curve over the finite
  field $K=\Z/5\Z$ defined by the equation
  $$
    y^2 = x^3 + x +1.
  $$
  \begin{subprob}
    \item List all~$9$ elements of~$E(K)$.
    \item What is the structure of the group
    $E(K)$, as a product of cyclic groups?
  \end{subprob}

  \item (8 points) Let~$E$ be an elliptic curve over~$\Q$.
  Define a binary operation $\boxplus$ on~$E$ as follows:
  $$P \boxplus Q = -(P+Q).$$
  Thus the $\boxplus$ of~$P$ and~$Q$ is the third
  point of intersection of the line through~$P$ and~$Q$
  with~$E$.
  \begin{subprob}
    \item Lists the axiom(s) of a group that fail for
    $E(\R)$ equipped with this binary operation.
    (The group axioms are ``identity'', ``inverses'', and ``associativity''.)
    \item Under
    what conditions on $E(\Q)$ does this binary operation
    define a group structure on $E(\Q)$? (E.g., when $E(\Q)=\{\mathcal{O}\}$
    this binary operation does define a group.)
  \end{subprob}


  \item (6 points) Let $g(t)$ be a quartic polynomial with distinct (complex) roots,
  and let~$\alpha$ be a root of $g(t)$.  Let $\beta\neq 0$ be any number.
  \begin{subprob}
    \item Prove that the equations
    $$
      x = \frac{\beta}{t-\alpha},
      \qquad
      y = x^2 u = \frac{\beta^2 u}{(t-\alpha)^2}
    $$
    give an ``algebraic transformation'' between the curve $u^2=g(t)$
    and the curve $y^2=f(x)$, where $f(x)$ is the cubic polynomial
    $$
      f(x) = g'(\alpha) \beta x^3 + \frac{1}{2} g''(\alpha) \beta^2 x^2 + \frac{1}{6}g'''(\alpha) \beta^3 x + \frac{1}{24} g''''(\alpha)\beta^4.
    $$
    \item Prove that if~$g$ has distinct (complex) roots, then~$f$ also
    has distinct roots, and so $u^2 = g(t)$ is an elliptic curve.

  \end{subprob}



  \item (8 points)
  In this problem you will finally find out exactly
  why elliptic curves are called ``elliptic curves''!
  Let $0<\beta\leq \alpha$, and let $C$ be the ellipse
  $$\frac{x^2}{\alpha^2} + \frac{y^2}{\beta^2} = 1.$$
  \begin{subprob}
    \item Prove that the arc length of~$C$ is given
    by the integral
    $$
      4\alpha\int_{0}^{\pi/2} \sqrt{1-k^2\sin^2\theta} d\theta
    $$
    for an appropriate choice of constant~$k$ depending on~$\alpha$
    and~$\beta$.

    \item
    Check your value for~$k$ in (i) by verifying that when $\alpha=\beta$,
    the integral  yields the correct value for the arc length of a circle.

    \item Prove that the integral in (i) is also equal to
    $$
      4\alpha\int_0^1 \sqrt{\frac{1-k^2t^2}{1-t^2}} dt
      = 4\alpha\int_0^1\frac{1-k^2t^2}{\sqrt{(1-t^2)(1-k^2t^2)}}dt.
    $$
    \item Prove that if the ellipse $E$ is not a circle, then the equation
    $$
      u^2 = (1-t^2)(1-k^2t^2)
    $$
    defines an elliptic curve (cf. the previous exercise).  Hence
    the problem of determining the arc length of an ellipse comes down
    to evaluating the integral
    $$
      \int_0^1 \frac{1-k^2t^2}{u} dt
    $$
    on the ``elliptic'' curve $u^2=(1-t^2)(1-k^2t^2)$.

  \end{subprob}

  \item (8 points)
  Suppose that $P=(x,y)$ is a point on the cubic curve
  $$
    y^2 = x^3 + ax + b.
  $$
  \begin{subprob}
    \item Verify that the~$x$ coordinate of the point $2P$
    is given by the duplication formula
    $$
      x(2P) = \frac{x^4 - 2ax^2 -8bx +a^2}{4y^2}.
    $$
    \item Derive a similar formula for the $y$ coordinate of $2P$
    in terms of~$x$ and~$y$.
    \item Find a polynomial in~$x$ whose roots are the $x$-coordinates
    of the points $P=(x,y)$ satisfying $3P=\mathcal{O}$. [Hint: The
        relation $3P=\mathcal{O}$ can also be written $2P=-P$.]
    \item For the particular curve $y^2 = x^3 + 1$, solve the equation
    in (iii) to find all of the points satisfying $3P=\mathcal{O}$.
    Note that you will have to use complex numbers.
  \end{subprob}
\end{problems}

\section{Elliptic Curves II}
\begin{problems}
  \item (10 points) Let $\Phi$ be the set of the $15$ possible groups of
  the form $E(\Q)_{\tor}$ for~$E$ an elliptic curve over~$\Q$ (see
  Lecture~27).  For each group $G\in\Phi$, if possible, find a finite
  field $k = \Z/p\Z$ and an elliptic curve~$E$ over~$k$ such that $E(k)
    \ncisom G$.  (Hint: It is a fact that
  $|p + 1 - \#E(\Z/p\Z))| \leq 2\sqrt{p}$, so you only have to try
  finitely many~$p$ to show that a group~$G$ does not occur as the group
  of points on an elliptic curve over a finite field.)

  %\item (5 points) Demonstrate how to use the Lutz-Nagell theorem
  %(Lecture~27) to compute the torsion subgroup of the elliptic
  %curve defined by the equation
  %$$y^2 +xy = x^3 -8369487776175x + 9319575518172005625.$$

  \item (6 points) Many number theorists, such as myself one week ago,
  incorrectly think that Lutz-Nagell works well in practice.
  Describe the steps you {\em would}
  take if you were to use the Lutz-Nagell theorem
  (Lecture~27) to compute the torsion subgroup of the elliptic
  curve~$E$ defined by the equation
  $$y^2 +xy = x^3 -8369487776175x + 9319575518172005625,$$
  then tell me why it would be {\em very} time consuming to actually
  carry these steps out.  Find the torsion subgroup of~$E$
  using the {\tt elltors} command in PARI.  Does {\tt elltors}
  use the Lutz-Nagell algorithm by default?

  \item (6 points) Let $E$ be the elliptic curve
  defined by the equation $y^2 = x^3 +1$.
  \begin{subprob}
    \item For each prime $p$ with $5\leq p<30$, describe the group
    of points on this curve having coordinates in the finite field
    $\Z/p\Z$. (You can just give the order of each group.)
    \item For each prime in (i), let $N_p$ be the number of
    points in the group.  (Don't forget the point infinity.)
    For the set of primes satisfying $p\con 2\pmod{3}$,
    can you see a pattern for the values of $N_p$?
    Make a general conjecture for the value of $N_p$
    when $p\con 2\pmod{3}$.
    \item Prove your conjecture.
  \end{subprob}

  \item (6 points)
  Let~$p$ be a prime and let $E$ be the elliptic curve
  defined by the equation $y^2 = x^3 +px$.
  Use Lutz-Nagel to find all points of finite order in $E(\Q)$.

  \item (4 points)
  \begin{subprob}
    \item
    Let~$E$ be an elliptic curve over the real numbers~$\R$.
    Prove that $E(\R)$ is not a finitely generated abelian group.
    \item
    Let~$E$ be an elliptic curve over a finite field $k=\Z/p\Z$.
    Prove that $E(k)$ is a finitely generated abelian group.
  \end{subprob}





\end{problems}

\section{Elliptic Curves III}

\begin{problems}
  \item (5 points) Make up a simple example that illustrates how to use
  the ElGamal elliptic curve cryptosystem (see Lecture 29).  You
  may mention Nikita and Michael if you wish.  Be very clear about what
  you are illustrating so that the grader can effortlessly understand your
  example.

  \item (5 points) Make up an example that illustrates an interesting
  aspect of the Pollard $(p-1)$ factorization method.

  \item (5 points) Make up an example that illustrates something that you
  consider an interesting aspect of Lenstra's elliptic curves
  factorization method.

  \item (10 points) Let~$R$ be a ring.  We say that Fermat's last
  theorem is false in~$R$ if there exists $x,y,z\in R$ and $n\in
    \mathbb{Z}$ with $n\geq 3$ such that $x^n + y^n = z^n$ and $xyz\neq
    0$.  For which prime numbers~$p$ is Fermat's last theorem false in the
  ring $\mathbb{Z}/p\mathbb{Z}$?\footnote{This problem was on the
    dreaded Harvard graduate school qualifying examination this year.
    Every one of the students who took that exam got this problem right.}




\end{problems}


\end{document}

